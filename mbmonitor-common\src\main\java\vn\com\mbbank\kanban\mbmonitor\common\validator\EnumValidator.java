package vn.com.mbbank.kanban.mbmonitor.common.validator;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Arrays;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.ValidEnum;

/**
 * Custom annotation to validate that the value of a field belongs to a specific Enum.
 *
 * @see EnumValidator
 *
 * <AUTHOR>
 * @created_date 09/26/2024
 */
public class EnumValidator implements ConstraintValidator<ValidEnum, Object> {

  private Class<? extends Enum<?>> enumClass;

  @Override
  public void initialize(ValidEnum constraintAnnotation) {
    this.enumClass = constraintAnnotation.enumClass();
  }

  @Override
  public boolean isValid(Object value, ConstraintValidatorContext context) {
    if (value == null) {
      return false;
    }

    return Arrays.stream(enumClass.getEnumConstants())
        .anyMatch(e -> e.name().equals(value.toString()));
  }
}
