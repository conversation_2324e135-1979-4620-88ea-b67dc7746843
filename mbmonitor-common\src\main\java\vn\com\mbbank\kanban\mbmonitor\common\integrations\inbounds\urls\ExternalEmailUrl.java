package vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls;

import vn.com.mbbank.kanban.mbmonitor.common.configs.KanbanPropertyConfigUtils;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.BaseUrl;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 1/16/2025
 */
public class ExternalEmailUrl extends BaseUrl {
  /**
   * ExternalEmailUrl.
   *
   * @param path Path
   */
  public ExternalEmailUrl(String path) {
    super(path);
  }

  /**
   * Load url from properties.
   *
   * @param baseApi baseApi
   * @return full url
   */
  public static String getUrl(String baseApi) {
    return KanbanPropertyConfigUtils.getProperty("monitor.external.email.url",
        getBaseUrl() + "api/external-email" + baseApi);
  }

  public static final String BASE_URL = "";
  public static final String VERSION = "/v1";

  public static final String EMAIL_URL = BASE_URL + VERSION + "/emails";
}
