package vn.com.mbbank.kanban.mbmonitor.external.execution.services.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseConnectionEntity;
import vn.com.mbbank.kanban.mbmonitor.external.execution.repositories.DatabaseConnectionRepository;
import vn.com.mbbank.kanban.mbmonitor.external.execution.services.DatabaseConnectionService;


/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/5/2024
 */
@Service
@RequiredArgsConstructor
public class DatabaseConnectionServiceImpl extends BaseServiceImpl<DatabaseConnectionEntity, Long>
    implements DatabaseConnectionService {

  private final DatabaseConnectionRepository databaseConnectionRepository;

  @Override
  protected JpaCommonRepository<DatabaseConnectionEntity, Long> getRepository() {
    return databaseConnectionRepository;
  }

}