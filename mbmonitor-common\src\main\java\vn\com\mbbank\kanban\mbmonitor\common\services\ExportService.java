package vn.com.mbbank.kanban.mbmonitor.common.services;

import java.io.IOException;
import java.util.List;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportFileDto;
import vn.com.mbbank.kanban.mbmonitor.common.utils.export.FileExporter;
import vn.com.mbbank.kanban.mbmonitor.common.utils.export.factory.FileExporterFactory;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 09/26/2024
 */
public class ExportService {
  private ExportService() {
  }

  /**
   * Exports data to a file format specified in the {@code exportFileRequest}.
   * file exporter based on the type of file requested, and then exports
   * the provided data accordingly.
   *
   * @param data          the list of objects to be exported. This data is transformed and written into the file.
   * @param exportFileDto the request object containing the details for the export,
   *                      including the type of file and a list of attribute information.
   * @return StreamingResponseBody A stream of the exported file that can be sent directly to the client.
   */
  public static StreamingResponseBody exportData(List<?> data, ExportFileDto exportFileDto)
      throws IOException, BusinessException {
    FileExporter exporter = FileExporterFactory.getFileExporter(exportFileDto.getTypeFile());
    return exporter.export(data, exportFileDto);
  }
}
