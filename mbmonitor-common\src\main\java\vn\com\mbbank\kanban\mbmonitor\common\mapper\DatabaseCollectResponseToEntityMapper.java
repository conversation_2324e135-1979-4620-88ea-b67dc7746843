package vn.com.mbbank.kanban.mbmonitor.common.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.DatabaseCollectResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseCollectEntity;


/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/18/2024
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DatabaseCollectResponseToEntityMapper extends
    KanbanBaseMapper<DatabaseCollectResponse, DatabaseCollectEntity> {
  DatabaseCollectResponseToEntityMapper INSTANCE =
      Mappers.getMapper(DatabaseCollectResponseToEntityMapper.class);
}
