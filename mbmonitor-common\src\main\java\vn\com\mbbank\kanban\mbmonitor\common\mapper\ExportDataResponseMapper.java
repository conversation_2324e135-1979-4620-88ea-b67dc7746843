package vn.com.mbbank.kanban.mbmonitor.common.mapper;


import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.ExportDataResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExportDataEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/03/2024
 */

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ExportDataResponseMapper extends KanbanBaseMapper<ExportDataResponse, ExportDataEntity> {
  ExportDataResponseMapper INSTANCE = Mappers.getMapper(ExportDataResponseMapper.class);
}
