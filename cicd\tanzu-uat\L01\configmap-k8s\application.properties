server.port=9007
server.servlet.context-path=/api/external-execution
#Metric
management.server.port=9007
management.endpoint.shutdown.enabled=true
management.endpoints.web.exposure.include=health,metrics,prometheus
management.endpoint.health.show-details=always
spring.jpa.properties.hibernate.format_sql=true
management.endpoint.health.probes.enabled=true
management.health.livenessState.enabled=true
management.health.readinessState.enabled=true
management.endpoint.health.group.liveness.include=livenessState,ping,diskSpace
management.endpoint.health.group.readiness.include=readinessState,ping
#database config
spring.datasource.url=********************************************
spring.datasource.hikari.username=mbmonitor
spring.datasource.hikari.password=mbmonitor
spring.datasource.hikari.minimumIdle=5
spring.datasource.hikari.maximumPoolSize=200
spring.datasource.hikari.idleTimeout=30000
spring.datasource.hikari.poolName=HikariCP
spring.datasource.hikari.maxLifetime=2000000
spring.datasource.hikari.connectionTimeout=30000
spring.jackson.serialization.fail-on-empty-beans=false
spring.datasource.driver-class-name=oracle.jdbc.driver.OracleDriver
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.properties.hibernate.jdbc.batch_size=600
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
spring.jpa.properties.hibernate.cache.use_second_level_cache=false
server.tomcat.relaxed-query-chars=|,{,},[,]
#Jackson config
spring.jackson.default-property-inclusion=non_null
#config encript
jasypt.encryptor.bean=encryptorBean
jasypt.encryptor.algorithm=PBEWithMD5AndDES
jasypt.encryptor.keyobtentioniterations=1000
jasypt.encryptor.poolsize=1
jasypt.encryptor.providername=SunJCE
jasypt.encryptor.saltgeneratorclassname=org.jasypt.salt.RandomSaltGenerator
jasypt.encryptor.stringoutputtype=base64
#keycloak
kanban.keycloak.client-id=mbmonitor-server
kanban.keycloak.client-secret=QXI1GnXNGFncak5LZAMSTe5sHvQU6rgn
#kanban.keycloak.client-secret=ENC(B59eeKpVbBbbRuK3CSjSeNls8MUH/sskGNtvEXlWU1B+vfYj69LcKn6V5WLrj7Ny)
kanban.keycloak.introspect-uri=https://keycloak-internal-uat.mbbank.com.vn/auth/realms/internal/protocol/openid-connect/token/introspect
#redis config
kanban.redis.url=**************:6379
kanban.redis.password=Kanban@2024
#kafka config
kanban.kafka.bootstrap-server=**************:9093,**************:9093,**************:9093,**************:9093,**************:9093,**************:9093
kanban.kafka.producer.timeout=30000
kanban.kafka.consumer.auto-commit=true
kanban.kafka.consumer.group-id=123
kanban.kafka.producer.max-request-size=1000000
kanban.kafka.topic.jobs=APP_MBMONITOR_JOBS
kanban.kafka.username=user04
kanban.kafka.password=AXzwKvfruEaQNZmW
#kanban.kafka.password=ENC(XZRUNJRcdsbDXxMI248VSGH1xGVfVrWIq4tNCE9h8k0=)
kanban.kafka.key=emailConfigId
kanban.general.white-list.origin=http://localhost:8400,http://localhost:9000,http://localhost:9990,http://localhost:9007,https://mbmonitor.tanzu-uat.mbbank.com.vn
#keycloak centrailized
kanban.authentication.keycloak.centrailized.url=http://**********:8831/auth/realms/ms-core/protocol/openid-connect/token
kanban.authentication.keycloak.centrailized.username=mbmonitor_user
kanban.authentication.keycloak.centrailized.password=fb7bfb60-f5e3-4f8d-b4ad-3f22249b1fa0
kanban.authentication.keycloak.centrailized.public-key=-----BEGIN PUBLIC KEY-----MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3sROnOw4rj3ainkFaVHf05ha3yt4njOxJzljdebkJJckOXj1wag47nGSukVcsytXuhq8QrM6z8KpcSsAXNYNBgM7i7/X+ZxTf/b5cDKDpwiOzcWbMazii5/8ljIII9Z2sBOXQAf3Ev2NIgUlXBOuwhkQ9gFWmni+GvLnQxNVdJ/16ABelL1LCAbnDlX0VrhLnMQUihjf0Npy+jv5vm20DMzpk5rTo01XTTirrUmiaE76qILbwILqZDVwd4RlnGWD37N9qbjYTZ6AQ3s/yCaEgeJ0z4kzqP5G2oXvll9joV5P/Jub7WGRh9alj+VfhYp9MusTzlLJmqb8uvY3w7E67QIDAQAB-----END PUBLIC KEY-----
kanban.authentication.keycloak.centrailized.enable=true
# app monitor
mbmonitor.url=https://mbmonitor.tanzu-uat.mbbank.com.vn/
mbmonitor.internal.url=http://mbmonitor-gateway-service.uat-mbmonitor.svc.cluster.local:9000/
#monitor.internal.server.url=https://mbmonitor.tanzu-uat.mbbank.com.vn/api
# setting hikari query sql
mbmonitor.database.hikari.minimumIdle=5
mbmonitor.database.hikari.maximumPoolSize=20
mbmonitor.database.hikari.idleTimeout=30000
mbmonitor.database.hikari.poolName=monitorCP
mbmonitor.database.hikari.maxLifetime=2000000
mbmonitor.database.hikari.connectionTimeout=30000
#trust domain
mbmonitor.trusted.hostnames=http://localhost:9000
mbmonitor.teams.proxy.ip=**********
mbmonitor.teams.proxy.port=8080
mbmonitor.execution.timeout=5
#500KB
mbmonitor.execution.maxOutputSize=524288

