package vn.com.mbbank.kanban.mbmonitor.common.mapper.log;

import java.util.ArrayList;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.AutoTriggerActionConfigLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.ConfigDependencyLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/6/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AutoTriggerActionConfigLogModelMapper extends
        KanbanBaseMapper<AutoTriggerActionConfigLogModel, AutoTriggerActionConfigEntity> {
  AutoTriggerActionConfigLogModelMapper INSTANCE = Mappers.getMapper(AutoTriggerActionConfigLogModelMapper.class);

  /**
   * map AutoTriggerActionConfigEntity to AutoTriggerActionConfigLogModel.
   *
   * @param config                        AlertGroupConfigEntity.
   * @param serviceDependencies           services
   * @param serviceWithAllAppDependencies services with all app
   * @param applicationDependencies       applications
   * @param executions                    executions
   * @return AutoTriggerActionConfigLogModel
   */
  default AutoTriggerActionConfigLogModel map(AutoTriggerActionConfigEntity config,
                                              List<ServiceEntity> serviceDependencies,
                                              List<ServiceEntity> serviceWithAllAppDependencies,
                                              List<ApplicationEntity> applicationDependencies,
                                              List<ExecutionEntity> executions) {
    var serviceNames = new ArrayList<String>();
    var applicationNames = new ArrayList<String>();
    var executionNames = new ArrayList<String>();
    for (ServiceEntity service : serviceDependencies) {
      serviceNames.add(service.getName());
    }
    for (ServiceEntity service : serviceWithAllAppDependencies) {
      serviceNames.add(service.getName() + " (All application)");
    }
    for (ApplicationEntity application : applicationDependencies) {
      applicationNames.add(application.getName());
    }
    for (ExecutionEntity execution : executions) {
      executionNames.add(execution.getName());
    }

    var dependency = ConfigDependencyLogModel.builder().services(serviceNames).applications(applicationNames).build();

    var res = AutoTriggerActionConfigLogModel.builder()
            .name(config.getName())
            .description(config.getDescription())
            .dependency(dependency)
            .condition(config.getRuleGroup().toString())
            .timeSinceLastTrigger(config.getTimeSinceLastTrigger())
            .active(config.getActive())
            .executions(executionNames);
    return res.build();
  }
}
