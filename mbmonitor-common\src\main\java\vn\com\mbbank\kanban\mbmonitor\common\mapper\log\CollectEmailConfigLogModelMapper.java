package vn.com.mbbank.kanban.mbmonitor.common.mapper.log;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.CollectEmailConfigLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CollectEmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CollectEmailConfigLogModelMapper extends
    KanbanBaseMapper<CollectEmailConfigLogModel, CollectEmailConfigEntity> {
  CollectEmailConfigLogModelMapper INSTANCE = Mappers.getMapper(CollectEmailConfigLogModelMapper.class);

  /**
   * map  CollectEmailConfigLogModel.
   *
   * @param collectEmailConfig CollectEmailConfigEntity.
   * @param emailConfig        EmailConfigEntity
   * @param service            ServiceEntity
   * @param application        ApplicationEntity
   * @param priority           AlertPriorityConfigEntity
   * @return CollectEmailConfigLogModel
   */
  default CollectEmailConfigLogModel map(CollectEmailConfigEntity collectEmailConfig, EmailConfigEntity emailConfig,
                                         ServiceEntity service, ApplicationEntity application,
                                         AlertPriorityConfigEntity priority) {
    var res = new CollectEmailConfigLogModel();
    res.setName(collectEmailConfig.getName());
    res.setDescription(collectEmailConfig.getDescription());
    res.setEmail(emailConfig.getEmail());
    res.setIntervalTime(collectEmailConfig.getIntervalTime());
    res.setCondition(collectEmailConfig.getRuleGroup().toString());
    res.setService(service.getName());
    res.setApplication(application.getName());
    res.setPriorityConfig(priority.getName());
    res.setActive(collectEmailConfig.isActive());
    return res;
  }
}
