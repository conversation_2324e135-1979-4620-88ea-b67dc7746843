package vn.com.mbbank.kanban.mbmonitor.common.dtos.responses;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * Model use for SqlExecutionResponse.
 */
@Getter
@Setter
@Builder
public class SqlExecutionResponse {

  /**
   * Transforms a list of SQL data mappings into a structured response.
   *
   * @param sqlDataMappings a list of {@link SqlDataMapping} objects containing column data.
   * @return a {@link QuerySqlExecuteResponse} with:
   *         - {@code result}: a list of maps representing rows of column-value pairs.
   *         - {@code total}: the total record count from the "TOTAL_RECORDS" column, or {@code null}.
   */
  public static QuerySqlExecuteResponse transformSqlData(
      List<SqlDataMapping> sqlDataMappings) {
    List<Map<String, Object>> resultData = new ArrayList<>();
    for (SqlDataMapping sqlDataMapping : sqlDataMappings) {
      Map<String, Object> rowData = new HashMap<>();
      for (SqlMappingColumnData columnData : sqlDataMapping.getListSqlMappingColumnDatas()) {
        String column = columnData.getColumn();
        String value = columnData.getValue();
        rowData.put(column, value);
      }
      resultData.add(rowData);
    }
    return QuerySqlExecuteResponse.builder()
        .result(resultData)
        .build();
  }


  /**
   * Transforms a list of SQL data mappings into a structured response.
   *
   * @param sqlExecutionResponse sqlExecutionResponse.
   * @return a {@link QuerySqlExecuteResponse} with:
   *         - {@code result}: a list of maps representing rows of column-value pairs.
   *         - {@code total}: the total record count from the "TOTAL_RECORDS" column, or {@code null}.
   */
  public static QuerySqlExecuteResponse transformSqlData(
      SqlExecutionResponse sqlExecutionResponse) {
    List<Map<String, Object>> resultData = new ArrayList<>();
    for (SqlDataMapping sqlDataMapping : sqlExecutionResponse.getListDataMappings()) {
      Map<String, Object> rowData = new HashMap<>();
      for (SqlMappingColumnData columnData : sqlDataMapping.getListSqlMappingColumnDatas()) {
        String column = columnData.getColumn();
        String value = columnData.getValue();
        rowData.put(column, value);
      }
      resultData.add(rowData);
    }
    return QuerySqlExecuteResponse.builder()
        .result(resultData)
        .columns(sqlExecutionResponse.getListColumns())
        .isNonQuery(sqlExecutionResponse.isNonQuery())
        .total(sqlExecutionResponse.getTotal())
        .build();
  }


  /**
   * Mapping sql column and value.
   */
  @Getter
  @Setter
  @Builder
  public static class SqlMappingColumnData {
    private String column;
    private String value;
  }

  /**
   * Mapping all Column and Value to a Row.
   */
  @Getter
  @Setter
  @Builder
  public static class SqlDataMapping {
    private List<SqlMappingColumnData> listSqlMappingColumnDatas;
  }

  private List<SqlDataMapping> listDataMappings;
  List<String> listColumns;

  @JsonProperty(value = "isNonQuery")
  private boolean isNonQuery;

  private Integer total;
}
