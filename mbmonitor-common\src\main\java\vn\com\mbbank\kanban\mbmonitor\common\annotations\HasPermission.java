package vn.com.mbbank.kanban.mbmonitor.common.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/4/2024
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface HasPermission {
  /**
   * value permission.
   *
   * @return AclPermission
   */
  AclPermission[] value() default {};

  /**
   * Flag check any or every all permission.
   *
   * @return true or false
   */
  boolean mappingAll() default false;
}
