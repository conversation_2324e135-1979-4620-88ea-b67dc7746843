package vn.com.mbbank.kanban.mbmonitor.common.utils;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import vn.com.mbbank.kanban.core.common.ResponseData;

class ResponseUtilsTest {
  @Test
  void constructor() {
    new ResponseUtils();
  }

  @Test
  void createSuccessResponse_success() {
    Object object = new Object();
    ResponseData<Object> result = ResponseUtils.success(object);
    Assertions.assertEquals(object, result.getData());
  }

  @Test
  void createErrorResponse_success() {
    ResponseData<?> result = ResponseUtils.error(0, "errorCode", "errorDescription");
    Assertions.assertNotNull(result);
  }
}