package vn.com.mbbank.kanban.mbmonitor.common.constants;

import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.notifications.ProseMirrorDocBuilder;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.notifications.ProseMirrorDocModel;

/**
 * Utility class for generating ProseMirror-formatted notification messages.
 */
public class ProseMirrorMessageConstants {

  /**
   * Generates a message for a submitted alert request.
   *
   * @param user    the username who submitted the request
   * @param linkUrl the link to the request
   * @return the message in ProseMirror JSON format
   */
  public static String submittedAlertRequest(String user, String linkUrl) {
    ProseMirrorDocModel model = ProseMirrorDocBuilder.create()
        .addParagraph(
            ProseMirrorDocBuilder.text("User {0} has created a request ", user),
            ProseMirrorDocBuilder.link("Link", linkUrl),
            ProseMirrorDocBuilder.text(" to set up a monitoring alert.")
        )
        .build();
    return ProseMirrorDocBuilder.toJson(model);
  }

  /**
   * Generates a message for an approved alert request.
   *
   * @param user    the username who approved the request
   * @param linkUrl the link to the request
   * @return the message in ProseMirror JSON format
   */
  public static String approvedAlertRequest(String user, String linkUrl) {
    ProseMirrorDocModel model = ProseMirrorDocBuilder.create()
        .addParagraph(
            ProseMirrorDocBuilder.text("User {0} has approved request ", user),
            ProseMirrorDocBuilder.link("Link", linkUrl),
            ProseMirrorDocBuilder.text(" to set up alerts.")
        )
        .build();
    return ProseMirrorDocBuilder.toJson(model);
  }

  /**
   * Generates a message for a rejected alert request.
   *
   * @param user    the username who rejected the request
   * @param linkUrl the link to the request
   * @param reason  the reason for rejection
   * @return the message in ProseMirror JSON format
   */
  public static String rejectedAlertRequest(String user, String linkUrl, String reason) {
    ProseMirrorDocModel model = ProseMirrorDocBuilder.create()
        .addParagraph(
            ProseMirrorDocBuilder.text("User {0} has rejected request ", user),
            ProseMirrorDocBuilder.link("Link", linkUrl),
            ProseMirrorDocBuilder.text(" alerts by reason {0}.", reason)
        )
        .build();
    return ProseMirrorDocBuilder.toJson(model);
  }

}
