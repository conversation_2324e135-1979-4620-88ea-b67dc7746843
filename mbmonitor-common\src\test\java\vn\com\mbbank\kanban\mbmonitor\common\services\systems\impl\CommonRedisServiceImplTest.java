package vn.com.mbbank.kanban.mbmonitor.common.services.systems.impl;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anySet;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.beans.factory.BeanCreationException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import vn.com.mbbank.kanban.core.configs.redis.RedisAdapter;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 1/15/2025
 */
class CommonRedisServiceImplTest {
  @Mock
  RedisAdapter redisAdapter;
  @InjectMocks
  @Spy
  CommonRedisServiceImpl commonRedisServiceImpl;

  @Mock
  private ValueOperations<Object, Object> valueOperations;

  @Mock
  private RedisTemplate<Object, Object> redisTemplate;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void get_success() {
    when(redisAdapter.getRedisTemplate()).thenReturn(redisTemplate);
    when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    when(valueOperations.get(anyString())).thenReturn("abc");
    Object result = commonRedisServiceImpl.get("key", String.class);
    Assertions.assertEquals("abc", result);
  }

  @Test
  void get_disconnected_success() {
    when(valueOperations.get(anyString())).thenReturn("abc");
    Object result = commonRedisServiceImpl.get("key", String.class);
    Assertions.assertEquals(null, result);
  }

  @Test
  void get_with_default_value_value_get_null() {
    when(redisAdapter.getRedisTemplate()).thenReturn(redisTemplate);
    when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    when(valueOperations.get(anyString())).thenReturn(null);
    Object result = commonRedisServiceImpl.get("key", "abc", String.class);
    Assertions.assertEquals("abc", result);
  }

  @Test
  void get_with_default_value_value_exception() {
    when(redisAdapter.getRedisTemplate()).thenReturn(redisTemplate);
    when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    when(commonRedisServiceImpl.get(any(), any())).thenThrow(new RuntimeException("abc"));
    Object result = commonRedisServiceImpl.get("key", "abc", String.class);
    Assertions.assertEquals("abc", result);
  }

  @Test
  void get_with_default_value_success() {
    when(redisAdapter.getRedisTemplate()).thenReturn(redisTemplate);
    when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    when(valueOperations.get(anyString())).thenReturn("abc");
    Object result = commonRedisServiceImpl.get("key", "abc", String.class);
    Assertions.assertEquals("abc", result);
  }

  @Test
  void get_with_default_value_connected_false() {
    Object result = commonRedisServiceImpl.get("key", "abc", String.class);
    Assertions.assertEquals("abc", result);
  }

  @Test
  void getByPrefix_success() {
    when(redisAdapter.getRedisTemplate()).thenReturn(redisTemplate);
    when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    when(valueOperations.get(anyString())).thenReturn("abc");
    when(redisTemplate.keys(anyString())).thenReturn(Set.of("abc"));

    List<String> result = commonRedisServiceImpl.getByPrefix("prefix", String.class);
    Assertions.assertEquals(List.of("abc"), result);
  }


  @Test
  void getByPrefix_redis_connected_false() {
    List<String> result = commonRedisServiceImpl.getByPrefix("prefix", String.class);
    Assertions.assertEquals(0, result.size());
  }

  @Test
  void save_success() {
    when(redisAdapter.getRedisTemplate()).thenReturn(redisTemplate);
    when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    doNothing().when(valueOperations).set(any(), any());
    commonRedisServiceImpl.save("key", "data");
  }

  @Test
  void save_connected_false() {
    commonRedisServiceImpl.save("key", "data");
  }

  @Test
  void save_with_timeout_success() {
    when(redisAdapter.getRedisTemplate()).thenReturn(redisTemplate);
    when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    doNothing().when(valueOperations).set(any(), any(), any());
    commonRedisServiceImpl.save("key", "data", 12L);
  }

  @Test
  void save_with_timeout_false() {
    commonRedisServiceImpl.save("key", "data", 12L);
  }

  @Test
  void delete_success() {
    when(redisAdapter.getRedisTemplate()).thenReturn(redisTemplate);
    when(redisTemplate.delete(any())).thenReturn(1L);
    var result = commonRedisServiceImpl.delete("key");
    Assertions.assertEquals(false, result);
  }

  @Test
  void delete_connected_false() {
    var result = commonRedisServiceImpl.delete("key");
  }

  @Test
  void isExistsByKey_success() {
    when(redisAdapter.getRedisTemplate()).thenReturn(redisTemplate);
    when(redisTemplate.hasKey(any())).thenReturn(true);
    var result = commonRedisServiceImpl.isExistsByKey("key");
    Assertions.assertEquals(true, result);
  }

  @Test
  void isExistsByKey_false() {
    when(redisAdapter.getRedisTemplate()).thenReturn(redisTemplate);
    when(redisTemplate.hasKey(any())).thenReturn(null);
    var result = commonRedisServiceImpl.isExistsByKey("key");
    Assertions.assertEquals(false, result);
  }

  @Test
  void isExistsByKey_connected_false() {
    var result = commonRedisServiceImpl.isExistsByKey("key");
    Assertions.assertEquals(false, result);
  }

  @Test
  void isConnected_true() {
    when(redisAdapter.getRedisTemplate()).thenReturn(redisTemplate);
    var result = commonRedisServiceImpl.isConnected();
    Assertions.assertEquals(true, result);
  }

  @Test
  void isConnected_false() {
    var result = commonRedisServiceImpl.isConnected();
    Assertions.assertEquals(false, result);
  }

  @Test
  void isConnected_bean_not_config() {
    when(redisAdapter.getRedisTemplate()).thenThrow(new BeanCreationException(""));
    var result = commonRedisServiceImpl.isConnected();
    Assertions.assertEquals(false, result);
  }

  @Test
  void deleteByPrefix_Empty_keys() {
    when(redisAdapter.getRedisTemplate()).thenReturn(redisTemplate);
    when(redisTemplate.delete(anySet())).thenReturn(1L);

    var result = commonRedisServiceImpl.deleteByPrefixKey("key");
    Assertions.assertEquals(0, result);
  }

  @Test
  void deleteByPrefix_success() {
    when(redisAdapter.getRedisTemplate()).thenReturn(redisTemplate);
    when(redisTemplate.delete(anySet())).thenReturn(1L);
    when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    when(valueOperations.get(anyString())).thenReturn("abc");
    when(redisTemplate.keys(anyString())).thenReturn(Set.of("abc"));
    var result = commonRedisServiceImpl.deleteByPrefixKey("key");
    Assertions.assertEquals(1, result);
  }

  @Test
  void deleteByPrefix_connected_false() {
    var result = commonRedisServiceImpl.deleteByPrefixKey("key");
  }


}
