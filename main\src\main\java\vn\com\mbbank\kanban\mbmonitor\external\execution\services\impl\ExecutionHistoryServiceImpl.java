package vn.com.mbbank.kanban.mbmonitor.external.execution.services.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionHistoryEntity;
import vn.com.mbbank.kanban.mbmonitor.external.execution.repositories.ExecutionHistoryRepository;
import vn.com.mbbank.kanban.mbmonitor.external.execution.services.ExecutionHistoryService;

@Service
@RequiredArgsConstructor
public class ExecutionHistoryServiceImpl extends BaseServiceImpl<ExecutionHistoryEntity, String>
    implements ExecutionHistoryService {
  private final ExecutionHistoryRepository executionHistoryRepository;

  @Override
  protected JpaCommonRepository<ExecutionHistoryEntity, String> getRepository() {
    return executionHistoryRepository;
  }

}
