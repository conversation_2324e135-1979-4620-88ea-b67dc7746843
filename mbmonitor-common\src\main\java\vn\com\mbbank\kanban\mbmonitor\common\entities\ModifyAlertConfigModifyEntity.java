package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;

@Entity
@Data
@Table(name = TableName.MODIFY_ALERT_CONFIG_MODIFY)
@EqualsAndHashCode(callSuper = true)
public class ModifyAlertConfigModifyEntity extends BaseEntity<Long> {
  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "MODIFY_ALERT_CONFIG_MODIFY_SEQ",
      sequenceName = "MODIFY_ALERT_CONFIG_MODIFY_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "MODIFY_ALERT_CONFIG_MODIFY_SEQ")
  private Long id;

  @Column(name = "MODIFY_ALERT_CONFIG_ID")
  private Long modifyAlertConfigId;

  @Column(name = "FIELD_NAME", nullable = false)
  private String fieldName;

  @Column(name = "FIELD_VALUE", nullable = false)
  private String fieldValue;

  @Column(name = "CONTENT_HTML")
  private String contentHtml;

  @Override
  public Long getId() {
    return id;
  }

}
