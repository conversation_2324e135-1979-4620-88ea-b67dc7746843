package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseSoftEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupOutputEnum;

@Entity
@Data
@Table(name = TableName.ALERT_GROUP_CONFIG)
@EqualsAndHashCode(callSuper = true)
public class AlertGroupConfigEntity extends BaseSoftEntity<Long> {

  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "ALERT_GROUP_CONFIG_SEQ", sequenceName = "ALERT_GROUP_CONFIG_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ALERT_GROUP_CONFIG_SEQ")
  private Long id;

  @Column(name = "NAME", nullable = false)
  private String name;

  @Column(name = "DESCRIPTION")
  private String description;

  @Column(name = "TYPE")
  @Enumerated(EnumType.STRING)
  private AlertGroupConfigTypeEnum type;

  @Column(name = "ALERT_OUTPUT", nullable = false)
  @Enumerated(EnumType.STRING)
  private AlertGroupOutputEnum alertOutput;

  @Column(name = "POSITION", nullable = false)
  private Integer position;

  @Column(name = "ACTIVE")
  private Boolean active;

  @Column(name = "CUSTOM_SERVICE_ID")
  private String customServiceId;

  @Column(name = "CUSTOM_APPLICATION_ID")
  private String customApplicationId;

  @Column(name = "CUSTOM_CONTENT")
  private String customContent;

  @Column(name = "CUSTOM_RECIPIENT")
  private String customRecipient;

  @Column(name = "CUSTOM_PRIORITY_CONFIG_ID")
  private Long customPriorityConfigId;

  @Override
  public Long getId() {
    return id;
  }

}
