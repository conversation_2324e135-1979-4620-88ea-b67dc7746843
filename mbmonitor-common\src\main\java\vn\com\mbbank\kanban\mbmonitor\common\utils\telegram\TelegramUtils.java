package vn.com.mbbank.kanban.mbmonitor.common.utils.telegram;

import java.util.Set;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Entities;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.jsoup.parser.Parser;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/25/2025
 */
public class TelegramUtils {
  private static final Set<String> TELEGRAM_ALLOWED_TAGS =
      Set.of("b", "strong", "i", "em", "u", "ins", "s", "strike", "del", "code", "pre", "a");


  /**
   * escape html text.
   *
   * @param input input
   * @return input after escape
   */
  public static String sanitizeHtml(String input) {
    Set<String> allowedTags = TELEGRAM_ALLOWED_TAGS;
    Document doc = Jsoup.parse(input, "", Parser.xmlParser());

    StringBuilder result = new StringBuilder();
    for (Node node : doc.childNodes()) {
      result.append(processNode(node, allowedTags, true));
    }

    return result.toString();
  }

  private static String processNode(Node node, Set<String> allowedTags, boolean isRoot) {
    if (node instanceof TextNode) {
      return Entities.escape(((TextNode) node).text());
    }
    if (node instanceof Element) {
      Element el = (Element) node;
      String content = el.childNodes().stream()
          .map(child -> processNode(child, allowedTags, false))
          .reduce("", String::concat);

      if (el.tagName().equals("a") && el.hasAttr("href")) {
        return "<a href=\"" + Entities.escape(el.attr("href")) + "\">"
            +
            content
            +
            "</a>";
      }
      return allowedTags.contains(el.tagName())
          ? "<" + el.tagName() + ">" + content + "</" + el.tagName() + ">"
          : "&lt;" + el.tagName() + "&gt; " + content;
    }
    return Entities.escape(node.outerHtml());
  }
}
