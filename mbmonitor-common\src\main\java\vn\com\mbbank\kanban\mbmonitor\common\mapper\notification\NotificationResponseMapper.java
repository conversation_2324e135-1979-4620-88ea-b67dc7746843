package vn.com.mbbank.kanban.mbmonitor.common.mapper.notification;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.core.utils.KanbanDateUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.NotificationMessageRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.NotificationResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NotificationEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 17/12/2024
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface NotificationResponseMapper extends KanbanBaseMapper<NotificationResponse, NotificationEntity> {
  NotificationResponseMapper INSTANCE = Mappers.getMapper(NotificationResponseMapper.class);

  /**
   * Maps the given NotificationMessageRequest object to a list of NotificationResponse objects.
   *
   * @param notificationMessageRequest the input request object containing notification details to be mapped
   * @return a list of NotificationResponse objects created from the provided NotificationMessageRequest
   */
  default List<NotificationResponse> map(NotificationMessageRequest notificationMessageRequest) {
    if (notificationMessageRequest == null) {
      return null;
    }

    var notifications = new ArrayList<NotificationResponse>();
    for (int i = 0; i < notificationMessageRequest.getUserNames().size(); i++) {
      notifications.add(NotificationResponse.builder()
          .id(notificationMessageRequest.getIds().get(i))
          .title(notificationMessageRequest.getTitle())
          .content(notificationMessageRequest.getContent())
          .type(notificationMessageRequest.getType())
          .userName(notificationMessageRequest.getUserNames().get(i))
          .sourceId(notificationMessageRequest.getSourceId())
          .sourceType(notificationMessageRequest.getSourceType())
          .createdDate(KanbanDateUtils.formatDate(new Date()))
          .isRead(false)
          .build());
    }
    return notifications;
  }
}
