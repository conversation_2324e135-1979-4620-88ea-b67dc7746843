package vn.com.mbbank.kanban.mbmonitor.common.utils;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.Map;
import javax.net.ssl.SSLContext;
import org.apache.hc.client5.http.HttpRoute;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.routing.HttpRoutePlanner;
import org.apache.hc.client5.http.socket.ConnectionSocketFactory;
import org.apache.hc.client5.http.ssl.NoopHostnameVerifier;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
import org.apache.hc.core5.http.HttpHost;
import org.apache.hc.core5.http.config.Registry;
import org.apache.hc.core5.http.config.RegistryBuilder;
import org.apache.hc.core5.ssl.SSLContextBuilder;
import org.apache.hc.core5.ssl.TrustStrategy;
import org.apache.hc.core5.util.Timeout;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/6/2025
 */
public class RestTemplateFactoryUtils {

  /**
   * Configuring local DNS is similar to adding entries to the hosts file on Windows.
   *
   * @param mapDns mapDns ip - hostname.
   * @param port   port
   * @return HttpComponentsClientHttpRequestFactory
   */
  public static HttpComponentsClientHttpRequestFactory localDnsHosts(Map<String, String> mapDns,
                                                                     Integer port) {
    SSLContext sslContext = null;
    try {
      //config allow all certificate.
      sslContext = SSLContextBuilder.create()
          .loadTrustMaterial((TrustStrategy) (X509Certificate[] chain, String authType) -> true)
          .build();
      // ignore check hostname
      SSLConnectionSocketFactory sslSocketFactory =
          new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);

      //SSLConnectionSocketFactory vào Connection Manager
      Registry<ConnectionSocketFactory> socketFactoryRegistry =
          RegistryBuilder.<ConnectionSocketFactory>create().register("https", sslSocketFactory)
              .build();

      PoolingHttpClientConnectionManager connectionManager =
          new PoolingHttpClientConnectionManager(socketFactoryRegistry);
      HttpRoutePlanner customRoutePlanner = (target, request) -> {
        String hostname = target.getHostName();
        if (mapDns.containsKey(hostname)) {
          String mappedIp = mapDns.get(hostname);
          HttpHost newTarget = new HttpHost(target.getSchemeName(), mappedIp, port);
          return new HttpRoute(newTarget);
        }
        return new HttpRoute(target);
      };

      CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(connectionManager)
          .setDefaultRequestConfig(RequestConfig.custom().setConnectTimeout(Timeout.ofSeconds(5))
              .setResponseTimeout(Timeout.ofSeconds(5)).build()).setRoutePlanner(customRoutePlanner)
          .build();

      HttpComponentsClientHttpRequestFactory factory =
          new HttpComponentsClientHttpRequestFactory(httpClient);
      return factory;

    } catch (NoSuchAlgorithmException e) {
      throw new RuntimeException(e);
    } catch (KeyManagementException e) {
      throw new RuntimeException(e);
    } catch (KeyStoreException e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * Setting proxy restemplate.
   *
   * @param ip  ip
   * @param port port
   * @return SimpleClientHttpRequestFactory
   */
  public static SimpleClientHttpRequestFactory settingProxy(String ip, int port) {
    SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
    Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(ip, port));
    requestFactory.setProxy(proxy);
    return requestFactory;
  }


}
