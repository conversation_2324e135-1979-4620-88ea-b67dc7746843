package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolSecurityTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EmailConnectionLogModel {
  private String host;
  private String description;
  private EmailProtocolTypeEnum protocolType;
  private EmailProtocolSecurityTypeEnum securityType;
  private Long port;
  private String email;
}
