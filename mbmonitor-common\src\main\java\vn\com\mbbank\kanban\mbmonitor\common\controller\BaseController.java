package vn.com.mbbank.kanban.mbmonitor.common.controller;

import java.util.Collection;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.acl.AclPermissionModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.systems.CommonAclPermissionService;


/**
 * Base Controller get userInfo or check permission users.
 */
public class BaseController implements vn.com.mbbank.kanban.core.controllers.BaseController {

  @Autowired
  private CommonAclPermissionService commonAclPermissionService;

  /**
   * check is admin.
   *
   * @throws BusinessException ex
   */

  public void makeSureUserAdmin() throws BusinessException {
    if (!commonAclPermissionService.isSupperAdmin()) {
      throw new BusinessException(ErrorCode.USER_DONT_HAVE_PERMISSIONS);
    }
  }

  /**
   * check permission.
   *
   * @param aclPermissionModels aclPermissionModels
   * @throws BusinessException ex
   */
  public void makeSurePermissions(List<AclPermissionModel> aclPermissionModels)
      throws BusinessException {
    var isPermission = commonAclPermissionService.isAnyPermission(aclPermissionModels);
    if (!isPermission) {
      throw new BusinessException(ErrorCode.USER_DONT_HAVE_PERMISSIONS);
    }
  }

  /**
   * check permission create or update.
   *
   * @param module       module
   * @param createAction createAction
   * @param updateAction updateAction
   * @param id           id
   * @throws BusinessException ex
   */
  public void makeSureCreateOrUpdate(PermissionModuleEnum module,
                                     PermissionActionEnum createAction,
                                     PermissionActionEnum updateAction,
                                     Collection<?> id)
      throws BusinessException {
    if (KanbanCommonUtil.isEmpty(id)) {
      makeSurePermissions(List.of(new AclPermissionModel(module, createAction)));
    } else {
      makeSurePermissions(List.of(new AclPermissionModel(module, updateAction)));
    }
  }

}
