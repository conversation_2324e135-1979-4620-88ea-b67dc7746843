package vn.com.mbbank.kanban.mbmonitor.common.constants;

/**
 * CommonConstants.
 */
public class CommonConstants {

  public static final int SECOND_PER_MINUTE = 60;
  public static final int ALERT_CLOSE_TIME_IF_COMMENT_IN_PAST_SECONDS = SECOND_PER_MINUTE * 5;
  public static final String DEFAULT_TIME_ZONE = "+7";
  /**
   * List constants of service/application.
   */
  public static final int SERVICE_NAME_MAX_LENGTH = 100;
  public static final int APPLICATION_NAME_MAX_LENGTH = 100;
  public static final int SERVICE_DESCRIPTION_MAX_LENGTH = 300;
  public static final int APPLICATION_DESCRIPTION_MAX_LENGTH = 300;
  public static final int MAX_QUERY_SQL_EXPORT_ROWS_LIMIT = 1_000_000;
  public static final int MAX_APPLICATION_EXPORT_ROWS_LIMIT = 10_000;
  public static final int MAX_SERVICE_EXPORT_ROWS_LIMIT = 1_000_000;
  public static final int MAX_ALERT_EXPORT_ROWS_LIMIT = 1_000_000;
  public static final int DEFAULT_WIDTH_COLUMN_EXCEL = 8000;
  public static final String CONTENT_DISPOSITION_ATTACHMENT = "attachment";
  /**
   * List constants of custom object.
   */
  public static final int CUSTOM_OBJECT_NAME_MAX_LENGTH = 300;
  public static final int CUSTOM_OBJECT_DESCRIPTION_MAX_LENGTH = 250;
  public static final int CUSTOM_OBJECT_REGEX_MAX_LENGTH = 250;
  public static final int CUSTOM_OBJECT_KEYWORD_MAX_LENGTH = 20;
  public static final int CUSTOM_OBJECT_INDEX_MAX_LENGTH = 1000;
  /**
   * List constants of alert priority.
   */
  public static final int ALERT_PRIORITY_CONFIG_NAME_MAX_LENGTH = 100;
  public static final int ALERT_PRIORITY_CONFIG_COLOR_MAX_LENGTH = 20;
  /**
   * List constants of config email.
   */
  public static final int COLLECT_EMAIL_CONFIG_NAME_MAX_LENGTH = 50;
  public static final int COLLECT_EMAIL_CONFIG_RECIPIENT_ALERT_MAX_LENGTH = 300;
  public static final int COLLECT_EMAIL_CONFIG_DESCRIPTION_MAX_LENGTH = 300;
  public static final int EMAIL_CONFIG_DESCRIPTION_MAX_LENGTH = 300;
  public static final int EMAIL_PARTNER_LIST_MAX_LENGTH = 100;

  /**
   * Database connection request constraints.
   */
  public static final int DATABASE_CONNECTION_NAME_MAX_LENGTH = 100;
  public static final int DATABASE_CONNECTION_DESCRIPTION_MAX_LENGTH = 300;
  public static final int DATABASE_CONNECTION_HOST_MAX_LENGTH = 245;
  public static final int DATABASE_CONNECTION_USERNAME_MAX_LENGTH = 128;
  public static final int DATABASE_CONNECTION_PASSWORD_MAX_LENGTH = 128;
  public static final int DATABASE_CONNECTION_DATABASE_NAME_MAX_LENGTH = 128;
  public static final int DATABASE_CONNECTION_ORACLE_SERVICE_NAME_MAX_LENGTH = 50;
  public static final int DATABASE_CONNECTION_PORT_MIN = 0;
  public static final int DATABASE_CONNECTION_PORT_MAX = 65535;

  /**
   * alert request constraints.
   */
  public static final int ALERT_REQUEST_OPERATOR_VALUE_MIN = -1;
  public static final int ALERT_REQUEST_OPERATOR_VALUE_MAX = 1_000_000_000;

  /**
   * List common.
   */
  public static final int COMMON_NAME_MAX_LENGTH = 100;
  public static final int COMMON_DESCRIPTION_MAX_LENGTH = 300;

  public static final int ALERT_GROUP_CONFIG_NAME_MAX_LENGTH = 50;
  public static final int ALERT_GROUP_CONFIG_DESCRIPTION_MAX_LENGTH = 200;
  public static final int EMAIL_CONFIG_HOST_MAX_LENGTH = 253;
  public static final int EMAIL_CONFIG_ADDRESS_MAX_LENGTH = 253;
  public static final int EMAIL_CONFIG_PASSWORD_MAX_LENGTH = 50;
  public static final int COMMON_QUERY_SQL_NAME_MAX_LENGTH = 100;
  public static final String DEFAULT_DELIMITER = ",";
  public static final String DEFAULT_WILDCARD = "*";
  public static final String MODULE_EXPORT = "MODULE_EXPORT";
  public static final int MAX_SIZE_BATCH_RECORD_EXPORT = 50_000;
  public static final int MAX_SIZE_FILE_RECORD_EXPORT = 1_000_000;

  public static final String EMAIL_MB_SUFFIX = "@mbbank.com.vn";

  public static final int EXECUTION_SCRIPT_MAX_LENGTH = 100_000;
  public static final int VARIABLE_MAX_LENGTH = 2000;
  public static final int NOTIFICATION_TITLE_MAX_LENGTH = 100;
  public static final int NOTIFICATION_CONTENT_MAX_LENGTH = 5000;
  public static final int EXECUTION_API_KEY_VALUE_MAX_LENGTH = 1000;
  public static final int EXECUTION_API_DESCRIPTION_MAX_LENGTH = 300;
  public static final int EXECUTION_API_URL_MAX_LENGTH = 2000;
  public static final int EXECUTION_API_USERNAME_MAX_LENGTH = 500;
  public static final int EXECUTION_API_PASSWORD_MAX_LENGTH = 500;
  public static final int EXECUTION_API_TOKEN_MAX_LENGTH = 4000;
}
