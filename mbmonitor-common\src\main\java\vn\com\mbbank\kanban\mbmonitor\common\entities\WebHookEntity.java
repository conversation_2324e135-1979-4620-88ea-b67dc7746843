package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WebHookConfigDataTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WebHookConfigMapTypeEnum;

/**
 * WebHookEntity
 *
 * <AUTHOR>
 * @created_date 8/19/2024
 */
@Entity
@Getter
@Setter
@Table(name = TableName.WEBHOOK)
public class WebHookEntity extends BaseEntity<Long> {
  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "WEBHOOK_SEQ", sequenceName = "WEBHOOK_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "WEBHOOK_SEQ")
  private Long id;

  @Column(name = "NAME")
  private String name;

  @Column(name = "DATA_TYPE")
  @Enumerated(EnumType.STRING)
  private WebHookConfigDataTypeEnum dataType;

  @Column(name = "TOKEN")
  private String token;

  @Column(name = "SERVICE_TYPE")
  @Enumerated(EnumType.STRING)
  private WebHookConfigMapTypeEnum serviceNameType;

  @Column(name = "SERVICE_ID")
  private String serviceId;

  @Column(name = "SERVICE_MAP_VALUE")
  private String serviceMapValue;

  @Column(name = "APPLICATION_TYPE")
  @Enumerated(EnumType.STRING)
  private WebHookConfigMapTypeEnum applicationType;

  @Column(name = "APPLICATION_ID")
  private String applicationId;

  @Column(name = "APPLICATION_MAP_VALUE")
  private String applicationMapValue;

  @Column(name = "ALERT_CONTENT_TYPE")
  @Enumerated(EnumType.STRING)
  private WebHookConfigMapTypeEnum alertContentType;

  @Column(name = "ALERT_CONTENT_CUSTOM_VALUE")
  private String alertContentCustomValue;


  @Column(name = "ALERT_CONTENT_MAP_VALUE")
  private String alertContentMapValue;

  @Column(name = "CONTACT_TYPE")
  @Enumerated(EnumType.STRING)
  private WebHookConfigMapTypeEnum contactType;

  @Column(name = "CONTACT_CUSTOM_VALUE")
  private String contactCustomValue;

  @Column(name = "CONTACT_MAP_VALUE")
  private String contactMapValue;

  @Column(name = "PRIORITY_TYPE")
  @Enumerated(EnumType.STRING)
  private WebHookConfigMapTypeEnum priorityType;

  @Column(name = "ALERT_PRIORITY_CONFIG_ID")
  private Long alertPriorityConfigId;

  @Column(name = "PRIORITY_MAP_VALUE")
  private String priorityMapValue;

  @Override
  public Long getId() {
    return id;
  }
}
