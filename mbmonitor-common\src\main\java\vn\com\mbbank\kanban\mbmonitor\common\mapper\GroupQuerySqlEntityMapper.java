package vn.com.mbbank.kanban.mbmonitor.common.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.GroupQuerySqlRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.GroupQuerySqlEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 16/9/2024
 */

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GroupQuerySqlEntityMapper extends KanbanBaseMapper<GroupQuerySqlEntity, GroupQuerySqlRequest> {
  GroupQuerySqlEntityMapper INSTANCE = Mappers.getMapper(GroupQuerySqlEntityMapper.class);

}


