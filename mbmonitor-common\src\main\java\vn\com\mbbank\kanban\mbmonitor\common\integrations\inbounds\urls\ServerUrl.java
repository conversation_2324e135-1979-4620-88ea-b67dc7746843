package vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls;

import vn.com.mbbank.kanban.mbmonitor.common.configs.KanbanPropertyConfigUtils;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.BaseUrl;

/**
 * Demo for Server URL.
 */
public class ServerUrl extends BaseUrl {
  /**
   * Demo for Server URL.
   *
   * @param path Path
   */
  private ServerUrl(String path) {
    super(path);
  }


  /**
   * Get baseUrl.
   *
   * @param baseApi baseApi
   * @return base url
   */
  public static final String getUrl(String baseApi) {
    return KanbanPropertyConfigUtils.getProperty("monitor.internal.server.url",
        getBaseUrl() + "api") + baseApi;
  }

  /**
   * Controller API.
   */
  public static final String BASE_URL = "";
  public static final String VERSION = "/v1";
  public static final String USERS_URL = BASE_URL + VERSION + "/systems/users";
  public static final String SUPERIOR_URL = BASE_URL + VERSION + "/admin/superiors";
  public static final String ALERT_URL = BASE_URL + VERSION + "/alerts";
  public static final String NOTE_URL = BASE_URL + VERSION + "/notes";
  public static final String SERVICE_URL = BASE_URL + VERSION + "/services";
  public static final String ALERT_REQUEST_URL = BASE_URL + VERSION + "/alert-requests";
  public static final String CUSTOM_OBJECT_URL = BASE_URL + VERSION + "/admin/custom-objects";
  public static final String APPLICATION_URL = BASE_URL + VERSION + "/applications";
  public static final String WEB_HOOK_CONFIG_URL = BASE_URL + VERSION + "/admin/webhooks";
  public static final String ROLE_URL = BASE_URL + VERSION + "/admin/roles";
  public static final String WEB_HOOK_URL = BASE_URL + VERSION + "/webhooks";
  public static final String ALERT_PRIORITY_CONFIG_URL =
      BASE_URL + VERSION + "/admin/priority-configs";
  public static final String EMAIL_CONFIG_URL = BASE_URL + VERSION + "/admin/email-configs";
  public static final String EMAIL_PARTNER_URL =
      BASE_URL + VERSION + "/admin/email-config/partners";
  public static final String EMAIL_TEMPLATE_URL =
      BASE_URL + VERSION + "/admin/email-config/templates";
  public static final String COLLECT_EMAIL_CONFIG_URL =
      BASE_URL + VERSION + "/admin/collect-emails";
  public static final String ALERT_GROUP_CONFIG_URL = BASE_URL + VERSION + "/admin/group-configs";
  public static final String ALERT_GROUP_URL = BASE_URL + VERSION + "/alert-groups";
  public static final String DATABASE_CONNECTION =
      BASE_URL + VERSION + "/admin/database-connections";
  public static final String EMAIL_URL = BASE_URL + VERSION + "/email";
  public static final String DATABASE_COLLECT = BASE_URL + VERSION + "/admin/database-collects";
  public static final String DATABASE_THRESHOLD_URL = BASE_URL + VERSION + "/admin/database-thresholds";
  public static final String MAINTENANCE_TIME_CONFIG_URL =
      BASE_URL + VERSION + "/admin/maintenance-times";

  public static final String TASK_URL = BASE_URL + VERSION + "/tasks";
  public static final String FILTER_ALERT_CONFIG_URL = BASE_URL + VERSION + "/admin/filter-alerts";
  public static final String MODIFY_ALERT_CONFIG_URL = BASE_URL + VERSION + "/admin/modify-alerts";
  public static final String EXPORT_DATA_URL = BASE_URL + VERSION + "/export-datas";

  public static final String TELEGRAM_ALERT_CONFIG_URL =
      BASE_URL + VERSION + "/admin/telegram-alert-configs";

  public static final String  TEAMS_ALERT_CONFIG_URL =
      BASE_URL + VERSION + "/admin/teams-alert-configs";

  /**
   * Details API users.
   */
  public static final BaseUrl USERS_URL_SERVICE = new ServerUrl(getUrl(USERS_URL));

  public static final String SYS_LOG_URL = BASE_URL + VERSION + "/admin/sys-logs";
  public static final String EXECUTION_URL = BASE_URL + VERSION + "/admin/executions";
  public static final String EXECUTION_GROUP_URL = BASE_URL + VERSION + "/admin/execution-groups";
  public static final String VARIABLE_URL = BASE_URL + VERSION + "/admin/variables";
  public static final String EXECUTION_HISTORY_URL = BASE_URL + VERSION + "/admin/execution-histories";
  public static final String AUTHENTICATE_URL = BASE_URL + VERSION + "/authenticate";
  public static final String AUTO_TRIGGER_ACTION_CONFIG_URL = BASE_URL + VERSION + "/admin/auto-trigger-action-configs";

  /**
   * API RPA Monitor Web.
   */
  public static final String RPA_CONFIG_URL = BASE_URL + VERSION + "/admin/rpa-configs";
  public static final String WEB_MONITOR_CONFIG_URL = BASE_URL + VERSION + "/admin/web-monitor-configs";
  public static final String NOTIFICATION_EVENT_URL = BASE_URL + VERSION + "/admin/notification-events";
}
