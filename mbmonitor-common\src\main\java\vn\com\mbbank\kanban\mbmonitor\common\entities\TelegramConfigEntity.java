package vn.com.mbbank.kanban.mbmonitor.common.entities;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TelegramConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/6/2025
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@Table(name = TableName.TELEGRAM_CONFIG)
public class TelegramConfigEntity extends BaseEntity<String> {

  @Id
  @Column(name = "ID")
  private String id = GeneratorUtil.generateId();

  @Column(name = "BOT_TOKEN")
  private String botToken;

  @Column(name = "DEFAULT_GROUP_CHAT_ID")
  private String defaultGroupChatId;

  @Column(name = "DESCRIPTION")
  private String description;

  @Column(name = "TYPE")
  @Enumerated(EnumType.STRING)
  private TelegramConfigTypeEnum type;

  @JsonProperty("isActive")
  @Column(name = "ACTIVE")
  private Boolean active;

}
