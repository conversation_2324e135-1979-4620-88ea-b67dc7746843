package vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.QuerySqlExecuteResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionStatusEnum;

/**
 * ExecutionRequest.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@SuperBuilder
public class ExecuteScriptResponse extends ExecutionResponse {
  QuerySqlExecuteResponse sqlExecutionResponse;
  String scriptResponse;
  String scriptError;
  ExecutionStatusEnum status;
}
