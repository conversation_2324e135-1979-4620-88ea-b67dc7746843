package vn.com.mbbank.kanban.mbmonitor.common.condition;



import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionCombinatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.OperatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleCondition;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleConverterUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleElement;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;

class RuleConverterUtilsTest {

  @Test
  void convertStringToRuleGroupType_success_case() throws Exception {
    String jsonString = """
        {
            "combinator": "AND",
            "rules": [
                {
                    "field": "age",
                    "operator": "GREATER_THAN",
                    "value": 18
                },
                {
                    "combinator": "OR",
                    "rules": [
                        {
                            "field": "name",
                            "operator": "IS",
                            "value": "John"
                        },
                        {
                            "field": "city",
                            "operator": "IS_NOT",
                            "value": "New York"
                        }
                    ]
                }
            ]
        }
        """;

    RuleGroupType ruleGroup = RuleConverterUtils.convertStringToRuleGroupType(jsonString);

    Assertions.assertEquals(ConditionCombinatorEnum.AND, ruleGroup.getCombinator());
    List<RuleElement> rules = ruleGroup.getRules();
    Assertions.assertEquals(2, rules.size());

    RuleCondition<?> ruleCondition1 = (RuleCondition<?>) rules.get(0);
    Assertions.assertEquals("age", ruleCondition1.getField());
    Assertions.assertEquals(OperatorEnum.GREATER_THAN, ruleCondition1.getOperator());
    Assertions.assertEquals(18, ruleCondition1.getValue());

    RuleGroupType nestedGroup = (RuleGroupType) rules.get(1);
    Assertions.assertEquals(ConditionCombinatorEnum.OR, nestedGroup.getCombinator());
    Assertions.assertEquals(2, nestedGroup.getRules().size());
  }

  @Test
  void getCombinator_success_case() {
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("combinator", "AND");

    ConditionCombinatorEnum combinator = RuleConverterUtils.getCombinator(jsonObject);
    Assertions.assertEquals(ConditionCombinatorEnum.AND, combinator);
  }

  @Test
  void getCombinator_invalidCombinator_exception_case() {
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("combinator", "INVALID");

    Assertions.assertThrows(IllegalArgumentException.class, () -> {
      RuleConverterUtils.getCombinator(jsonObject);
    });
  }

  @Test
  void getRules_success_case() throws Exception {
    JSONArray rulesArray = new JSONArray();

    JSONObject ruleCondition = new JSONObject();
    ruleCondition.put("field", "age");
    ruleCondition.put("operator", "GREATER_THAN");
    ruleCondition.put("value", 18);
    rulesArray.add(ruleCondition);

    JSONObject nestedGroup = new JSONObject();
    nestedGroup.put("combinator", "OR");
    JSONArray nestedRules = new JSONArray();
    JSONObject nestedRule = new JSONObject();
    nestedRule.put("field", "name");
    nestedRule.put("operator", "IS");
    nestedRule.put("value", "John");
    nestedRules.add(nestedRule);
    nestedGroup.put("rules", nestedRules);
    rulesArray.add(nestedGroup);

    // Test
    List<RuleElement> rules = RuleConverterUtils.getRules(rulesArray);
    Assertions.assertEquals(2, rules.size());

    // Check first rule
    RuleCondition<?> ruleCondition1 = (RuleCondition<?>) rules.get(0);
    Assertions.assertEquals("age", ruleCondition1.getField());
    Assertions.assertEquals(OperatorEnum.GREATER_THAN, ruleCondition1.getOperator());
    Assertions.assertEquals(18, ruleCondition1.getValue());

    // Check second rule (nested group)
    RuleGroupType nestedGroupType = (RuleGroupType) rules.get(1);
    Assertions.assertEquals(ConditionCombinatorEnum.OR, nestedGroupType.getCombinator());
    Assertions.assertEquals(1, nestedGroupType.getRules().size());
  }

  @Test
  void getRules_invalidRule_throwsException() throws Exception {
    JSONArray rulesArray = new JSONArray();
    JSONObject invalidRule = new JSONObject();
    invalidRule.put("invalidKey", "invalidValue");
    rulesArray.add(invalidRule);

    RuleConverterUtils.getRules(rulesArray);
  }

  @Test
  void contructor() {
    RuleConverterUtils r = new RuleConverterUtils();
    r = null;
  }

  @Test
  void createRuleCondition_success_case() {
    JSONObject ruleObject = new JSONObject();
    ruleObject.put("field", "age");
    ruleObject.put("operator", "GREATER_THAN");
    ruleObject.put("value", 18);

    RuleCondition<?> ruleCondition = RuleConverterUtils.createRuleCondition(ruleObject);
    Assertions.assertEquals("age", ruleCondition.getField());
    Assertions.assertEquals(OperatorEnum.GREATER_THAN, ruleCondition.getOperator());
    Assertions.assertEquals(18, ruleCondition.getValue());
  }

  @Test
  void createRuleCondition_invalidOperator_exception_case() {
    JSONObject ruleObject = new JSONObject();
    ruleObject.put("field", "age");
    ruleObject.put("operator", "INVALID_OPERATOR");
    ruleObject.put("value", 18);

    Assertions.assertThrows(IllegalArgumentException.class, () -> {
      RuleConverterUtils.createRuleCondition(ruleObject);
    });
  }
}
