Setting checkstyle intelij

1: File -> setting -> plugin and install CheckstyleIde
![img.png](image/img.png)
2: File -> setting -> tools -> checkstyle
![img_1.png](image/img_1.png)

3: add new checkstyle.xml from resource
![img_2.png](image/img_2.png)

4: apply checkstyle kanban

![img_3.png](image/img_3.png)

II: scan checkstyle when coding

![img_4.png](image/img_4.png)

III: Apply formater with checkstyle
File -> setting -> editor -> code style -> java
![img_5.png](image/img_5.png)

- Import schema -> checkstyle -> choice file checkstyle from source code
  ![img_6.png](image/img_6.png)

- scan source with checkstyle when develop
  ![img_7.png](image/img_7.png)

IV: setting auto format with save

- open file -> setting -> tool -> action on save and setting same image
  ![img_8.png](image/img_8.png)










