package vn.com.mbbank.kanban.mbmonitor.common.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.Getter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class EntityUtilsTest {

  private List<TestEntity> testData;

  @Getter
  static class TestEntity {
    private Long id;
    private String name;
    private String description;
    private Integer priority;
    private Status status;

    public TestEntity(Long id, String name, String description, Integer priority, Status status) {
      this.id = id;
      this.name = name;
      this.description = description;
      this.priority = priority;
      this.status = status;
    }

    enum Status {
      ACTIVE, INACTIVE, PENDING
    }
  }

  @BeforeEach
  void setUp() {
    testData = Arrays.asList(
        new TestEntity(1L, "Task A", "Fix bug in system", 2, TestEntity.Status.ACTIVE),
        new TestEntity(2L, "Task B", "Implement feature X", 1, TestEntity.Status.PENDING),
        new TestEntity(3L, "Task C", "Database migration", 3, TestEntity.Status.INACTIVE),
        new TestEntity(4L, "Task D", "Optimize API calls", 4, TestEntity.Status.ACTIVE)
    );
  }

  @Test
  void constructor() {
    new EntityUtils();
  }

  // Test getFieldsByOrder
  @Test
  void getFieldsByOrder_shouldReturnOrderedNames() {
    List<Long> ids = Arrays.asList(1L, 3L, 2L);
    List<String> result = EntityUtils.getFieldsByOrder(ids, testData, TestEntity::getId, TestEntity::getName);

    assertEquals(Arrays.asList("Task A", "Task C", "Task B"), result);
  }

  @Test
  void getFieldsByOrder_withMissingIds_shouldReturnNulls() {
    List<Long> ids = Arrays.asList(1L, 5L);
    List<String> result = EntityUtils.getFieldsByOrder(ids, testData, TestEntity::getId, TestEntity::getName);

    assertEquals(Arrays.asList("Task A", null), result);
  }

  @Test
  void getFieldsByOrder_withEmptyIds_shouldReturnEmptyList() {
    List<Long> ids = Collections.emptyList();
    List<String> result = EntityUtils.getFieldsByOrder(ids, testData, TestEntity::getId, TestEntity::getName);

    assertTrue(result.isEmpty());
  }

}