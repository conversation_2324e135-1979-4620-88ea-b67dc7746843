package vn.com.mbbank.kanban.mbmonitor.common.services.impl;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;
import vn.com.mbbank.kanban.mbmonitor.common.configs.ApplicationContextProvider;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.telegram.TelegramUserInGroupModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.telegram.TelegramUserInfoModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.services.RestApiService;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.CommonKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.RestTemplateFactoryUtils;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/24/2025
 */
class TelegramServiceImplTest {
  @Mock
  RestApiService restApiService;
  @Mock
  RestTemplate restTemplate;

  @Mock
  private CommonKafkaProducerService commonKafkaProducerService;
  @Mock
  Logger logger;
  @InjectMocks
  TelegramServiceImpl telegramServiceImpl;

  @Mock
  private HttpComponentsClientHttpRequestFactory requestFactory;

  @Mock
  private ClientHttpRequestFactory clientHttpRequestFactory;

  private MockedStatic<ApplicationContextProvider> applicationContextMock;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void sendMessage() {
    when(restApiService.post(anyString(), any(), any()))
        .thenReturn(new ResponseEntity<>("Ok", HttpStatus.OK));
    var result = telegramServiceImpl.sendMessage("botToken", "groupId", "message");
    Assertions.assertEquals(HttpStatus.OK, result.getStatusCode());
  }

  @Test
  void sendMessageFalse() {
    when(restApiService.post(anyString(), any(), any()))
        .thenReturn(new ResponseEntity<>("Ok", HttpStatus.FOUND));
    var result = telegramServiceImpl.sendMessage("botToken", "groupId", "message");
    Assertions.assertEquals(HttpStatus.FOUND, result.getStatusCode());
  }

  @Test
  void getMe_success() {
    TelegramUserInfoModel model = new TelegramUserInfoModel();
    model.setStatus(true);
    when(restApiService.get(anyString(), any()))
        .thenReturn(new ResponseEntity<>(new TelegramUserInfoModel(), HttpStatus.OK));

    TelegramUserInfoModel result = telegramServiceImpl.getMe("botToken");
    Assertions.assertEquals(false, result.isStatus());
  }

  @Test
  void getChatMember_success() {
    when(restApiService.get(anyString(), any()))
        .thenReturn(new ResponseEntity<>(new TelegramUserInGroupModel(), HttpStatus.OK));

    var result = telegramServiceImpl.getChatMember("botToken", "", "");
    Assertions.assertEquals(false, result.isStatus());
  }

  @Test
  void sendAlertKafka_success() {
    applicationContextMock = mockStatic(ApplicationContextProvider.class);
    applicationContextMock.when(
            () -> ApplicationContextProvider.getBean(CommonKafkaProducerService.class))
        .thenReturn(commonKafkaProducerService);
    TelegramUserInfoModel model = new TelegramUserInfoModel();
    model.setStatus(true);
    Mockito.doNothing().when(commonKafkaProducerService).send(any());
    assertDoesNotThrow(() -> telegramServiceImpl.sendAlertKafka(List.of(new AlertEntity())));
  }

  @Test
  void sendAlertKafka_AlertNull() {
    telegramServiceImpl.sendAlertKafka(null);
  }

  @Test
  void sendAlertKafka_exception() {
    TelegramUserInfoModel model = new TelegramUserInfoModel();
    model.setStatus(true);
    Mockito.doNothing().when(commonKafkaProducerService).send(any());
    assertDoesNotThrow(() -> telegramServiceImpl.sendAlertKafka(List.of(new AlertEntity())));
  }

  @Test
  void init_success() {
    when(restTemplate.getRequestFactory())
        .thenReturn(requestFactory);
    telegramServiceImpl.init();
    verify(restApiService).setCustomRestTemplate(any());
    verify(restApiService).setIsErrorHandler(anyBoolean());
  }

  @Test
  void init_has_dns_success() {
    ReflectionTestUtils.setField(telegramServiceImpl, "dnsIp", "a");
    ReflectionTestUtils.setField(telegramServiceImpl, "dnsPort", 80);
    when(restTemplate.getRequestFactory())
        .thenReturn(requestFactory);
    try (MockedStatic<RestTemplateFactoryUtils> mockedStatic = mockStatic(
        RestTemplateFactoryUtils.class)) {
      mockedStatic.when(() -> RestTemplateFactoryUtils.localDnsHosts(any(), any()))
          .thenReturn(requestFactory);
      telegramServiceImpl.init();
      verify(restApiService).setCustomRestTemplate(any());
      verify(restApiService).setIsErrorHandler(anyBoolean());
    }
  }

}
