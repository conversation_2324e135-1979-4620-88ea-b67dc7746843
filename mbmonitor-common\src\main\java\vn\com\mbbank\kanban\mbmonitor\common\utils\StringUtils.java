package vn.com.mbbank.kanban.mbmonitor.common.utils;

import vn.com.mbbank.kanban.core.utils.KanbanStringFormatter;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/24/2024
 */
public class StringUtils {
  /**
   * Capitalizes the first letter of the input string.
   *
   * @param input The string to capitalize.
   * @return A string with the first letter capitalized.
   */
  public static String capitalizeFirstLetter(String input) {
    return KanbanStringFormatter.newBuilder().blankIfNull().trim().capitalizeFirst(true).build()
        .format(input).toString();
  }

}