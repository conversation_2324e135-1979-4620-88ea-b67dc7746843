package vn.com.mbbank.kanban.mbmonitor.common.services;

import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseSoftService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/19/2025
 */
public interface CommonServiceService extends BaseSoftService<ServiceEntity, String> {
  
  /**
   * Find all by name ignore or serviceIds.
   *
   * @param serviceNames serviceNames
   * @param serviceIds   serviceIds
   * @return list entity
   */
  List<ServiceEntity> findAllByNameIgnoreCaseInOrServiceIdIn(List<String> serviceNames,
                                                             List<String> serviceIds);

}
