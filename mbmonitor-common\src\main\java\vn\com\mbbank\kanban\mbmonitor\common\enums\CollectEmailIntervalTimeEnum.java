package vn.com.mbbank.kanban.mbmonitor.common.enums;

/**
 * Enum representing the type of custom object.
 */
public enum CollectEmailIntervalTimeEnum {
  FIFTEEN_SECONDS(15 * 1000L),
  THIRTY_SECONDS(30 * 1000L),
  ONE_MINUTE(60 * 1000L),
  TWO_MINUTES(2 * 60 * 1000L),
  THREE_MINUTES(3 * 60 * 1000L),
  FIVE_MINUTES(5 * 60 * 1000L),
  TEN_MINUTES(10 * 60 * 1000L),
  THIRTY_MINUTES(30 * 60 * 1000L);

  private final Long milliseconds;

  CollectEmailIntervalTimeEnum(Long milliseconds) {
    this.milliseconds = milliseconds;
  }

  /**
   * Returns the duration in milliseconds.
   *
   * @return the duration in milliseconds
   */
  public Long getMilliseconds() {
    return milliseconds;
  }
}
