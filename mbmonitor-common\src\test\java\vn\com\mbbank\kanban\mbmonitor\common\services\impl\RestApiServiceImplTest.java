package vn.com.mbbank.kanban.mbmonitor.common.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

class RestApiServiceImplTest {

  @Mock
  private RestTemplate defaultRestTemplate;

  @Mock
  private RestTemplate customTemplate;

  @InjectMocks
  private RestApiServiceImpl restApiService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    restApiService.setCustomRestTemplate(customTemplate);
  }

  @Test
  void getRestTemplate_CustomTemplateNotNull() {
    restApiService.setCustomRestTemplate(customTemplate);
    assertEquals(customTemplate, restApiService.getRestTemplate());
  }

  @Test
  void getRestTemplate_DefaultTemplate() {
    restApiService.setCustomRestTemplate(null);
  }

  @Test
  void setRequestFactory_Simple() {
    SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
    restApiService.setRequestFactory(factory);
    verify(customTemplate).setRequestFactory(factory);
  }

  @Test
  void getWithoutParams() {
    String url = "http://example.com";
    ResponseEntity<String> mockResponse = new ResponseEntity<>("Success", HttpStatus.OK);

    when(customTemplate.exchange(url, HttpMethod.GET, null, String.class)).thenReturn(mockResponse);

    ResponseEntity<String> response = restApiService.get(url, String.class);
    assertEquals("Success", response.getBody());
  }

  @Test
  void getWithParams() {
    String url = "http://example.com";
    Map<String, String> params = Collections.singletonMap("key", "value");
    String finalUrl =
        UriComponentsBuilder.fromHttpUrl(url).queryParam("key", "value").toUriString();

    ResponseEntity<String> mockResponse = new ResponseEntity<>("Success", HttpStatus.OK);
    when(customTemplate.exchange(finalUrl, HttpMethod.GET, null, String.class)).thenReturn(
        mockResponse);

    ResponseEntity<String> response = restApiService.get(url, params, String.class);
    assertEquals("Success", response.getBody());
  }

  @Test
  void getWithHeaders() {
    String url = "http://example.com";
    HttpHeaders headers = new HttpHeaders();
    headers.set("Authorization", "Bearer token");
    HttpEntity<String> entity = new HttpEntity<>(headers);

    ResponseEntity<String> mockResponse = new ResponseEntity<>("Success", HttpStatus.OK);
    when(customTemplate.exchange(url, HttpMethod.GET, entity, String.class)).thenReturn(
        mockResponse);

    ResponseEntity<String> response = restApiService.get(url, headers, String.class);
    assertEquals("Success", response.getBody());
  }

  @Test
  void getWithHeadersParamAndresponseType_case_param_null() {
    String url = "http://example.com";
    HttpHeaders headers = new HttpHeaders();
    headers.set("Authorization", "Bearer token");
    HttpEntity<String> entity = new HttpEntity<>(headers);

    ResponseEntity<String> mockResponse = new ResponseEntity<>("Success", HttpStatus.OK);
    when(customTemplate.exchange(url, HttpMethod.GET, entity, String.class)).thenReturn(
        mockResponse);

    ResponseEntity<String> response = restApiService.get(url, null, headers, String.class);
    assertEquals("Success", response.getBody());
  }

  @Test
  void getWithHeadersParamAndresponseType_case_param_not_null() {
    String url = "http://example.com";
    HttpHeaders headers = new HttpHeaders();
    headers.set("Authorization", "Bearer token");
    HttpEntity<String> entity = new HttpEntity<>(headers);

    ResponseEntity<String> mockResponse = new ResponseEntity<>("Success", HttpStatus.OK);
    when(customTemplate.exchange(
        anyString(), // URL
        any(HttpMethod.class), // HTTP Method
        any(HttpEntity.class), // Request entity
        any(Class.class) // Response type
    )).thenReturn(mockResponse);

    ResponseEntity<String> response =
        restApiService.get(url, Map.of("a", ""), headers, String.class);
    assertEquals("Success", response.getBody());
  }

  @Test
  void post_success() {
    String url = "http://example.com";
    Object body = new Object();
    ResponseEntity<String> mockResponse = new ResponseEntity<>("Created", HttpStatus.CREATED);

    when(customTemplate.exchange(url, HttpMethod.POST, new HttpEntity<>(body),
        String.class)).thenReturn(mockResponse);

    ResponseEntity<String> response = restApiService.post(url, body, String.class);
    assertEquals(HttpStatus.CREATED, response.getStatusCode());
  }

  @Test
  void postWithHeaders() {
    String url = "http://example.com";
    Object body = new Object();
    HttpHeaders headers = new HttpHeaders();
    HttpEntity<Object> entity = new HttpEntity<>(body, headers);

    ResponseEntity<String> mockResponse = new ResponseEntity<>("Created", HttpStatus.CREATED);
    when(customTemplate.exchange(url, HttpMethod.POST, entity, String.class)).thenReturn(
        mockResponse);

    ResponseEntity<String> response = restApiService.post(url, body, headers, String.class);
    assertEquals(HttpStatus.CREATED, response.getStatusCode());
  }

  @Test
  void put_success() {
    String url = "http://example.com";
    Object body = new Object();
    ResponseEntity<String> mockResponse = new ResponseEntity<>("Updated", HttpStatus.OK);

    when(customTemplate.exchange(url, HttpMethod.PUT, new HttpEntity<>(body),
        String.class)).thenReturn(mockResponse);

    ResponseEntity<String> response = restApiService.put(url, body, String.class);
    assertEquals(HttpStatus.OK, response.getStatusCode());
  }

  @Test
  void putWithHeaders() {
    String url = "http://example.com";
    Object body = new Object();
    HttpHeaders headers = new HttpHeaders();
    HttpEntity<Object> entity = new HttpEntity<>(body, headers);

    ResponseEntity<String> mockResponse = new ResponseEntity<>("Updated", HttpStatus.OK);
    when(customTemplate.exchange(url, HttpMethod.PUT, entity, String.class)).thenReturn(
        mockResponse);

    ResponseEntity<String> response = restApiService.put(url, body, headers, String.class);
    assertEquals(HttpStatus.OK, response.getStatusCode());
  }

  @Test
  void delete_success() {
    String url = "http://example.com";
    ResponseEntity<String> mockResponse = new ResponseEntity<>("Deleted", HttpStatus.NO_CONTENT);

    when(customTemplate.exchange(url, HttpMethod.DELETE, null, String.class)).thenReturn(
        mockResponse);

    ResponseEntity<String> response = restApiService.delete(url, String.class);
    assertEquals(HttpStatus.NO_CONTENT, response.getStatusCode());
  }

  @Test
  void testDeleteWithParams() {
    String url = "http://example.com";
    Map<String, String> params = Collections.singletonMap("id", "123");
    String finalUrl = UriComponentsBuilder.fromHttpUrl(url).queryParam("id", "123").toUriString();

    ResponseEntity<String> mockResponse = new ResponseEntity<>("Deleted", HttpStatus.NO_CONTENT);
    when(customTemplate.exchange(finalUrl, HttpMethod.DELETE, null, String.class)).thenReturn(
        mockResponse);

    ResponseEntity<String> response = restApiService.delete(url, params, String.class);
    assertEquals(HttpStatus.NO_CONTENT, response.getStatusCode());
  }

  @Test
  void deleteWithHeaders() {
    String url = "http://example.com";
    HttpHeaders headers = new HttpHeaders();
    HttpEntity<String> entity = new HttpEntity<>(headers);

    ResponseEntity<String> mockResponse = new ResponseEntity<>("Deleted", HttpStatus.NO_CONTENT);
    when(customTemplate.exchange(url, HttpMethod.DELETE, entity, String.class)).thenReturn(
        mockResponse);

    ResponseEntity<String> response = restApiService.delete(url, headers, String.class);
    assertEquals(HttpStatus.NO_CONTENT, response.getStatusCode());
  }

  @Test
  void delete_with_param_success() {
    String url = "http://example.com";
    ResponseEntity<String> mockResponse = new ResponseEntity<>("Deleted", HttpStatus.NO_CONTENT);

    when(customTemplate.exchange(url, HttpMethod.DELETE, null, String.class)).thenReturn(
        mockResponse);

    ResponseEntity<String> response = restApiService.delete(url, new HashMap(), String.class);
    assertEquals(HttpStatus.NO_CONTENT, response.getStatusCode());
  }

  @Test
  void delete_with_param_empty_and_header_success() {
    String url = "http://example.com";
    ResponseEntity<String> mockResponse = new ResponseEntity<>("Deleted", HttpStatus.NO_CONTENT);

    when(customTemplate.exchange(anyString(), eq(HttpMethod.DELETE), any(), eq(String.class)))
        .thenReturn(mockResponse);
    HttpHeaders headers = new HttpHeaders();
    ResponseEntity<String> response =
        restApiService.delete(url, new HashMap(), headers, String.class);
    assertEquals(HttpStatus.NO_CONTENT, response.getStatusCode());
  }

  @Test
  void delete_with_param_and_header_success() {
    String url = "http://example.com";
    ResponseEntity<String> mockResponse = new ResponseEntity<>("Deleted", HttpStatus.NO_CONTENT);

    when(customTemplate.exchange(anyString(), eq(HttpMethod.DELETE), any(), eq(String.class)))
        .thenReturn(mockResponse);
    HttpHeaders headers = new HttpHeaders();
    ResponseEntity<String> response =
        restApiService.delete(url, Map.of("1", "1"), headers, String.class);
    assertEquals(HttpStatus.NO_CONTENT, response.getStatusCode());
  }

  @Test
  void setRequestFactory_Custom_SimpleClientHttpRequestFactory() {
    SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
    restApiService.setCustomRestTemplate(customTemplate);
    restApiService.setRequestFactory(factory);
    assertEquals(restApiService.getRestTemplate().getRequestFactory(),
        customTemplate.getRequestFactory());
  }

  @Test
  void setRequestFactory_default_SimpleClientHttpRequestFactory() {
    SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
    restApiService.setCustomRestTemplate(null);
    restApiService.setRequestFactory(factory);
    assertEquals(restApiService.getRestTemplate().getRequestFactory(),
        defaultRestTemplate.getRequestFactory());
  }

  @Test
  void setRequestFactory_Custom_HttpComponentsClientHttpRequestFactory() {
    CloseableHttpClient httpClient = mock(CloseableHttpClient.class);
    HttpComponentsClientHttpRequestFactory factory =
        new HttpComponentsClientHttpRequestFactory(httpClient);
    restApiService.setCustomRestTemplate(customTemplate);
    restApiService.setRequestFactory(factory);
    assertEquals(restApiService.getRestTemplate().getRequestFactory(),
        customTemplate.getRequestFactory());
  }

  @Test
  void setRequestFactory_default_HttpComponentsClientHttpRequestFactory() {
    CloseableHttpClient httpClient = mock(CloseableHttpClient.class);
    HttpComponentsClientHttpRequestFactory factory =
        new HttpComponentsClientHttpRequestFactory(httpClient);
    restApiService.setCustomRestTemplate(null);
    restApiService.setRequestFactory(factory);
    assertEquals(restApiService.getRestTemplate().getRequestFactory(),
        defaultRestTemplate.getRequestFactory());
  }

  @Test
  void setIsErrorHandler_Custom() {
    restApiService.setCustomRestTemplate(customTemplate);
    restApiService.setIsErrorHandler(true);
    assertEquals(restApiService.getRestTemplate().getRequestFactory(),
        customTemplate.getRequestFactory());
  }

  @Test
  void setIsErrorHandler_Default() {
    restApiService.setCustomRestTemplate(null);
    restApiService.setIsErrorHandler(true);
    assertEquals(restApiService.getRestTemplate().getRequestFactory(),
        defaultRestTemplate.getRequestFactory());
  }


}
