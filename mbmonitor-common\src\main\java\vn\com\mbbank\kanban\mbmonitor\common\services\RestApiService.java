package vn.com.mbbank.kanban.mbmonitor.common.services;

import java.util.Map;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * Service providing methods for making REST API calls using RestTemplate.
 */
@Service
public interface RestApiService {

  /**
   * Custom template.
   *
   * @param restTemplate restTemplate
   */
  void setCustomRestTemplate(RestTemplate restTemplate);

  /**
   * get rest template.
   *
   * @return RestTemplate
   */
  RestTemplate getRestTemplate();

  /**
   * Set factory.
   *
   * @param factory factory
   */
  void setRequestFactory(SimpleClientHttpRequestFactory factory);

  /**
   * Set custom request factory.
   *
   * @param factory factory
   */
  void setRequestFactory(HttpComponentsClientHttpRequestFactory factory);

  /**
   * On/Off error handel exception when status 4xx and 5xx.
   *
   * @param isErrorHandler isErrorHandler
   */
  void setIsErrorHandler(Boolean isErrorHandler);

  /**
   * Sends a GET request to the specified URL.
   *
   * @param url          The API endpoint to call.
   * @param responseType The expected response type.
   * @param <T>          The type of the response.
   * @return A ResponseEntity containing the API response.
   */
  <T> ResponseEntity<T> get(String url, Class<T> responseType);

  /**
   * Sends a GET request with query parameters.
   *
   * @param url          The API endpoint to call.
   * @param params       A map of query parameters.
   * @param responseType The expected response type.
   * @param <T>          The type of the response.
   * @return A ResponseEntity containing the API response.
   */
  <T> ResponseEntity<T> get(String url, Map<String, String> params, Class<T> responseType);

  /**
   * Sends a GET request with custom headers.
   *
   * @param url          The API endpoint to call.
   * @param headers      Custom HTTP headers.
   * @param responseType The expected response type.
   * @param <T>          The type of the response.
   * @return A ResponseEntity containing the API response.
   */
  <T> ResponseEntity<T> get(String url, HttpHeaders headers, Class<T> responseType);

  /**
   * Sends a GET request with query parameters and custom headers.
   *
   * @param url          The API endpoint to call.
   * @param params       A map of query parameters.
   * @param headers      Custom HTTP headers.
   * @param responseType The expected response type.
   * @param <T>          The type of the response.
   * @return A ResponseEntity containing the API response.
   */
  <T> ResponseEntity<T> get(String url, Map<String, String> params, HttpHeaders headers,
                            Class<T> responseType);

  /**
   * Sends a POST request with a request body.
   *
   * @param url          The API endpoint to call.
   * @param body         The request body to send.
   * @param responseType The expected response type.
   * @param <T>          The type of the response.
   * @return A ResponseEntity containing the API response.
   */
  <T> ResponseEntity<T> post(String url, Object body, Class<T> responseType);

  /**
   * Sends a POST request with a request body and custom headers.
   *
   * @param url          The API endpoint to call.
   * @param body         The request body to send.
   * @param headers      Custom HTTP headers.
   * @param responseType The expected response type.
   * @param <T>          The type of the response.
   * @return A ResponseEntity containing the API response.
   */
  <T> ResponseEntity<T> post(String url, Object body, HttpHeaders headers, Class<T> responseType);

  /**
   * Sends a PUT request with a request body.
   *
   * @param url          The API endpoint to call.
   * @param body         The request body to send.
   * @param responseType The expected response type.
   * @param <T>          The type of the response.
   * @return A ResponseEntity containing the API response.
   */
  <T> ResponseEntity<T> put(String url, Object body, Class<T> responseType);

  /**
   * Sends a PUT request with a request body and custom headers.
   *
   * @param url          The API endpoint to call.
   * @param body         The request body to send.
   * @param headers      Custom HTTP headers.
   * @param responseType The expected response type.
   * @param <T>          The type of the response.
   * @return A ResponseEntity containing the API response.
   */
  <T> ResponseEntity<T> put(String url, Object body, HttpHeaders headers, Class<T> responseType);

  /**
   * Sends a DELETE request to the specified URL.
   *
   * @param url          The API endpoint to call.
   * @param responseType The expected response type.
   * @param <T>          The type of the response.
   * @return A ResponseEntity containing the API response.
   */
  <T> ResponseEntity<T> delete(String url, Class<T> responseType);

  /**
   * Sends a DELETE request with query parameters.
   *
   * @param url          The API endpoint to call.
   * @param params       A map of query parameters.
   * @param responseType The expected response type.
   * @param <T>          The type of the response.
   * @return A ResponseEntity containing the API response.
   */
  <T> ResponseEntity<T> delete(String url, Map<String, String> params, Class<T> responseType);

  /**
   * Sends a DELETE request with custom headers.
   *
   * @param url          The API endpoint to call.
   * @param headers      Custom HTTP headers.
   * @param responseType The expected response type.
   * @param <T>          The type of the response.
   * @return A ResponseEntity containing the API response.
   */
  <T> ResponseEntity<T> delete(String url, HttpHeaders headers, Class<T> responseType);

  /**
   * Sends a DELETE request with query parameters and custom headers.
   *
   * @param url          The API endpoint to call.
   * @param params       A map of query parameters.
   * @param headers      Custom HTTP headers.
   * @param responseType The expected response type.
   * @param <T>          The type of the response.
   * @return A ResponseEntity containing the API response.
   */
  <T> ResponseEntity<T> delete(String url, Map<String, String> params, HttpHeaders headers,
                               Class<T> responseType);
}
