CREATE TABLE MBMONITOR.SYS_LOG
(
    ID            VARCHAR2(50)  NOT NULL,
    FUNCTION      VARCHAR2(100) NOT NULL,
    ACTION        VARCHAR2(100) NOT NULL,
    LOG_BY        VARCHAR2(255) NOT NULL,
    LOG_DATE      TIMESTAMP(6) NOT NULL,
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255),
    MESSAGE       CLOB
) PARTITION BY RANGE (LOG_DATE) INTERVAL (NUMTOYMINTERVAL(1, 'MONTH'))
(
    PARTITION PARTBASE VALUES LESS THAN (TO_DATE('2024-10-01 00:00:00', 'SYYYY-MM-DD HH24:MI:SS',
                                                 'NLS_CALENDAR=GREGORIAN'))
)
/

ALTER TABLE MBMONITOR.SYS_LOG
    ADD CONSTRAINT TASK_PK PRIMARY KEY (ID, LOG_DATE) USING INDEX LOCAL
/

CREATE INDEX MBMONITOR.SYS_LOG_ACTION_INDEX
    ON MBMONITOR.SYS_LOG (ACTION) LOCAL TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.SYS_LOG_FUNCTION_INDEX
    ON MBMONITOR.SYS_LOG (FUNCTION) LOCAL TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.SYS_LOG_LOG_BY_INDEX
    ON MBMONITOR.SYS_LOG (LOG_BY) LOCAL TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.SYS_LOG_LOG_DATE_ID_INDEX
    ON MBMONITOR.SYS_LOG (LOG_DATE, ID) LOCAL TABLESPACE INDEXS
/

CREATE SEQUENCE MBMONITOR.EXPORT_DATA_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999 NOCYCLE
    CACHE 50
/
CREATE TABLE MBMONITOR.EXPORT_DATA
(
    ID              VARCHAR2(50) NOT NULL
        CONSTRAINT EXPORT_DATA_pk
            PRIMARY KEY,
    FILE_NAME       VARCHAR2(100 CHAR),
    STATUS          VARCHAR2(30),
    EXTENSION       VARCHAR2(30),
    EXPORTED_BY     VARCHAR2(255),
    FILE_STORAGE_ID NUMBER,
    CREATED_DATE    TIMESTAMP(6),
    CREATED_BY      VARCHAR2(255),
    MODIFIED_DATE   TIMESTAMP(6),
    MODIFIED_BY     VARCHAR2(255)
)
    /

CREATE INDEX MBMONITOR.EXPORT_DATA_FILE_STORAGE_ID_INDEX
    ON MBMONITOR.EXPORT_DATA (FILE_STORAGE_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.DATABASE_THRESHOLD_CONFIG
(
    ID                     VARCHAR2(50) PRIMARY KEY,
    NAME                   VARCHAR2(100 CHAR),
    DESCRIPTION            VARCHAR2(300 CHAR),
    DATABASE_CONNECTION_ID NUMBER NOT NULL,
    SQL_COMMAND            CLOB,
    CRON_TIME              VARCHAR2(30),
    CONDITION_OPERATOR     VARCHAR2(30),
    CONDITION_VALUE        NUMBER,
    SERVICE_ID             VARCHAR2(50),
    APPLICATION_ID         VARCHAR2(50),
    PRIORITY_ID            NUMBER,
    RECIPIENT              VARCHAR2(255 CHAR),
    CONTENT                VARCHAR2(2000 CHAR),
    CONTENT_JSON           VARCHAR2(4000 CHAR),
    ACTIVE                 NUMBER(1) DEFAULT 1 NOT NULL
        CHECK (ACTIVE IN (0, 1)),
    CREATED_DATE           TIMESTAMP(6),
    CREATED_BY             VARCHAR2(255),
    MODIFIED_DATE          TIMESTAMP(6),
    MODIFIED_BY            VARCHAR2(255)
)
    /
CREATE INDEX MBMONITOR.DATABASE_THRESHOLD_CONFIG_SERVICE_ID_INDEX
    ON MBMONITOR.DATABASE_THRESHOLD_CONFIG (SERVICE_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.DATABASE_THRESHOLD_CONFIG_APPLICATION_ID_INDEX
    ON MBMONITOR.DATABASE_THRESHOLD_CONFIG (APPLICATION_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.DATABASE_THRESHOLD_CONFIG_PRIORITY_ID_INDEX
    ON MBMONITOR.DATABASE_THRESHOLD_CONFIG (PRIORITY_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.DATABASE_THRESHOLD_CONFIG_EMAIL_CONFIG_ID_INDEX
    ON MBMONITOR.DATABASE_THRESHOLD_CONFIG (DATABASE_CONNECTION_ID) TABLESPACE INDEXS
/
-- teams config
CREATE TABLE MBMONITOR.TEAMS_CONFIG
(
    ID               VARCHAR2(50) NOT NULL ENABLE,
    CLIENT_ID        VARCHAR2(255),
    TENANT_ID        VARCHAR2(255),
    CLIENT_SECRET    VARCHAR2(500),
    EMAIL            VARCHAR2(100),
    PASSWORD         VARCHAR2(500),
    TYPE             VARCHAR2(50),
    MESSAGE_TEMPLATE CLOB,
    INTERVAL         VARCHAR2(50),
    INTERVAL_TYPE    VARCHAR2(50),
    DESCRIPTION      VARCHAR2(1000),
    CREATED_DATE     TIMESTAMP(6),
    CREATED_BY       VARCHAR2(255),
    MODIFIED_DATE    TIMESTAMP(6),
    MODIFIED_BY      VARCHAR2(255),
    PRIMARY KEY (ID)
)
    /
CREATE INDEX MBMONITOR.CLIENT_ID_INDEX
    ON MBMONITOR.TEAMS_CONFIG (CLIENT_ID) TABLESPACE INDEXS
/
CREATE INDEX MBMONITOR.TENANT_ID_INDEX
    ON MBMONITOR.TEAMS_CONFIG (TENANT_ID) TABLESPACE INDEXS
/
CREATE INDEX MBMONITOR.EMAIL_INDEX
    ON MBMONITOR.TEAMS_CONFIG (EMAIL) TABLESPACE INDEXS
/
CREATE TABLE MBMONITOR.TEAMS_GROUP_CONFIG
(
    ID               VARCHAR2(50) NOT NULL ENABLE,
    TEAMS_CONFIG_ID  VARCHAR2(50),
    TEAMS_GROUP_ID   VARCHAR2(255),
    TEAMS_GROUP_NAME VARCHAR2(500 CHAR),
    POSITION         NUMBER,
    CREATED_DATE     TIMESTAMP(6),
    CREATED_BY       VARCHAR2(255),
    MODIFIED_DATE    TIMESTAMP(6),
    MODIFIED_BY      VARCHAR2(255),
    PRIMARY KEY (ID)
)
    /
CREATE INDEX MBMONITOR.TEAMS_GROUP_ID_INDEX
    ON MBMONITOR.TEAMS_GROUP_CONFIG (TEAMS_GROUP_ID) TABLESPACE INDEXS
/
CREATE INDEX MBMONITOR.TEAMS_CONFIG_ID_INDEX
    ON MBMONITOR.TEAMS_GROUP_CONFIG (TEAMS_CONFIG_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.TEAMS_USER_CONFIG
(
    ID                  VARCHAR2(50) NOT NULL ENABLE,
    TEAMS_GROUP_ID      VARCHAR2(50),
    TEAMS_USER_ID       VARCHAR2(255 CHAR),
    EMAIL               VARCHAR2(100),
    CREATED_DATE        TIMESTAMP(6),
    CREATED_BY          VARCHAR2(255),
    MODIFIED_DATE       TIMESTAMP(6),
    MODIFIED_BY         VARCHAR2(255),
    TEAMS_GROUP_CHAT_ID VARCHAR2(255),
    PRIMARY KEY (ID)
)
    /
CREATE INDEX MBMONITOR.TEAMS_USER_CONFIG_TEAMS_GROUP_ID_INDEX
    ON MBMONITOR.TEAMS_USER_CONFIG (TEAMS_GROUP_ID) TABLESPACE INDEXS
/
CREATE INDEX MBMONITOR.TEAMS_USER_CONFIG_TEAMS_USER_ID_INDEX
    ON MBMONITOR.TEAMS_USER_CONFIG (TEAMS_USER_ID) TABLESPACE INDEXS
/
CREATE INDEX MBMONITOR.TEAMS_USER_CONFIG_EMAIL_INDEX
    ON MBMONITOR.TEAMS_USER_CONFIG (EMAIL) TABLESPACE INDEXS
/

-- update collect email config
ALTER TABLE MBMONITOR.COLLECT_EMAIL_CONFIG
    ADD (
        TYPE VARCHAR2(50),
        LAST_RECEIVED_EMAIL_DATE TIMESTAMP(6),
        ABSENCE_INTERVAL NUMBER,
        ALERT_REPEAT_INTERVAL NUMBER
        )
/
UPDATE MBMONITOR.COLLECT_EMAIL_CONFIG
SET TYPE = 'EVENT_BASE_ALERT';


CREATE TABLE MBMONITOR.EXECUTION
(
    ID                     VARCHAR2(50)       NOT NULL ENABLE PRIMARY KEY,
    NAME                   VARCHAR2(100 CHAR) NOT NULL,
    DESCRIPTION            VARCHAR2(300 CHAR),
    TYPE                   VARCHAR2(50)       NOT NULL,
    DATABASE_CONNECTION_ID NUMBER,
    EXECUTION_GROUP_ID     VARCHAR2(50)       NOT NULL,
    SCRIPT                 CLOB,
    CREATED_DATE           TIMESTAMP(6),
    CREATED_BY             VARCHAR2(255),
    MODIFIED_DATE          TIMESTAMP(6),
    MODIFIED_BY            VARCHAR2(255)
)
/

CREATE INDEX MBMONITOR.EXECUTION_DATABASE_CONNECTION_ID_INDEX
    ON MBMONITOR.EXECUTION (DATABASE_CONNECTION_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.EXECUTION_EXECUTION_GROUP_ID_INDEX
    ON MBMONITOR.EXECUTION (EXECUTION_GROUP_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.EXECUTION_GROUP
(
    ID            VARCHAR2(50)       NOT NULL ENABLE PRIMARY KEY,
    NAME          VARCHAR2(100 CHAR) NOT NULL,
    DESCRIPTION   VARCHAR2(300 CHAR),
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255)
)
    /

CREATE TABLE MBMONITOR.EXECUTION_HISTORY
(
    ID                    VARCHAR2(50)       NOT NULL,
    EXECUTION_NAME        VARCHAR2(100 CHAR) NOT NULL,
    EXECUTION_DESCRIPTION VARCHAR2(300 CHAR),
    EXECUTION_TYPE        VARCHAR2(50)       NOT NULL,
    EXECUTION_SCRIPT      CLOB         NOT NULL,
    EXECUTION_PARAMS      CLOB,
    STATUS                VARCHAR2(50)       NOT NULL,
    RESULT                CLOB,
    ERROR                 CLOB,
    EXECUTION_BY          VARCHAR2(255)      NOT NULL,
    START_TIME            TIMESTAMP(6) NOT NULL,
    END_TIME              TIMESTAMP(6),
    CREATED_DATE          TIMESTAMP(6),
    CREATED_BY            VARCHAR2(255),
    MODIFIED_DATE         TIMESTAMP(6),
    MODIFIED_BY           VARCHAR2(255)
) PARTITION BY RANGE (START_TIME) INTERVAL (NUMTOYMINTERVAL(1, 'MONTH'))
(
    PARTITION PARTBASE VALUES LESS THAN (TO_DATE('2024-10-01 00:00:00', 'SYYYY-MM-DD HH24:MI:SS',
                                                 'NLS_CALENDAR=GREGORIAN'))
)
/

ALTER TABLE MBMONITOR.EXECUTION_HISTORY
    ADD CONSTRAINT EXECUTION_HISTORY_PK PRIMARY KEY (ID, START_TIME) USING INDEX LOCAL
/

CREATE INDEX MBMONITOR.EXECUTION_HISTORY_EXECUTION_BY_INDEX
    ON MBMONITOR.EXECUTION_HISTORY (EXECUTION_BY) LOCAL TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.EXECUTION_PARAM
(
    ID            VARCHAR2(50)       NOT NULL ENABLE PRIMARY KEY,
    NAME          VARCHAR2(100 CHAR) NOT NULL,
    EXECUTION_ID  VARCHAR2(50)       NOT NULL,
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255)
)
    /

CREATE INDEX MBMONITOR.EXECUTION_PARAM_EXECUTION_ID_INDEX
    ON MBMONITOR.EXECUTION_PARAM (EXECUTION_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.VARIABLE
(
    ID            VARCHAR2(50)        NOT NULL ENABLE PRIMARY KEY,
    NAME          VARCHAR2(100 CHAR)  NOT NULL,
    DESCRIPTION   VARCHAR2(300 CHAR),
    VALUE         VARCHAR2(4000 CHAR) NOT NULL,
    HIDDEN        NUMBER(1)           NOT NULL,
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255)
)
    /

-- Xóa table liên quan đến tính năng sql (SQL gộp vs execution)
DROP TABLE MBMONITOR.QUERY_SQL;
DROP TABLE MBMONITOR.GROUP_QUERY_SQL;
DROP TABLE MBMONITOR.PARAM_QUERY_SQL;

-- Detele role for SQL command feature
DELETE
FROM MBMONITOR.SYS_ROLE_PERMISSION sysRolePermission
WHERE sysRolePermission.MODULE_NAME = 'MULTI_SQL_QUERY';

-- Update MODULE_ID and MODULE_PARENT_ID to string
ALTER TABLE MBMONITOR.SYS_ROLE_PERMISSION
    MODIFY MODULE_ID VARCHAR2(50)
    MODIFY MODULE_PARENT_ID VARCHAR2(50)
    /

CREATE TABLE MBMONITOR.SYS_USER_REFRESH_TOKEN
(
    ID            VARCHAR2(30)  NOT NULL ENABLE,
    REFRESH_TOKEN VARCHAR2(26)  NOT NULL,
    EXPIRED_DATE  TIMESTAMP(6)  NOT NULL,
    USERNAME      VARCHAR2(255) NOT NULL,
    CREATED_BY    VARCHAR2(100),
    CREATED_DATE  TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(100),
    MODIFIED_DATE TIMESTAMP(6)
) PARTITION BY RANGE (EXPIRED_DATE) INTERVAL (NUMTOYMINTERVAL(1, 'MONTH'))
(
    PARTITION PARTBASE VALUES LESS THAN (TO_DATE('2025-01-01 00:00:00', 'SYYYY-MM-DD HH24:MI:SS',
                                                 'NLS_CALENDAR=GREGORIAN'))
)
/

ALTER TABLE MBMONITOR.SYS_USER_REFRESH_TOKEN
    ADD CONSTRAINT SYS_USER_REFRESH_TOKEN_PK PRIMARY KEY (ID, EXPIRED_DATE) USING INDEX LOCAL
/

CREATE INDEX MBMONITOR.SYS_USER_REFRESH_TOKEN_REFRESH_TOKEN_INDEX
    ON MBMONITOR.SYS_USER_REFRESH_TOKEN (REFRESH_TOKEN) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.AUTO_TRIGGER_ACTION_CONFIG
(
    ID                      VARCHAR2(50)       NOT NULL,
    CREATED_DATE            TIMESTAMP(6),
    CREATED_BY              VARCHAR2(255),
    MODIFIED_DATE           TIMESTAMP(6),
    MODIFIED_BY             VARCHAR2(255),
    NAME                    VARCHAR2(100 CHAR) NOT NULL,
    DESCRIPTION             VARCHAR2(300 CHAR),
    ACTIVE                  NUMBER(1, 0) DEFAULT 1,
    TIME_SINCE_LAST_TRIGGER NUMBER(10, 0),
    RULE_GROUP              CLOB,
    LAST_RUN                TIMESTAMP(6),
    PRIMARY KEY (ID)
)
/

CREATE TABLE MBMONITOR.AUTO_TRIGGER_ACTION_CONFIG_DEPENDENCY
(
    ID                            VARCHAR2(50) NOT NULL,
    AUTO_TRIGGER_ACTION_CONFIG_ID VARCHAR2(50),
    DEPENDENCY_ID                 VARCHAR2(50),
    TYPE                          VARCHAR2(50),
    CREATED_DATE                  TIMESTAMP(6),
    CREATED_BY                    VARCHAR2(255),
    MODIFIED_DATE                 TIMESTAMP(6),
    MODIFIED_BY                   VARCHAR2(255),
    PRIMARY KEY (ID)
)
/
CREATE INDEX MBMONITOR.AUTO_TRIGGER_ACTION_CONFIG_DEPENDENCY_AUTO_TRIGGER_ACTION_CONFIG_ID_TYPE_DEPENDENCE_ID_INDEX
    ON MBMONITOR.AUTO_TRIGGER_ACTION_CONFIG_DEPENDENCY (AUTO_TRIGGER_ACTION_CONFIG_ID, TYPE, DEPENDENCY_ID) TABLESPACE INDEXS;
/

CREATE TABLE MBMONITOR.AUTO_TRIGGER_ACTION_CONFIG_EXECUTION_MAPPER
(
    ID                            VARCHAR2(50) NOT NULL,
    AUTO_TRIGGER_ACTION_CONFIG_ID VARCHAR2(50),
    EXECUTION_ID                  VARCHAR2(50),
    CREATED_DATE                  TIMESTAMP(6),
    CREATED_BY                    VARCHAR2(255),
    MODIFIED_DATE                 TIMESTAMP(6),
    MODIFIED_BY                   VARCHAR2(255),
    PRIMARY KEY (ID)
)
/
CREATE INDEX MBMONITOR.AUTO_TRIGGER_ACTION_CONFIG_EXECUTION_MAPPER_AUTO_TRIGGER_ACTION_CONFIG_ID_EXECUTION_ID_INDEX
    ON MBMONITOR.AUTO_TRIGGER_ACTION_CONFIG_EXECUTION_MAPPER (AUTO_TRIGGER_ACTION_CONFIG_ID, EXECUTION_ID) TABLESPACE INDEXS
/
ALTER TABLE MBMONITOR.DATABASE_CONNECTION
    ADD DATABASE_NAME VARCHAR2(128 CHAR)
    MODIFY USER_NAME VARCHAR2(128 CHAR)
    MODIFY PASSWORD VARCHAR2(128 CHAR)
    /
CREATE TABLE MBMONITOR.ALERT_REQUEST
(
    ID                 VARCHAR2(50) NOT NULL ENABLE,
    SERVICE_ID         VARCHAR2(10),
    APPLICATION_ID     VARCHAR2(10),
    SOURCE_TYPE        VARCHAR2(50),
    STATUS             VARCHAR2(50),
    CRON_TIME          VARCHAR2(50),
    CONTENT            VARCHAR2(2000 CHAR),
    CONTENT_JSON       VARCHAR2(4000 CHAR),
    RECIPIENT          VARCHAR2(255 CHAR),
    PRIORITY_ID        NUMBER,
    CONDITION_OPERATOR VARCHAR2(50),
    CONDITION_VALUE    NUMBER,
    APPROVED_BY        VARCHAR2(100),
    APPROVED_DATE      TIMESTAMP(6),
    REJECTED_REASON    VARCHAR2(1000 CHAR),
    CREATED_DATE       TIMESTAMP(6),
    CREATED_BY         VARCHAR2(100),
    MODIFIED_DATE      TIMESTAMP(6),
    MODIFIED_BY        VARCHAR2(100)
) PARTITION BY RANGE (CREATED_DATE) INTERVAL (NUMTOYMINTERVAL(1, 'MONTH'))
(
    PARTITION PARTBASE VALUES LESS THAN (TO_DATE('2024-10-01 00:00:00', 'SYYYY-MM-DD HH24:MI:SS',
                                                 'NLS_CALENDAR=GREGORIAN'))
)
/
ALTER TABLE MBMONITOR.ALERT_REQUEST
    ADD CONSTRAINT ALERT_REQUEST_PK PRIMARY KEY (ID, CREATED_DATE) USING INDEX LOCAL
/
CREATE INDEX MBMONITOR.ALERT_REQUEST_SERVICE_ID_INDEX
    ON MBMONITOR.ALERT_REQUEST (SERVICE_ID)  LOCAL TABLESPACE INDEXS;
/

CREATE INDEX MBMONITOR.ALERT_REQUEST_APPLICATION_ID_INDEX
    ON MBMONITOR.ALERT_REQUEST (APPLICATION_ID)  LOCAL TABLESPACE INDEXS;
/

CREATE INDEX MBMONITOR.ALERT_REQUEST_STATUS_INDEX
    ON MBMONITOR.ALERT_REQUEST (STATUS)  LOCAL TABLESPACE INDEXS;
/

CREATE INDEX MBMONITOR.ALERT_REQUEST_PRIORITY_ID_INDEX
    ON MBMONITOR.ALERT_REQUEST (PRIORITY_ID)  LOCAL TABLESPACE INDEXS;
/

CREATE INDEX MBMONITOR.ALERT_REQUEST_CREATED_BY_INDEX
    ON MBMONITOR.ALERT_REQUEST (CREATED_BY)  LOCAL TABLESPACE INDEXS;
/

CREATE TABLE MBMONITOR.ALERT_REQUEST_DATABASE
(
    ID                     VARCHAR2(50) NOT NULL ENABLE,
    NAME                   VARCHAR2(100 CHAR),
    ALERT_REQUEST_ID       VARCHAR2(50),
    DATABASE_CONNECTION_ID NUMBER,
    SQL_COMMAND            VARCHAR2(4000 char),
    CREATED_DATE           TIMESTAMP(6),
    CREATED_BY             VARCHAR2(100),
    MODIFIED_DATE          TIMESTAMP(6),
    MODIFIED_BY            VARCHAR2(100)
) PARTITION BY RANGE (CREATED_DATE) INTERVAL (NUMTOYMINTERVAL(1, 'MONTH'))
(
    PARTITION PARTBASE VALUES LESS THAN (TO_DATE('2024-10-01 00:00:00', 'SYYYY-MM-DD HH24:MI:SS',
                                                 'NLS_CALENDAR=GREGORIAN'))
)
/
ALTER TABLE MBMONITOR.ALERT_REQUEST_DATABASE
    ADD CONSTRAINT ALERT_REQUEST_DATABASE_PK PRIMARY KEY (ID, CREATED_DATE) USING INDEX LOCAL
/

CREATE INDEX MBMONITOR.ALERT_REQUEST_DATABASE_ALERT_REQUEST_ID_INDEX
    ON MBMONITOR.ALERT_REQUEST_DATABASE (ALERT_REQUEST_ID)  LOCAL TABLESPACE INDEXS;
/

CREATE INDEX MBMONITOR.ALERT_REQUEST_DATABASE_DATABASE_CONNECTION_ID_INDEX
    ON MBMONITOR.ALERT_REQUEST_DATABASE (DATABASE_CONNECTION_ID)  LOCAL TABLESPACE INDEXS;
/

CREATE INDEX MBMONITOR.ALERT_REQUEST_DATABASE_CREATED_BY_INDEX
    ON MBMONITOR.ALERT_REQUEST_DATABASE (CREATED_BY) LOCAL TABLESPACE INDEXS;
/

CREATE TABLE MBMONITOR.NOTIFICATION
(
    ID            VARCHAR2(50),
    TITLE         VARCHAR2(100 CHAR),
    CONTENT       CLOB,
    TYPE          VARCHAR2(50),
    USER_NAME     VARCHAR2(100 CHAR),
    SOURCE_ID     VARCHAR2(50),
    SOURCE_TYPE   VARCHAR2(50),
    IS_READ       NUMBER(1),
    READ_DATE     TIMESTAMP(6),
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(100 CHAR),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(100 CHAR)
) PARTITION BY RANGE (CREATED_DATE) INTERVAL (NUMTOYMINTERVAL(1, 'MONTH'))
(
    PARTITION PARTBASE VALUES LESS THAN (TO_DATE('2024-10-01 00:00:00', 'SYYYY-MM-DD HH24:MI:SS',
                                                 'NLS_CALENDAR=GREGORIAN'))
)
/

ALTER TABLE MBMONITOR.NOTIFICATION
    ADD CONSTRAINT NOTIFICATION_PK PRIMARY KEY (ID, CREATED_DATE) USING INDEX LOCAL
/

CREATE INDEX MBMONITOR.NOTIFICATION_USERNAME_INDEX
    ON MBMONITOR.NOTIFICATION (USER_NAME) LOCAL TABLESPACE INDEXS
/

CREATE INDEX NOTIFICATION_USER_NAME_CREATED_DATE_ID_INDEX
    ON MBMONITOR.NOTIFICATION (USER_NAME, CREATED_DATE DESC, ID) LOCAL TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.NOTIFICATION_SOURCE_TYPE_SOURCE_ID_INDEX
    ON MBMONITOR.NOTIFICATION (SOURCE_TYPE, SOURCE_ID) LOCAL TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.RPA_CONFIG
(
    ID              VARCHAR2(50)        NOT NULL ENABLE PRIMARY KEY,
    INTERVAL        NUMBER    DEFAULT 300,
    NUMBER_OF_RETRY NUMBER    DEFAULT 0,
    ACTIVE          NUMBER(1) DEFAULT 1 NOT NULL CHECK (ACTIVE IN (0, 1)),
    CREATED_DATE    TIMESTAMP(6),
    CREATED_BY      VARCHAR2(255),
    MODIFIED_DATE   TIMESTAMP(6),
    MODIFIED_BY     VARCHAR2(255)
)
/
CREATE TABLE MBMONITOR.MONITOR_ACTION
(
    ID              VARCHAR2(50)  NOT NULL ENABLE PRIMARY KEY,
    ACTION_ID       VARCHAR2(50)  NOT NULL,
    ACTION_TYPE     VARCHAR2(100) NOT NULL,
    FIND_ELEMENT_BY VARCHAR2(50)  NOT NULL,
    IDENTIFIER      CLOB,
    VALUE           VARCHAR2(2000 CHAR),
    ORDERS          NUMBER        NOT NULL,
    CREATED_DATE    TIMESTAMP(6),
    CREATED_BY      VARCHAR2(255),
    MODIFIED_DATE   TIMESTAMP(6),
    MODIFIED_BY     VARCHAR2(255)
)
/
CREATE INDEX MBMONITOR.MONITOR_ACTION_ACTION_ID_INDEX
    ON MBMONITOR.MONITOR_ACTION (ACTION_ID)
    TABLESPACE INDEXS
/
CREATE TABLE MBMONITOR.MONITOR_WEB_CONFIG
(
    ID             VARCHAR2(50)        NOT NULL ENABLE PRIMARY KEY,
    NAME           VARCHAR2(100 CHAR),
    DESCRIPTION    VARCHAR2(300 CHAR),
    WEB_URL        VARCHAR2(2000 CHAR) NOT NULL,
    MONITOR_TYPE   VARCHAR2(100)       NOT NULL,
    TIME_OUT       NUMBER              NOT NULL,
    BROWSER        VARCHAR2(50)        NOT NULL,
    MONTHS         VARCHAR2(50),
    DAY_OF_MONTHS  VARCHAR2(100),
    DAY_OF_WEEKS   VARCHAR2(50),
    HOURS          VARCHAR2(200),
    AUTH_ACTION_ID VARCHAR2(50),
    ACTION_ID      VARCHAR2(50),
    SERVICE_ID     VARCHAR2(50),
    APPLICATION_ID VARCHAR2(50),
    PRIORITY_ID    NUMBER,
    CONTACT        VARCHAR2(255 CHAR),
    CONTENT        VARCHAR2(2000 CHAR),
    CONTENT_JSON   VARCHAR2(4000 CHAR),
    ACTIVE         NUMBER(1) DEFAULT 1 NOT NULL CHECK (ACTIVE IN (0, 1)),
    CREATED_DATE   TIMESTAMP(6),
    CREATED_BY     VARCHAR2(255),
    MODIFIED_DATE  TIMESTAMP(6),
    MODIFIED_BY    VARCHAR2(255)
)
/
CREATE INDEX MBMONITOR.MONITOR_WEB_CONFIG_MONITOR_AUTH_ACTION_INDEX
    ON MBMONITOR.MONITOR_WEB_CONFIG (AUTH_ACTION_ID) TABLESPACE INDEXS
/
CREATE INDEX MBMONITOR.MONITOR_WEB_CONFIG_MONITOR_ACTION_INDEX
    ON MBMONITOR.MONITOR_WEB_CONFIG (ACTION_ID) TABLESPACE INDEXS
/
CREATE INDEX MBMONITOR.MONITOR_WEB_CONFIG_MONITOR_SERVICE_ID_INDEX
    ON MBMONITOR.MONITOR_WEB_CONFIG (SERVICE_ID) TABLESPACE INDEXS
/
CREATE INDEX MBMONITOR.MONITOR_WEB_CONFIG_MONITOR_APPLICATION_ID_INDEX
    ON MBMONITOR.MONITOR_WEB_CONFIG (APPLICATION_ID) TABLESPACE INDEXS
/
CREATE INDEX MBMONITOR.MONITOR_WEB_CONFIG_MONITOR_PRIORITY_ID_INDEX
    ON MBMONITOR.MONITOR_WEB_CONFIG (PRIORITY_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.NOTIFICATION_EVENT
(
    ID                VARCHAR2(50),
    TITLE             VARCHAR2(100 CHAR),
    CONTENT           CLOB,
    NOTIFICATION_TYPE VARCHAR2(50),
    SCHEDULE_TYPE     VARCHAR2(50),
    TRIGGERED_DATE    TIMESTAMP(6),
    CRON_EXPRESSION   VARCHAR2(200),
    ACTIVE            NUMBER(1, 0) DEFAULT 1,
    CREATED_DATE      TIMESTAMP(6),
    CREATED_BY        VARCHAR2(100),
    MODIFIED_DATE     TIMESTAMP(6),
    MODIFIED_BY       VARCHAR2(100),
    PRIMARY KEY (ID)
)
/

-- Create NOTIFICATION_EVENT_TARGET table
CREATE TABLE MBMONITOR.NOTIFICATION_EVENT_TARGET
(
    ID                    VARCHAR2(50),
    TARGET                VARCHAR2(300 CHAR),
    TYPE                  VARCHAR2(50),
    NOTIFICATION_EVENT_ID VARCHAR2(50),
    CREATED_DATE          TIMESTAMP(6),
    CREATED_BY            VARCHAR2(100),
    MODIFIED_DATE         TIMESTAMP(6),
    MODIFIED_BY           VARCHAR2(100),
    PRIMARY KEY (ID)
)
/

-- Create indexes
CREATE INDEX MBMONITOR.NOTIFICATION_EVENT_ACTIVE_INDEX
    ON MBMONITOR.NOTIFICATION_EVENT (ACTIVE) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.NOTIFICATION_EVENT_SCHEDULE_TYPE_INDEX
    ON MBMONITOR.NOTIFICATION_EVENT (SCHEDULE_TYPE) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.NOTIFICATION_EVENT_TARGET_EVENT_ID_INDEX
    ON MBMONITOR.NOTIFICATION_EVENT_TARGET (NOTIFICATION_EVENT_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.NOTIFICATION_EVENT_TARGET_TYPE_INDEX
    ON MBMONITOR.NOTIFICATION_EVENT_TARGET (TYPE) TABLESPACE INDEXS
/
CREATE TABLE MBMONITOR.EXECUTION_API_CONFIG
(
    ID              VARCHAR2(50)       NOT NULL,
    EXECUTION_ID    VARCHAR2(50)       NOT NULL,
    URL             CLOB               NOT NULL,
    METHOD          VARCHAR2(10)       NOT NULL,
    HTTP_VERSION    VARCHAR2(20),
    ENABLE_SSL      NUMBER,
    BODY_TYPE       VARCHAR2(30),
    CONTENT_TYPE    VARCHAR2(50),
    BODY_RAW        CLOB,
    AUTH_TYPE       VARCHAR2(30),
    AUTH_TOKEN      CLOB,
    USERNAME        VARCHAR2(500 CHAR),
    PASSWORD        VARCHAR2(500 CHAR),
    HEADERS         CLOB,
    PARAMS          CLOB,
    BODY_URLENCODED CLOB,
    CREATED_DATE    TIMESTAMP(6),
    CREATED_BY      VARCHAR2(255),
    MODIFIED_DATE   TIMESTAMP(6),
    MODIFIED_BY     VARCHAR2(255),
    PRIMARY KEY (ID)
)
/
CREATE INDEX MBMONITOR.EXECUTION_API_CONFIG_INDEX
    ON MBMONITOR.EXECUTION_API_CONFIG (EXECUTION_ID) TABLESPACE INDEXS
/

ALTER TABLE MBMONITOR.SYS_USER ADD PASSWORD VARCHAR2(255);
/