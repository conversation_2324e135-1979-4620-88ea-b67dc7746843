package vn.com.mbbank.kanban.mbmonitor.common.utils.condition;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionCombinatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.OperatorEnum;

/**
 * Rule converter json string into object RuleGroupType using FastJSON.
 */
public class RuleConverterUtils {

  /**
   * Function to convert JSON string into RuleGroupType object.
   *
   * @param jsonString JSON string to convert
   * @return RuleGroupType object
   */
  public static RuleGroupType convertStringToRuleGroupType(String jsonString) throws Exception {
    JSONObject jsonObject = JSON.parseObject(jsonString);
    JSONArray rulesArray = jsonObject.getJSONArray("rules");
    if (Objects.isNull(rulesArray)) {
      throw new IllegalArgumentException("The 'rules' field is missing or null in the provided JSON string.");
    }
    ConditionCombinatorEnum combinator = getCombinator(jsonObject);
    List<RuleElement> rules = getRules(rulesArray);
    return new RuleGroupType(combinator, rules);
  }

  /**
   * Function to get ConditionCombinator.
   *
   * @param jsonObject JSON object to convert
   * @return RuleGroupType object
   */
  public static ConditionCombinatorEnum getCombinator(JSONObject jsonObject) {
    return ConditionCombinatorEnum.valueOf(jsonObject.getString("combinator"));
  }

  /**
   * Function to get RuleGroupType object.
   *
   * @param rulesArray JSONArray to convert
   * @return RuleGroupType object
   */
  public static List<RuleElement> getRules(JSONArray rulesArray) throws Exception {
    List<RuleElement> rules = new ArrayList<>();
    for (int i = 0; i < rulesArray.size(); i++) {
      JSONObject ruleObject = rulesArray.getJSONObject(i);
      if (ruleObject.containsKey("field")) {
        rules.add(createRuleCondition(ruleObject));
      } else if (ruleObject.containsKey("combinator")) {
        rules.add(convertStringToRuleGroupType(ruleObject.toJSONString()));
      }
    }
    return rules;
  }

  /**
   * Function to create RuleCondition object.
   *
   * @param ruleObject JSONObject to convert
   * @return RuleCondition object
   */
  public static RuleCondition<?> createRuleCondition(JSONObject ruleObject) {
    String field = ruleObject.getString("field");
    OperatorEnum operator = OperatorEnum.valueOf(ruleObject.getString("operator"));
    Object value = ruleObject.get("value");
    return new RuleCondition<>(field, operator, value);
  }
}
