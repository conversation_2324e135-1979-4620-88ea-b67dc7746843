package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;

@Entity
@Data
@Table(name = TableName.ALERT_GROUP)
@EqualsAndHashCode(callSuper = true)
public class AlertGroupEntity extends BaseEntity<Long> {

  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "ALERT_GROUP_SEQ", sequenceName = "ALERT_GROUP_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ALERT_GROUP_SEQ")
  private Long id;

  @Column(name = "PRIMARY_ALERT_ID")
  private Long primaryAlertId;

  @Column(name = "MATCH_VALUE")
  private String matchValue;

  @Column(name = "ALERT_GROUP_CONFIG_ID", nullable = false)
  private Long alertGroupConfigId;

  @Column(name = "STATUS", nullable = false)
  @Enumerated(EnumType.STRING)
  private AlertGroupStatusEnum status;

  @Column(name = "SERVICE_ID", nullable = false)
  private String serviceId;

  @Column(name = "APPLICATION_ID", nullable = false)
  private String applicationId;

  @Override
  public Long getId() {
    return id;
  }
}
