package vn.com.mbbank.kanban.mbmonitor.common.dtos.models;

import java.util.List;
import lombok.Getter;
import lombok.Setter;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysPermissionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysRoleEntity;


/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 9/23/2024
 */
@Getter
@Setter
public class SysRoleWithPermissionsModel extends SysRoleEntity {
  private Long userId;
  private List<SysPermissionEntity> permissions;
}
