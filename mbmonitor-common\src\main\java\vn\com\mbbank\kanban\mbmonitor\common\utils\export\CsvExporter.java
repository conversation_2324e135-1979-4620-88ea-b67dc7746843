package vn.com.mbbank.kanban.mbmonitor.common.utils.export;

import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AttributeInfoDto;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportFileDto;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 09/27/2024
 */
public class CsvExporter implements FileExporter {
  private static final String UTF8_BOM = "\uFEFF";
  private OutputStreamWriter writer;
  private CSVPrinter csvPrinter;

  @Override
  public <T> StreamingResponseBody export(List<T> data, ExportFileDto exportFileDto) throws IOException {
    return outputStream -> {
      init(exportFileDto, outputStream);
      writeBatch(data, exportFileDto, outputStream);
      close(outputStream);
    };
  }

  @Override
  public void init(ExportFileDto exportFileDto, OutputStream outputStream) throws IOException {
    writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
    writer.write(UTF8_BOM);
    csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT);
    printerTitle(exportFileDto.getTitle(), csvPrinter);
    List<AttributeInfoDto> sortedAttributes = exportFileDto.getAttributes().stream()
        .sorted(Comparator.comparingLong(AttributeInfoDto::getPosition))
        .collect(Collectors.toList());
    printerHeader(sortedAttributes, csvPrinter);
    csvPrinter.flush();
  }

  @Override
  public <T> void writeBatch(List<T> batchData, ExportFileDto exportFileDto,
                             OutputStream outputStream) throws IOException {
    var transformedData = ExcelUtils.transformDataExport(batchData,
        exportFileDto.getAttributes());
    for (List<AttributeInfoDto> rowData : transformedData) {
      printerRow(rowData, csvPrinter);
    }
    csvPrinter.flush();
  }

  @Override
  public void close(OutputStream outputStream) throws IOException {
    csvPrinter.flush();
    csvPrinter.close();
    writer.close();
  }

  /**
   * Print data title.
   *
   * @param titles     title list info
   * @param csvPrinter csv print
   */
  public static void printerTitle(List<String> titles, CSVPrinter csvPrinter) throws IOException {
    if (Objects.nonNull(titles) && !titles.isEmpty()) {
      for (String title : titles) {
        csvPrinter.printRecord(title);
      }
      csvPrinter.println();
    }
  }

  /**
   * Print data to header.
   *
   * @param attributeInfoClone attribute info
   * @param csvPrinter         csv print
   */
  public static void printerHeader(List<AttributeInfoDto> attributeInfoClone, CSVPrinter csvPrinter)
      throws IOException {
    for (AttributeInfoDto attributeInfoDto : attributeInfoClone) {
      csvPrinter.print(attributeInfoDto.getAttributeName());
    }
    csvPrinter.println();
  }

  /**
   * Print data to row.
   *
   * @param rowData    attribute info
   * @param csvPrinter csv print
   */
  public static void printerRow(List<AttributeInfoDto> rowData, CSVPrinter csvPrinter) throws IOException {
    for (AttributeInfoDto attribute : rowData) {
      csvPrinter.print(attribute.getValue());
    }
    csvPrinter.println();
  }

}