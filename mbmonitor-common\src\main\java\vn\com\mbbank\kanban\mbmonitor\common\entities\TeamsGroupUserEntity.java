package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.KanbanAutoGenerateUlId;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/16/2025
 */
@Entity
@Table(name = "TEAMS_USER_CONFIG")
@Data
@KanbanAutoGenerateUlId
public class TeamsGroupUserEntity extends BaseEntity<String> {

  @Id
  @Column(name = "ID")
  private String id;

  @Column(name = "TEAMS_GROUP_ID")
  private String teamsGroupId;

  @Column(name = "TEAMS_GROUP_CHAT_ID")
  private String teamsGroupChatId;

  @Column(name = "TEAMS_USER_ID")
  private String teamsUserId;

  @Column(name = "EMAIL")
  private String email;
}
