package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/13/2024
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SampleDataRequest {
  @NotNull
  private Long connectionId;
  @NotNull
  private String sqlCommand;

  @NotNull
  private String createdDateField;

}
