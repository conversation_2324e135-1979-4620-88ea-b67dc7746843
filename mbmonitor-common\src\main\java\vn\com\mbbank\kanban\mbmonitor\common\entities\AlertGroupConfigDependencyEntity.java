package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupConfigDependencyTypeEnum;

@Entity
@Data
@EqualsAndHashCode(callSuper = true, exclude = {"id"})
@Table(name = TableName.ALERT_GROUP_CONFIG_DEPENDENCY)
public class AlertGroupConfigDependencyEntity extends BaseEntity<Long> {

  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "ALERT_GROUP_CONFIG_DEPENDENCY_SEQ",
      sequenceName = "ALERT_GROUP_CONFIG_DEPENDENCY_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ALERT_GROUP_CONFIG_DEPENDENCY_SEQ")
  private Long id;

  @Column(name = "DEPENDENCE_ID", nullable = false)
  private String dependencyId;

  @Column(name = "ALERT_GROUP_CONFIG_ID", nullable = false)
  private Long alertGroupConfigId;

  @Column(name = "TYPE", nullable = false)
  @Enumerated(EnumType.STRING)
  private AlertGroupConfigDependencyTypeEnum type;

}
