package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import vn.com.mbbank.kanban.core.entities.core.BaseSoftEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;

@Data
@Entity
@Table(name = TableName.ALERT_PRIORITY_CONFIG)
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlertPriorityConfigEntity extends BaseSoftEntity<Long> {

  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "ALERT_PRIORITY_CONFIG_SEQ", sequenceName = "ALERT_PRIORITY_CONFIG_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ALERT_PRIORITY_CONFIG_SEQ")
  private Long id;

  @Column(name = "NAME", nullable = false)
  private String name;

  @Column(name = "COLOR", nullable = false)
  private String color;

  @Column(name = "POSITION")
  private Integer position;

  @Override
  public Long getId() {
    return id;
  }
}
