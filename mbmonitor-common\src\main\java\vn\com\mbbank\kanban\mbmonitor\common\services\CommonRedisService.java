package vn.com.mbbank.kanban.mbmonitor.common.services;

import java.util.List;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 1/7/2025
 */
public interface CommonRedisService {
  /**
   * Get value by key.
   *
   * @param key key
   * @param clazz clazz
   * @return clazz
   * @param <T> clazz
   */
  <T> T get(String key, Class<T> clazz);

  /**
   * Get by prefix.
   *
   * @param prefix prefix
   * @param clazz clazz
   * @return List data
   * @param <T> clazz
   */
  <T> List<T> getByPrefix(String prefix, Class<T> clazz);

  /**
   * Get value by key with default value.
   *
   * @param key key
   * @param defaultValue defaultValue
   * @param clazz clazz
   * @return data
   * @param <T> clazz
   */
  <T> T get(String key, T defaultValue, Class<T> clazz);

  /**
   * Save data into redis.
   *
   * @param key key
   * @param data data
   */
  void save(String key, Object data);

  /**
   * save data redis with timeout.
   *
   * @param key key
   * @param data key
   * @param timeOutMs timeOutMs
   */
  void save(String key, Object data, Long timeOutMs);


  /**
   * delete redis by key.
   *
   * @param key key
   * @return true/false
   */
  boolean delete(String key);

  /**
   * delete redis by prefix key.
   *
   * @param prefixKey key
   * @return total record delete
   */
  Long deleteByPrefixKey(String prefixKey);

  /**
   * Check exists key.
   *
   * @param key key
   * @return true/false
   */
  boolean isExistsByKey(String key);

  /**
   * check  config redis available.
   *
   * @return true/false
   */
  boolean isConnected();


}
