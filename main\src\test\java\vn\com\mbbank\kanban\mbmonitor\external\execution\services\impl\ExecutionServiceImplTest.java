package vn.com.mbbank.kanban.mbmonitor.external.execution.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;

import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.task.TaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecuteScriptParamModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecutionScriptRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionScriptResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.external.execution.services.ExecutionHistoryService;

/**
 * Unit tests for ExecutionServiceImpl.
 */
@ExtendWith(MockitoExtension.class)
class ExecutionServiceImplTest {

  @Test
  void execute_success_withValidRequest() {
    // Arrange
    ExecutionHistoryService executionHistoryService = mock(ExecutionHistoryService.class);
    TaskExecutor taskExecutor = mock(TaskExecutor.class);

    TestExecutionServiceImpl executionService = new TestExecutionServiceImpl(executionHistoryService);
    ReflectionTestUtils.setField(executionService, "executor", taskExecutor);
    ReflectionTestUtils.setField(executionService, "maxOutputSize", 1048576);
    ReflectionTestUtils.setField(executionService, "executeTimeOut", 5);

    ExecutionScriptRequest request = createTestRequest();
    ExecutionScriptResponse expectedResponse = new ExecutionScriptResponse();
    expectedResponse.setStatus(ExecutionStatusEnum.COMPLETED);

    // Use spy to avoid actual process execution
    TestExecutionServiceImpl spyService = spy(executionService);
    doReturn(expectedResponse).when(spyService).runExecution(request);

    // Act
    ExecutionScriptResponse response = spyService.execute(request);

    // Assert
    assertEquals(expectedResponse, response);
    verify(spyService).runExecution(request);
  }

  @Test
  void executeAsync_success_withValidRequest() {
    // Arrange
    ExecutionHistoryService executionHistoryService = mock(ExecutionHistoryService.class);
    TaskExecutor taskExecutor = mock(TaskExecutor.class);

    TestExecutionServiceImpl executionService = new TestExecutionServiceImpl(executionHistoryService);
    ReflectionTestUtils.setField(executionService, "executor", taskExecutor);
    ReflectionTestUtils.setField(executionService, "maxOutputSize", 1048576);
    ReflectionTestUtils.setField(executionService, "executeTimeOut", 5);

    ExecutionScriptRequest request = createTestRequest();

    // Act
    executionService.executeAsync(request);

    // Assert
    verify(taskExecutor).execute(any(Runnable.class));
  }

  /**
   * Creates a test request for use in tests.
   */
  private ExecutionScriptRequest createTestRequest() {
    ExecutionScriptRequest request = new ExecutionScriptRequest();
    request.setName("Test Script");
    request.setDescription("Test Description");
    request.setExecutionBy("Test User");
    request.setScript("print('Hello, World!')");

    List<ExecuteScriptParamModel> params = new ArrayList<>();
    ExecuteScriptParamModel param = new ExecuteScriptParamModel();
    param.setName("TEST_PARAM");
    param.setValue("test_value");
    params.add(param);

    request.setParams(params);
    return request;
  }

  /**
   * Concrete implementation of the abstract class for testing.
   */
  private static class TestExecutionServiceImpl extends ExecutionServiceImpl {
    public TestExecutionServiceImpl(ExecutionHistoryService executionHistoryService) {
      super(executionHistoryService);
    }

    public void stop(String executionHistoryId) {
      // Implementation for testing
      if (executionHistoryId == null || executionHistoryId.isEmpty()) {
        throw new IllegalArgumentException("Execution history ID cannot be null or empty");
      }
    }
  }
}
