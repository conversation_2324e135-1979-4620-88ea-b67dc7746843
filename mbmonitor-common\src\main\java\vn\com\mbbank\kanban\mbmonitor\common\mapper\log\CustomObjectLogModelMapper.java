package vn.com.mbbank.kanban.mbmonitor.common.mapper.log;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.CustomObjectLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CustomObjectLogModelMapper extends
    KanbanBaseMapper<CustomObjectLogModel, CustomObjectEntity> {
  CustomObjectLogModelMapper INSTANCE = Mappers.getMapper(CustomObjectLogModelMapper.class);
}
