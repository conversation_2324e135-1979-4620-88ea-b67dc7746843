package vn.com.mbbank.kanban.mbmonitor.common.dtos.models;

import jakarta.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/24/2025
 */
@Data
public class TeamsSendModel {
  private String groupId;
  private String groupName;
  private List<@NotBlank String> contacts;
  private Boolean isSend;
  private List<String> invalidEmails = new ArrayList<>();
  @NotBlank
  private String message;
}
