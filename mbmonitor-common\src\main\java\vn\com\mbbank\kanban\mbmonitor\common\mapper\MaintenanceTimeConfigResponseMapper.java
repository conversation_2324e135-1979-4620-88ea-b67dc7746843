package vn.com.mbbank.kanban.mbmonitor.common.mapper;

import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.MaintenanceTimeConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigEntity;

/**
 * Mapper logic MaintenanceTimeConfigResponseMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MaintenanceTimeConfigResponseMapper extends
    KanbanBaseMapper<MaintenanceTimeConfigResponse, MaintenanceTimeConfigEntity> {
  MaintenanceTimeConfigResponseMapper INSTANCE = Mappers.getMapper(MaintenanceTimeConfigResponseMapper.class);


  /**
   * map from AlertPriorityConfigEntity to AlertPriorityConfigResponse.
   *
   * @param entity         MaintenanceTimeConfigEntity.
   * @param serviceIds     list of service id
   * @param applicationIds list of application id
   * @return MaintenanceTimeConfigResponse
   */
  default MaintenanceTimeConfigResponse map(MaintenanceTimeConfigEntity entity,
                                            List<String> serviceIds,
                                            List<String> applicationIds) {

    var maintenanceTimeConfigResponse = this.map(entity);
    maintenanceTimeConfigResponse.setServiceIds(serviceIds);
    maintenanceTimeConfigResponse.setApplicationIds(applicationIds);
    return maintenanceTimeConfigResponse;
  }
}
