package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import vn.com.mbbank.kanban.core.annotations.KanbanDisableColumnSearch;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConfigCollectMapTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/12/2024
 */
@Getter
@Setter
@Entity
@Table(name = "DATABASE_COLLECT")
@DynamicInsert
public class DatabaseCollectEntity extends BaseEntity<Long> {
  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "DATABASE_COLLECT_SEQ", sequenceName = "DATABASE_COLLECT_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "DATABASE_COLLECT_SEQ")
  private Long id;

  @Column(name = "NAME")
  private String name;

  @Column(name = "DESCRIPTION")
  private String description;

  private Long connectionId;
  private String sqlCommand;
  private String createdDateField;
  private String alertIdField;
  private Long interval;
  private ConfigCollectMapTypeEnum serviceNameType;
  private String serviceId;
  private String serviceMapValue;
  private ConfigCollectMapTypeEnum applicationType;
  private String applicationId;
  private String applicationMapValue;
  private String alertMapValue;
  private ConfigCollectMapTypeEnum priorityType;
  private Long priorityId;
  private String priorityMapValue;
  private ConfigCollectMapTypeEnum contactType;
  private String contactMapValue;
  private String contactCustomValue;
  private Boolean isActive;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  @Access(AccessType.PROPERTY)
  @Basic
  @Column(name = "CONNECTION_ID")
  @KanbanDisableColumnSearch
  public Long getConnectionId() {
    return connectionId;
  }

  public void setConnectionId(Long connectionId) {
    this.connectionId = connectionId;
  }

  @Access(AccessType.PROPERTY)
  @Basic
  @Column(name = "SQL_COMMAND")
  @KanbanDisableColumnSearch
  public String getSqlCommand() {
    return sqlCommand;
  }

  public void setSqlCommand(String sqlCommand) {
    this.sqlCommand = sqlCommand;
  }

  @Access(AccessType.PROPERTY)
  @Basic
  @Column(name = "CREATED_DATE_FIELD")
  @KanbanDisableColumnSearch
  public String getCreatedDateField() {
    return createdDateField;
  }

  public void setCreatedDateField(String createdDateField) {
    this.createdDateField = createdDateField;
  }

  @Access(AccessType.PROPERTY)
  @Basic
  @Column(name = "ALERT_ID_FIELD")
  @KanbanDisableColumnSearch
  public String getAlertIdField() {
    return alertIdField;
  }

  public void setAlertIdField(String alertIdField) {
    this.alertIdField = alertIdField;
  }

  @Access(AccessType.PROPERTY)
  @Basic
  @Column(name = "INTERVAL")
  @KanbanDisableColumnSearch
  public Long getInterval() {
    return interval;
  }

  public void setInterval(Long interval) {
    this.interval = interval;
  }

  @Access(AccessType.PROPERTY)
  @Basic
  @Column(name = "SERVICE_NAME_TYPE")
  @KanbanDisableColumnSearch
  @Enumerated(EnumType.STRING)
  public ConfigCollectMapTypeEnum getServiceNameType() {
    return serviceNameType;
  }

  public void setServiceNameType(
      ConfigCollectMapTypeEnum serviceNameType) {
    this.serviceNameType = serviceNameType;
  }

  @Access(AccessType.PROPERTY)
  @Basic
  @Column(name = "SERVICE_ID")
  @KanbanDisableColumnSearch
  public String getServiceId() {
    return serviceId;
  }

  public void setServiceId(String serviceId) {
    this.serviceId = serviceId;
  }

  @Access(AccessType.PROPERTY)
  @Basic
  @Column(name = "SERVICE_MAP_VALUE")
  @KanbanDisableColumnSearch
  public String getServiceMapValue() {
    return serviceMapValue;
  }

  public void setServiceMapValue(String serviceMapValue) {
    this.serviceMapValue = serviceMapValue;
  }

  @Column(name = "APPLICATION_TYPE")
  @Enumerated(EnumType.STRING)
  @Access(AccessType.PROPERTY)
  @Basic
  @KanbanDisableColumnSearch
  public ConfigCollectMapTypeEnum getApplicationType() {
    return applicationType;
  }

  public void setApplicationType(
      ConfigCollectMapTypeEnum applicationType) {
    this.applicationType = applicationType;
  }

  @Column(name = "APPLICATION_ID")
  @KanbanDisableColumnSearch
  @Access(AccessType.PROPERTY)
  @Basic
  public String getApplicationId() {
    return applicationId;
  }

  public void setApplicationId(String applicationId) {
    this.applicationId = applicationId;
  }

  @Access(AccessType.PROPERTY)
  @Basic
  @KanbanDisableColumnSearch
  @Column(name = "APPLICATION_MAP_VALUE")
  public String getApplicationMapValue() {
    return applicationMapValue;
  }

  public void setApplicationMapValue(String applicationMapValue) {
    this.applicationMapValue = applicationMapValue;
  }

  @Column(name = "ALERT_MAP_VALUE")
  @Access(AccessType.PROPERTY)
  @Basic
  @KanbanDisableColumnSearch
  public String getAlertMapValue() {
    return alertMapValue;
  }

  public void setAlertMapValue(String alertMapValue) {
    this.alertMapValue = alertMapValue;
  }


  @Column(name = "PRIORITY_TYPE")
  @Access(AccessType.PROPERTY)
  @Basic
  @Enumerated(EnumType.STRING)
  @KanbanDisableColumnSearch
  public ConfigCollectMapTypeEnum getPriorityType() {
    return priorityType;
  }

  public void setPriorityType(
      ConfigCollectMapTypeEnum priorityType) {
    this.priorityType = priorityType;
  }

  @Access(AccessType.PROPERTY)
  @Basic
  @Column(name = "PRIORITY_ID")
  @KanbanDisableColumnSearch
  public Long getPriorityId() {
    return priorityId;
  }

  public void setPriorityId(Long priorityId) {
    this.priorityId = priorityId;
  }

  @Column(name = "PRIORITY_MAP_VALUE")
  @Access(AccessType.PROPERTY)
  @Basic
  @KanbanDisableColumnSearch
  public String getPriorityMapValue() {
    return priorityMapValue;
  }

  public void setPriorityMapValue(String priorityMapValue) {
    this.priorityMapValue = priorityMapValue;
  }

  @Column(name = "CONTACT_TYPE")
  @Enumerated(EnumType.STRING)
  @Access(AccessType.PROPERTY)
  @Basic
  @KanbanDisableColumnSearch
  public ConfigCollectMapTypeEnum getContactType() {
    return contactType;
  }

  public void setContactType(
      ConfigCollectMapTypeEnum contactType) {
    this.contactType = contactType;
  }

  @Column(name = "CONTACT_MAP_VALUE")
  @Access(AccessType.PROPERTY)
  @Basic
  @KanbanDisableColumnSearch
  public String getContactMapValue() {
    return contactMapValue;
  }

  public void setContactMapValue(String contactMapValue) {
    this.contactMapValue = contactMapValue;
  }


  @Access(AccessType.PROPERTY)
  @Basic
  @Column(name = "CONTACT_CUSTOM_VALUE")
  @KanbanDisableColumnSearch
  public String getContactCustomValue() {
    return contactCustomValue;
  }

  public void setContactCustomValue(String contactCustomValue) {
    this.contactCustomValue = contactCustomValue;
  }

  @Access(AccessType.PROPERTY)
  @Basic
  @Column(name = "IS_ACTIVE")
  @KanbanDisableColumnSearch
  public Boolean getIsActive() {
    return isActive;
  }

  public void setIsActive(Boolean active) {
    isActive = active;
  }
}
