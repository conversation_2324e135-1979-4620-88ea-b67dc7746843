package vn.com.mbbank.kanban.mbmonitor.common.configs.caches;

import jakarta.persistence.PostRemove;
import jakarta.persistence.PostUpdate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysPermissionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonRedisService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.RedisUtils;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 1/16/2025
 */
@Component
public class SysPermissionEntityCacheListener {
  @Autowired
  private CommonRedisService commonRedisService;

  /**
   * trigger when edit, remove data table SysUserRoleEntity.
   *
   * @param entity entity
   */
  @PostUpdate
  @PostRemove
  public void clearCache(SysPermissionEntity entity) {
    commonRedisService.deleteByPrefixKey(RedisUtils.MONITOR_USER_RESPONSE_PREFIX);
  }
}
