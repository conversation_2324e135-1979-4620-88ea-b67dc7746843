package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.KanbanAutoGenerateUlId;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertRequestSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertRequestStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionOperatorEnum;


@Getter
@Setter
@Entity
@Table(name = TableName.ALERT_REQUEST)
@DynamicInsert
@KanbanAutoGenerateUlId
public class AlertRequestEntity extends BaseEntity<String> {

  @Id
  @Column(name = "ID")
  private String id;

  @Column(name = "SERVICE_ID")
  private String serviceId;

  @Column(name = "APPLICATION_ID")
  private String applicationId;

  @Column(name = "SOURCE_TYPE")
  @Enumerated(EnumType.STRING)
  private AlertRequestSourceTypeEnum sourceType;

  @Column(name = "STATUS")
  @Enumerated(EnumType.STRING)
  private AlertRequestStatusEnum status;

  @Column(name = "CRON_TIME")
  private String cronTime;

  @Column(name = "CONTENT")
  private String content;

  @Column(name = "CONTENT_JSON")
  private String contentJson;

  @Column(name = "RECIPIENT")
  private String recipient;

  @Column(name = "PRIORITY_ID")
  private Long priorityId;

  @Column(name = "CONDITION_OPERATOR")
  @Enumerated(EnumType.STRING)
  private ConditionOperatorEnum conditionOperator;

  @Column(name = "CONDITION_VALUE")
  private Long conditionValue;

  @Column(name = "APPROVED_BY")
  private String approvedBy;

  @Column(name = "APPROVED_DATE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date approvedDate;

  @Column(name = "REJECTED_REASON")
  private String rejectedReason;

}