package vn.com.mbbank.kanban.mbmonitor.common.entities;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import org.hibernate.annotations.DynamicInsert;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.configs.caches.SysUserEntityCacheListener;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;

@Entity
@Table(name = TableName.SYS_USER)
@DynamicInsert
@EntityListeners(SysUserEntityCacheListener.class)
public class SysUserEntity extends BaseEntity<Long> {

  private Long id;
  private String userName;
  private String email;
  private Boolean isActive;
  private Boolean isAdmin;
  private String password;

  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "SYS_USER_SEQ", sequenceName = "SYS_USER_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SYS_USER_SEQ")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  @Column(name = "USERNAME")
  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }

  @Column(name = "EMAIL")
  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  @Column(name = "IS_ACTIVE")
  public Boolean getIsActive() {
    return isActive;
  }

  public void setIsActive(Boolean isActive) {
    this.isActive = isActive;
  }

  @Column(name = "IS_ADMIN")
  public Boolean getIsAdmin() {
    return isAdmin;
  }

  public void setIsAdmin(Boolean admin) {
    isAdmin = admin;
  }

  @Column(name = "PASSWORD")
  @JsonIgnore
  public String getPassword() {
    return password;
  }

  public void setPassword(String password) {
    this.password = password;
  }
}
