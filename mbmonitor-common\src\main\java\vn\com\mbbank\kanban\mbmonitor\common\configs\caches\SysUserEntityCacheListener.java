package vn.com.mbbank.kanban.mbmonitor.common.configs.caches;

import jakarta.persistence.PostRemove;
import jakarta.persistence.PostUpdate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysUserEntity;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonRedisService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.RedisUtils;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 1/8/2025
 */
@Component
public class SysUserEntityCacheListener {
  @Autowired
  private CommonRedisService commonRedisService;

  /**
   * Trigger when entity manager query for update or delete data in table SysUserEntity.
   *
   * @param entity entity
   */
  @PostUpdate
  @PostRemove
  public void clearCache(SysUserEntity entity) {
    //reset case
    commonRedisService.delete(RedisUtils.getUserResponseKey(entity.getUserName()));
  }
}
