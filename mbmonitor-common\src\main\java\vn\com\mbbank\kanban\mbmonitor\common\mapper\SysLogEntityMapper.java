package vn.com.mbbank.kanban.mbmonitor.common.mapper;

import java.util.Date;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.SysLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysLogEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 17/12/2024
 */

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SysLogEntityMapper extends KanbanBaseMapper<SysLogEntity, SysLogModel> {
  SysLogEntityMapper INSTANCE = Mappers.getMapper(SysLogEntityMapper.class);

  /**
   * map SysLogEntity.
   *
   * @param model SysLogModel
   * @return SysLogEntity
   */
  @Mapping(target = "logDate", expression = "java(mapLogDate(model.getLogDate()))")
  SysLogEntity map(SysLogModel model);

  /**
   * map String to Date.
   *
   * @param logDate string log date
   * @return Date
   */
  default Date mapLogDate(String logDate) {
    return DateUtils.convertStringToDate(logDate, DateUtils.FORMAT_DDMMYYYY_HHMMSS);
  }
}
