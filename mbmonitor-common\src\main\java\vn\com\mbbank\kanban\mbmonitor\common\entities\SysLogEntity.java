package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.converter.SysLogMessageConverter;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.SysLogMessageModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogFunctionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;

@Entity
@Data
@Table(name = TableName.SYS_LOG)
@EqualsAndHashCode(callSuper = true)
public class SysLogEntity extends BaseEntity<String> {

  @Id
  @Column(name = "ID")
  private String id = GeneratorUtil.generateId();

  @Column(name = "FUNCTION")
  @Enumerated(EnumType.STRING)
  private LogFunctionEnum function;

  @Column(name = "ACTION")
  @Enumerated(EnumType.STRING)
  private LogActionEnum action;

  @Column(name = "LOG_BY")
  private String logBy;

  @Column(name = "LOG_DATE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date logDate;

  @Column(name = "MESSAGE")
  @Convert(converter = SysLogMessageConverter.class)
  private SysLogMessageModel message;

  @Override
  public String getId() {
    return id;
  }
}