package vn.com.mbbank.kanban.mbmonitor.common.services.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.mapper.KanbanMapperUtils;
import vn.com.mbbank.kanban.core.services.KanbanRedisService;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanJsonUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.TeamsConfigModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.TeamsGroupChatModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.TeamsTokenModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonTeamsService;
import vn.com.mbbank.kanban.mbmonitor.common.services.RestApiService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.RedisUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.RestTemplateFactoryUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.TeamsUtils;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/14/2025
 */
@RequiredArgsConstructor
@Service
public class CommonTeamsServiceImpl implements CommonTeamsService {
  private Logger logger = LoggerFactory.getLogger(this.getClass());
  private final KanbanRedisService kanbanRedisService;
  private final RestApiService restApiService;
  private TeamsConfigModel teamsConfig;
  private String redisTokenKey;
  @Value("${mbmonitor.teams.proxy.ip}")
  private String proxyTeamsIp;

  @Value("${mbmonitor.teams.proxy.port}")
  private Integer proxyTeamsPort;

  private static final String GRAPH_BASE_URL = "https://graph.microsoft.com/v1.0/";
  private static final String DEFAULT_CHAT_URL =
      GRAPH_BASE_URL + "chats?$filter=chatType eq 'group'&$expand=members&$top=50";

  private static final String LOGIN_BASE_URL =
      "https://login.microsoftonline.com/%s/oauth2/v2.0/token";

  private static final String USER_BASE_URL = "https://graph.microsoft.com/v1.0/users/";

  @Override
  public void init(TeamsConfigModel teamsConfig) {
    restApiService.setIsErrorHandler(false);
    if (!KanbanCommonUtil.isEmpty(proxyTeamsIp) && !KanbanCommonUtil.isEmpty(proxyTeamsPort)) {
      restApiService.setRequestFactory(
          RestTemplateFactoryUtils.settingProxy(proxyTeamsIp, proxyTeamsPort));
    }

    this.redisTokenKey = RedisUtils.getTeamsTokenRedisKey(teamsConfig.getClientId(), teamsConfig.getEmail());
    this.teamsConfig = teamsConfig;
  }

  @Override
  public String getToken() {

    try {
      if (kanbanRedisService.isExistsByKey(redisTokenKey)) {
        logger.info("get token from redis");
        return kanbanRedisService.get(redisTokenKey, null, String.class);
      }
      var tokenModel = getTokenFromTeams();
      if (!KanbanCommonUtil.isEmpty(tokenModel)) {
        kanbanRedisService.save(redisTokenKey, tokenModel.getToken(),
            (tokenModel.getExpTime() - 120) * 1000);
        return tokenModel.getToken();
      }
      return null;
    } catch (Exception e) {
      logger.error("get token false ", e);
      return null;
    }
  }

  @Override
  public ResponseEntity<String> sendMessage(String message, String chatId) {
    Map<String, String> mapContent = Map.of("content", message, "contentType", "text");
    Map<String, Map<String, String>> mapBody = Map.of("body", mapContent);
    String messageContent = KanbanMapperUtils.objectToJson(mapBody);
    return sendMessageWithBody(messageContent, chatId);

  }

  @Override
  public ResponseEntity<String> sendMessageWithBody(String body, String chatId) {
    if (KanbanCommonUtil.isEmpty(teamsConfig)) {
      logger.error("Teams config not exists");
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body("Teams configuration is missing");
    }

    try {
      String endpoint = GRAPH_BASE_URL + "chats/" + chatId + "/messages";
      // Thiết lập HTTP headers
      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_JSON);
      headers.setBearerAuth(getToken());
      var response = restApiService.post(endpoint, body, headers, String.class);

      logger.info("Message sent to Teams chat. Status: {}", response.getStatusCode());

      return response;
    } catch (Exception e) {
      logger.error("Error sending message to Teams: {}", e.getMessage(), e);
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body("Failed to send message: " + e.getMessage());
    }
  }


  @Override
  public TeamsGroupChatModel createGroupChat(List<String> users, Boolean isSkipEmailInValid)
      throws BusinessException {
    String token = getToken();
    if (KanbanCommonUtil.isEmpty(token)) {
      logger.info("Unable to obtain token for Teams API");
      throw new BusinessException(ErrorCode.TEAMS_CONFIG_DATA_INVALID);
    }
    var teamsGroupChatModel  = createChatWithMembers(users, token, isSkipEmailInValid);
    return teamsGroupChatModel;
  }

  @Override
  public List<TeamsGroupChatModel> getAllGroupChat() {
    List<TeamsGroupChatModel> result = new ArrayList<>();
    try {
      logger.info("Starting to fetch group chats with URL: {}", DEFAULT_CHAT_URL);
      fetchChatsRecursively(DEFAULT_CHAT_URL, result);
      logger.info("Successfully fetched {} group chats", result.size());
    } catch (Exception e) {
      logger.error("Error fetching group chats: {}", e.getMessage(), e);
      throw new RuntimeException("Failed to fetch group chats: " + e.getMessage(), e);
    }
    return result;
  }

  /**
   * Get all group chat info.
   *
   * @param url    url
   * @param result result
   * @throws Exception ex
   */
  protected void fetchChatsRecursively(String url, List<TeamsGroupChatModel> result)
      throws Exception {
    var token = getToken();
    // Sử dụng URL mặc định nếu url rỗng
    String requestUrl = (url == null || url.trim().isEmpty()) ? DEFAULT_CHAT_URL : url;
    logger.info("Fetching chats from URL: {}", requestUrl);

    // Cấu hình headers
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setBearerAuth(token);

    // Gửi yêu cầu GET
    ResponseEntity<String> response = restApiService.get(requestUrl, headers, String.class);

    if (!response.getStatusCode().is2xxSuccessful() || response.getBody() == null) {
      logger.info("Failed to fetch chats from URL: {}, status: {}", requestUrl,
          response.getStatusCode());
      throw new BusinessException(ErrorCode.TEAMS_GROUP_COLLECT_ERROR);
    }
    ObjectMapper objectMapper = new ObjectMapper();
    // Parse JSON response
    JsonNode rootNode = objectMapper.readTree(response.getBody());
    JsonNode chatsArray = rootNode.path("value");

    if (!chatsArray.isArray()) {
      logger.info("No chats found in response for URL: {}", requestUrl);
      return;
    }

    // Xử lý danh sách chat
    for (JsonNode chatNode : chatsArray) {
      String chatId = chatNode.path("id").asText(null);
      String topic = chatNode.path("topic").asText(null);
      String chatType = chatNode.path("chatType").asText(null);

      if (chatId == null || !"group".equalsIgnoreCase(chatType)) {
        logger.info("Skipping non-group chat or invalid chat ID: {}", chatId);
        continue;
      }

      TeamsGroupChatModel model = new TeamsGroupChatModel();
      model.setGroupChatId(chatId);
      model.setGroupChatName(topic);

      // Xử lý danh sách thành viên
      JsonNode membersArray = chatNode.path("members");
      if (membersArray.isArray()) {
        for (JsonNode memberNode : membersArray) {
          String email = memberNode.path("email").asText(null);
          if (email != null && !email.trim().isEmpty()) {
            model.getEmailValid().add(email);
          } else {
            logger.info("Skipping member with null or empty email in chat: {}", chatId);
          }
        }
      } else {
        logger.info("No members found for chat: {}", chatId);
      }

      result.add(model);
      logger.info("Processed chat: {}, valid emails: {}, invalid emails: {}",
          chatId, model.getEmailValid().size(), model.getEmailInvalid().size());
    }

    // Kiểm tra nextLink để đệ quy
    String nextLink = rootNode.path("@odata.nextLink").asText(null);
    if (nextLink != null) {
      logger.info("Found next page, fetching: {}", nextLink);
      fetchChatsRecursively(URLDecoder.decode(nextLink, StandardCharsets.UTF_8.name()), result);
    } else {
      logger.info("No more pages to fetch");
    }
  }


  /**
   * Create chat with a list of members
   */
  TeamsGroupChatModel createChatWithMembers(List<String> users, String token,
                                            Boolean isSkipEmailInValid) {
    try {
      var usersRequest = new ArrayList<>(users);
      users = new ArrayList<>(users);
      users.add(this.teamsConfig.getEmail());
      // Configure request headers
      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_JSON);
      headers.setBearerAuth(token);

      TeamsGroupChatModel teamsGroupChatModel = new TeamsGroupChatModel();
      // Graph API endpoint for creating chats
      String graphApiUrl = GRAPH_BASE_URL + "chats";

      // Execute API request
      ResponseEntity<String> response =
          restApiService.post(graphApiUrl, createBodyCreateGroupChat(users), headers,
              String.class);
      logger.info("Create group request response", response);
      // Check result
      if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
        // Parse responese to get chat ID
        var groupChatId = KanbanJsonUtils.getStringField(response.getBody(), "id");
        teamsGroupChatModel.setGroupChatId(groupChatId);
        teamsGroupChatModel.setEmailValid(users);
        return teamsGroupChatModel;
      } else {
        if (isSkipEmailInValid) {
          users.removeAll(TeamsUtils.extractEmailsCreateGroupInvalid(response.getBody()));
          response = restApiService.post(graphApiUrl, createBodyCreateGroupChat(users), headers,
              String.class);
          if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
            // Parse responese to get chat ID
            var groupChatId = KanbanJsonUtils.getStringField(response.getBody(), "id");
            teamsGroupChatModel.setGroupChatId(groupChatId);
            teamsGroupChatModel.setEmailValid(users);
            return teamsGroupChatModel;
          } else {
            return new TeamsGroupChatModel().setEmailInvalid(users)
                .setEmailValid(new ArrayList<>());
          }
        }
        teamsGroupChatModel.setEmailInvalid(
            TeamsUtils.extractEmailsCreateGroupInvalid(response.getBody()));
        usersRequest.removeAll(teamsGroupChatModel.getEmailInvalid());
        //case response return exception message
        if (KanbanCommonUtil.isEmpty(teamsGroupChatModel.getEmailInvalid())) {
          return new TeamsGroupChatModel().setEmailInvalid(users).setEmailValid(new ArrayList<>());
        }
        teamsGroupChatModel.setEmailValid(usersRequest);
        return teamsGroupChatModel;
      }
    } catch (Exception e) {
      logger.error("Error creating chat with members", e);
      return new TeamsGroupChatModel().setEmailInvalid(users).setEmailValid(new ArrayList<>());
    }
  }

  @Override
  public TeamsTokenModel getTokenFromTeams() {
    if (KanbanCommonUtil.isEmpty(teamsConfig)) {
      return null;
    }

    String tokenUrl = String.format(LOGIN_BASE_URL,
        teamsConfig.getTenantId());

    // Prepare headers
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
    headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

    // Prepare body parameters
    MultiValueMap<String, String> formParams = new LinkedMultiValueMap<>();
    formParams.add("client_id", teamsConfig.getClientId());
    formParams.add("client_secret", teamsConfig.getClientSecret());
    formParams.add("scope", "https://graph.microsoft.com/.default");
    formParams.add("username", teamsConfig.getEmail());
    formParams.add("password", teamsConfig.getPassword());
    formParams.add("grant_type", "password");

    try {

      // Create HTTP entity
      var response = restApiService.post(tokenUrl, formParams, headers, TeamsTokenModel.class);
      if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
        return response.getBody();
      } else {
        logger.error("get token false status != 200", response);
        return null;
      }
    } catch (Exception e) {
      logger.error("get token false ", e);
      return null;
    }
  }

  /**
   * create data group or chat.
   *
   * @param users users
   * @return Data body
   */
  private String createBodyCreateGroupChat(List<String> users) {
    List<Map<String, Object>> members = new ArrayList<>();
    for (String user : users) {
      Map<String, Object> member = new HashMap<>();
      member.put("@odata.type", "#microsoft.graph.aadUserConversationMember");
      member.put("roles",
          Collections.singletonList("owner")); // You can change to "member" if needed
      member.put("<EMAIL>", USER_BASE_URL + user);
      members.add(member);
    }

    // Create request body
    Map<String, Object> requestBody = new HashMap<>();
    requestBody.put("chatType", users.size() > 2 ? "group" : "oneOnOne");
    requestBody.put("members", members);

    // Convert to JSON
    String jsonBody = KanbanJsonUtils.objectToJson(requestBody);
    return jsonBody;
  }

}
