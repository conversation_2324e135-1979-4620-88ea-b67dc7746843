package vn.com.mbbank.kanban.mbmonitor.common.integrations.outbounds.urls;

import lombok.Getter;
import lombok.Setter;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/6/2025
 */
public class TelegramUrls {

  @Getter
  @Setter
  private String botToken;

  /**
   * Get send message url.
   *
   * @return url
   */
  public String getSendMessageUrl() {
    return getUrl() + "/sendMessage";
  }

  /**
   * Get me url.
   *
   * @return url
   */
  public String getMeUrl() {
    return getUrl() + "/getMe";
  }

  /**
   * Get chat member url.
   *
   * @return url
   */
  private String getChatMember() {
    return getUrl() + "/getChatMember?chat_id=%s&user_id=%s";
  }

  /**
   * Build chat member url.
   *
   * @param groupId groupId
   * @param userId  userId
   * @return url
   */
  public String getChatMemberUrl(String groupId, String userId) {
    return String.format(getChatMember(), groupId, userId);
  }

  /**
   * Constructor.
   *
   * @param botToken botToken
   */
  public TelegramUrls(String botToken) {
    this.botToken = botToken;
  }

  /**
   * Get url.
   *
   * @return url
   */
  public String getUrl() {
    return "https://api.telegram.org/bot" + getBotToken();
  }


  /**
   * Create instance TelegramUrls.
   *
   * @param botToken botToken
   * @return TelegramUrls
   */
  public static TelegramUrls getUrl(String botToken) {
    return new TelegramUrls(botToken);
  }
}
