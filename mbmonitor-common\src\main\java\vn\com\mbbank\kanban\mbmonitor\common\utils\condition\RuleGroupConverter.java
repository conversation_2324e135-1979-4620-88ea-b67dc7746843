package vn.com.mbbank.kanban.mbmonitor.common.utils.condition;

import com.alibaba.fastjson2.JSON;
import jakarta.persistence.AttributeConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * RuleGroupConverter.
 */
public class RuleGroupConverter implements AttributeConverter<RuleGroupType, String> {
  private static final Logger logger = LoggerFactory.getLogger(RuleGroupConverter.class);

  /**
   * RuleGroupConverter.
   *
   * @param dbData rule in db.
   * @return RuleGroupType object.
   */
  public static RuleGroupType convertToRuleGroup(String dbData) {
    try {
      return RuleConverterUtils.convertStringToRuleGroupType(dbData);
    } catch (Exception e) {
      logger.warn("Convert to RuleGroupType error fallback to null");
      return null;
    }
  }

  @Override
  public String convertToDatabaseColumn(RuleGroupType ruleGroupType) {
    return JSON.toJSONString(ruleGroupType);
  }

  @Override
  public RuleGroupType convertToEntityAttribute(String dbData) {
    try {
      return RuleConverterUtils.convertStringToRuleGroupType(dbData);
    } catch (Exception e) {
      logger.warn("Convert to RuleGroupType error fallback to null");
      return null;
    }
  }
}