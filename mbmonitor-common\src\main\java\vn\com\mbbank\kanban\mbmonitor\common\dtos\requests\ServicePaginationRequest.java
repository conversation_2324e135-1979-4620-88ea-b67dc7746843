package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;

import jakarta.validation.constraints.Max;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;

/**
 * Model view list service to filter in alert table tab report.
 */

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ServicePaginationRequest extends PaginationRequestDTO {
  String name;
  Boolean withDeleted;
  @Builder.Default
  @Max(value = CommonConstants.MAX_SERVICE_EXPORT_ROWS_LIMIT, message = "Size cannot be greater than"
      + CommonConstants.MAX_SERVICE_EXPORT_ROWS_LIMIT)
  int size = 5;
}

