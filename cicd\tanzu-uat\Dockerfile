# Start with a base image containing Java runtime 
FROM k8sdev.mbbank.com.vn/support/eclipse-temurin-python-env:1.0.0

# Set workdir pointing to /app 
WORKDIR /app 

# Add argument spring profile
ARG ENV_PROFILE
ENV ENV_PROFILE=$ENV_PROFILE

# Add the application's jar to the container 
ADD main/target/*.jar app.jar 
# Make port 10592 available to the world outside this container
#EXPOSE 10592

# Create a group and user 
RUN addgroup --system --gid 10000 appadmin 
# RUN adduser --system --uid 10000 --group appadmin
RUN adduser --system appadmin --uid 10000 -g appadmin
RUN chown -R appadmin:appadmin /app 
# Set the default user. 
USER appadmin 
RUN mkdir -p /home/<USER>/logs/csp
# Run the jar file
VOLUME /app /tmp /home/<USER>/logs/csp
# ENTRYPOINT exec java -Djava.security.egd=file:/dev/./urandom -jar app.jar