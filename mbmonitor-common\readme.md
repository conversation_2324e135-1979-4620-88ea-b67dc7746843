# Cloning the Repository with Submodules

## Important:

Run `git config --global submodule.recurse`, if `result` is `true`, run:
`git config --global submodule.recurse false`

# To clone this repository along with its submodules, follow these steps:

1. `git clone <repository-url>`
2. `cd <repository-name>`
3. `git checkout <your-branch-name>`

4. Initialize and update submodules:  
   `git submodule update --init`
5. Update submodule
    - Update submodule from your branch:  
      `git pull`  
      `git submodule update --merge`
    - Update submodules from default branch develop:  
      `git submodule update --remote --merge`

6. `cd <submodule-name>`

7. `git checkout <your-branch-name>`

8. Verify Submodules
   `git submodule status`

# Update only submodule when change only submodule

1. `cd <submodule-name>`
2. `git add .`
3. `git commit -m "commit"`
4. `cd ..`

### Update submodule
- Update submodule from your branch:  
  `git pull`  
  `git submodule update --merge`

- Update submodules from default branch develop:  
  `git submodule update --remote --merge`

*Important: If you run the `git submodule update` command, it will pull the latest code from git origin and discard all
current local commits. Remember to use the `--merge` command to keep the current code + merge in*

# Resolve conflict when Conflict CommitId from Root

When working locally with the following steps:

1. `cd mbmonitor-common`
2. `git add .`
3. `git commit -m "update"`
4. `git push`
5. `cd ..`
6. `git add .`
7. `git commit -m "update"`
8. `git pull`

You might encounter a conflict error related to the `commitId` of `mbmonitor-common` (since a different version was
pushed to the root on the origin). To fix this issue, follow these steps:

1. Go to `gitlab` and navigate to the current branch.
2. Copy the `commitId` from the origin.
3. `cd mbmonitor-common`
4. `git merge *commitId*`
5. Resolve any conflicts.
6. `cd ..`
7. `git add .`
8. `git commit -m "update"`
9. `git push`

<!-- Receive commit hash
Ex: Log: Submodule path 'mbmonitor-common': checked out 'b41fc3813780e41ea067578d0598cbe67d72e9d1'
Commit hash: b41fc3813780e41ea067578d0598cbe67d72e9d1

6. cd <submodule-name>
7. git checkout <submodule-name>
8. git merge <commit-hash> -->

# Create merge request with Submodules

## 1. Commit your change at submodule

1. `cd submodule`  
2. `git add .`  
3. `git commit -m "Updated submodule references"`  
4. `git push origin feature/your-branch-name`  
5. Create mr from `feature/your-branch-name` to `develop`

## 2. Commit your change with repo

1. `git add .`  
2. `git commit -m "Description of the changes made"`  
3. `git push origin feature/your-branch-name`  
4. Create mr from `feature/your-branch-name` to `develop`

### Note: If you only commit the submodule, the commit hash on your branch won’t change;

    "Git submodule update" will update the submodule to its own commit hash