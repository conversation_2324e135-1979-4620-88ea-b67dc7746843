package vn.com.mbbank.kanban.mbmonitor.common.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.AlertRequestDatabaseResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertRequestDatabaseEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 06/11/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AlertRequestDatabaseResponseMapper extends
    KanbanBaseMapper<AlertRequestDatabaseResponse, AlertRequestDatabaseEntity> {
  AlertRequestDatabaseResponseMapper INSTANCE =
      Mappers.getMapper(AlertRequestDatabaseResponseMapper.class);
}