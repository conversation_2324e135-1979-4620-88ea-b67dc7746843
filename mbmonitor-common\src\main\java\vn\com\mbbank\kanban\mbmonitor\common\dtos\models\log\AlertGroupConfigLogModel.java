package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupOutputEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AlertGroupConfigLogModel {
  /**
   * Information of Author.
   *
   * <AUTHOR>
   * @created_date 3/3/2025
   */
  @Getter
  @Setter
  @AllArgsConstructor
  @NoArgsConstructor
  @Builder
  public static class CustomOutput {
    String service;
    String application;
    String contact;
    String priority;
    String alertContent;
  }

  private String name;
  private String description;
  private ConfigDependencyLogModel dependency;
  private List<String> conditions;
  private List<String> customObjects;
  private AlertGroupOutputEnum alertOutputType;
  private CustomOutput customOutput;
  private boolean active;
}
