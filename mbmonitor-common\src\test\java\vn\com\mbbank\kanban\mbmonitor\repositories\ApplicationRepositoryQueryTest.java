package vn.com.mbbank.kanban.mbmonitor.repositories;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.repositories.ApplicationRepositoryQuery;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;


public class ApplicationRepositoryQueryTest {

  @InjectMocks
  ApplicationRepositoryQuery applicationRepositoryQuery;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }
  @Test
  void buildQueryStatusEq_success_statusNull() {
    var result =
        ReflectionTestUtils.invokeMethod(applicationRepositoryQuery, "buildQueryStatusEq",
            new Object[] {null});
    assertNull(result);
  }

  @Test
  void buildQueryStatusEq_success_statusNonNull() {
    var result =
        ReflectionTestUtils.invokeMethod(applicationRepositoryQuery, "buildQueryStatusEq",
            AlertGroupStatusEnum.NEW);
    assertNotNull(result);
  }

  @Test
  void buildQueryDeletedEq_success_WithDeletedIsTrue() {
    PrepareQuery result =
        ReflectionTestUtils.invokeMethod(applicationRepositoryQuery, "buildQueryDeletedEq", true);
    assertNotNull(" AND application.DELETED = 0", result.getQuery());
  }

  @Test
  void buildQueryDeletedEq_success_WithDeletedIsFalse() {
    PrepareQuery result =
        ReflectionTestUtils.invokeMethod(applicationRepositoryQuery, "buildQueryDeletedEq", false);
    assertNotNull(result.getQuery());
  }

  @Test
  void buildQueryNameLike_success() {
    PrepareQuery result =
        ReflectionTestUtils.invokeMethod(applicationRepositoryQuery, "buildQueryNameLike", "213");
    assertNotNull(result.getQuery());
  }

}
