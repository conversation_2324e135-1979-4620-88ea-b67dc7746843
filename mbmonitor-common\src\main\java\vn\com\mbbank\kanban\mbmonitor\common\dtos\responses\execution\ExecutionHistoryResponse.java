package vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution;

import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecuteScriptParamModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionTypeEnum;

/**
 * ExecutionHistoryResponse.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ExecutionHistoryResponse {
  String id;
  String executionName;
  String executionDescription;
  ExecutionTypeEnum executionType;
  String executionScript;
  List<ExecuteScriptParamModel> executionParams;
  ExecutionStatusEnum status;
  String result;
  String error;
  String executionBy;
  String startTime;
  String endTime;
}
