package vn.com.mbbank.kanban.mbmonitor.common.utils;

import java.lang.reflect.Method;
import java.util.Collection;

/**
 * Utility class for object operations.
 */
public class ObjectUtils {

  /**
   * Checks if all public getter methods that return a Collection in the given object
   * are either null or empty.
   *
   * @param obj the object to check
   * @return true if all collections are null or empty, false if at least one collection is non-empty
   * @throws RuntimeException if a reflection error occurs
   */
  public static boolean isAllEmpty(Object obj) {
    if (obj == null) {
      return true;
    }

    try {
      for (Method method : obj.getClass().getMethods()) {
        if (isCollectionGetter(method)) {
          Collection<?> collection = (Collection<?>) method.invoke(obj);
          if (collection != null && !collection.isEmpty()) {
            return false;
          }
        }
      }
    } catch (Exception e) {
      throw new RuntimeException("Failed to check collections in object: " + obj.getClass().getName(), e);
    }

    return true;
  }

  /**
   * Checks if the given method is a public getter that returns a Collection and has no parameters.
   *
   * @param method the method to check
   * @return true if the method is a Collection getter, false otherwise
   */
  private static boolean isCollectionGetter(Method method) {
    return method.getName().startsWith("get")
        && method.getParameterCount() == 0
        && Collection.class.isAssignableFrom(method.getReturnType());
  }
}
