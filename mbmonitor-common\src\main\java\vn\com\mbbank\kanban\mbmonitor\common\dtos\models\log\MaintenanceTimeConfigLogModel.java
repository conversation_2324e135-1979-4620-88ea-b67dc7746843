package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupOutputEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeUnitEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MaintenanceTimeConfigLogModel {
  private String name;
  private String description;
  private ConfigDependencyLogModel dependency;
  private String condition;
  private AlertGroupOutputEnum alertOutputType;
  private MaintenanceTimeConfigTypeEnum type;
  private Integer nextTime;
  private MaintenanceTimeUnitEnum unit;
  private String cronExpression;
  private String startTime;
  private String endTime;
  private boolean active;
}
