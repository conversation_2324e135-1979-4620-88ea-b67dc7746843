package vn.com.mbbank.kanban.mbmonitor.common.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.Date;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class DateUtilsTest {
  final String FORMAT_YYYY_MM_DD_T_HH_MM_SS = "yyyy-MM-dd'T'HH:mm:ss";

  @Test
  void constructor() {
    new DateUtils();
  }

  @Test
  void convertStringToDate_success() {
    Date result = DateUtils.convertStringToDate("2024-08-01 00:00:00");
    Assertions.assertNotNull(result);
  }

  @Test
  void convertStringToFormattedString_ValidDateAndPattern_success() {
    String date = "2024-10-10 14:45:30.123";
    String result = DateUtils.convertDefaultStringDateToFormatedString(date, FORMAT_YYYY_MM_DD_T_HH_MM_SS);
    assertEquals("2024-10-10T14:45:30", result);
  }

  @Test
  void convertStringToFormattedString_ValidDateDifferentPattern_success() {
    String date = "2024-10-10 14:45:30.123";
    String result = DateUtils.convertDefaultStringDateToFormatedString(date, FORMAT_YYYY_MM_DD_T_HH_MM_SS);
    assertEquals("2024-10-10T14:45:30", result);
  }

  @Test
  void convertStringToFormattedString_NullDate_success() {
    String result = DateUtils.convertDefaultStringDateToFormatedString(null, "yyyy-MM-dd");
    assertEquals("", result);
  }

  @Test
  void convertStringToFormattedString_EmptyDate_success() {
    String result = DateUtils.convertDefaultStringDateToFormatedString("", "yyyy-MM-dd");
    assertEquals("", result);
  }

  @Test
  void convertStringToFormattedString_NullPattern_success() {
    String date = "2024-10-10T14:45:30.123";
    String result = DateUtils.convertDefaultStringDateToFormatedString(date, null);
    assertEquals("", result);
  }

  @Test
  void convertStringToFormattedString_EmptyPattern_success() {
    String date = "2024-10-10T14:45:30.123";
    String result = DateUtils.convertDefaultStringDateToFormatedString(date, "");
    assertEquals("", result);
  }

  @Test
  void convertStringToFormatedString_InvalidDate() {
    String date = "invalid-date-string";
    String pattern = "yyyy-MM-dd HH:mm:ss";
    String result = DateUtils.convertDefaultStringDateToFormatedString(date, pattern);
    assertEquals("", result);
  }
}
