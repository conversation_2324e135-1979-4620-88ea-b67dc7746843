package vn.com.mbbank.kanban.mbmonitor.common.utils.export;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.logging.log4j.util.Strings;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AttributeInfoDto;


/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 09/26/2024
 */
public class ExcelUtils {
  private ExcelUtils() {
  }

  /**
   * Transformer data.
   *
   * @param dataInfoList      the list of obj
   * @param attributeInfoList the list of attributeInfo
   * @param <T>               the type of the elements in the list
   * @return {@code true} if the string is a numeric value, {@code false} otherwise
   */
  public static <T> List<List<AttributeInfoDto>> transformDataExport(
      List<T> dataInfoList, List<AttributeInfoDto> attributeInfoList) {

    List<List<AttributeInfoDto>> transformedData = new ArrayList<>();
    if (dataInfoList == null || dataInfoList.isEmpty() || attributeInfoList == null) {
      return transformedData;
    }

    List<AttributeInfoDto> attributeInfoClone = attributeInfoClone(attributeInfoList);

    for (T instance : dataInfoList) {
      List<AttributeInfoDto> attributeInfoDtoList = new ArrayList<>();

      for (AttributeInfoDto attributeInfo : attributeInfoClone) {
        String value = getValueFromInstance(instance, attributeInfo.getAttributeId());
        AttributeInfoDto clonedAttribute = new AttributeInfoDto(
            attributeInfo.getPosition(),
            attributeInfo.getAttributeId(),
            attributeInfo.getAttributeName(),
            value != null ? value : Strings.EMPTY
        );
        attributeInfoDtoList.add(clonedAttribute);
      }
      transformedData.add(attributeInfoDtoList);
    }
    return transformedData;
  }

  /**
   * Retrieves the value of an attribute from an object instance.
   *
   * @param instance    the object to retrieve the value from (can be a Map or a class instance)
   * @param attributeId the name of the attribute to retrieve
   * @return the value of the attribute as a String, or null if not found or inaccessible
   */
  public static String getValueFromInstance(Object instance, String attributeId) {
    if (instance instanceof Map<?, ?>) {
      Object value = ((Map<?, ?>) instance).get(attributeId);
      return value != null ? value.toString() : null;
    } else {
      try {
        Field field = instance.getClass().getDeclaredField(attributeId);
        field.setAccessible(true);
        Object value = field.get(instance);
        return value != null ? value.toString() : null;
      } catch (NoSuchFieldException | IllegalAccessException e) {
        return null;
      }
    }
  }

  private static List<AttributeInfoDto> attributeInfoClone(List<AttributeInfoDto> attributeInfoList) {
    return attributeInfoList.stream()
        .sorted(Comparator.comparingInt(AttributeInfoDto::getPosition))
        .collect(Collectors.toList());
  }

  /**
   * Retrieves all declared fields from the class of the first object in the provided list
   * and its superclasses.
   *
   * @param dataInfoList a list of objects from which to extract fields.
   * @return a list of {@link Field} objects representing all declared fields
   */
  public static List<Field> getAllDeclaredFields(List<?> dataInfoList) {
    List<Field> allFields = new ArrayList<>();

    if (dataInfoList.isEmpty()) {
      return allFields;
    }

    Class<?> clazz = dataInfoList.get(0).getClass();

    while (clazz != null) {
      Field[] fields = clazz.getDeclaredFields();
      for (Field field : fields) {
        field.setAccessible(true);
        allFields.add(field);
      }
      clazz = clazz.getSuperclass();
    }
    return allFields;
  }

  /**
   * Finds a matching field by name in a list of fields.
   *
   * @param fields the list of fields to search in
   * @param id     the name of the field to find
   * @return an Optional containing the matching field, or an empty Optional if not found
   */
  public static Optional<Field> getMatchingField(List<Field> fields, String id) {
    return fields.stream()
        .filter(field -> field.getName().equals(id))
        .findFirst();
  }

  /**
   * Get value of field.
   *
   * @param matchingField the field matching
   * @param instance      the instance of obj
   * @return String of value
   */
  public static String getFieldValue(Field matchingField, Object instance) {
    try {
      Object value = matchingField.get(instance);
      return Objects.nonNull(value) ? value.toString() : Strings.EMPTY;
    } catch (IllegalAccessException e) {
      return Strings.EMPTY;
    }
  }

}

