package vn.com.mbbank.kanban.mbmonitor.common.mapper.log;

import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.SysPermissionLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.SysRoleLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysPermissionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysRoleEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SysRoleLogModelMapper extends
    KanbanBaseMapper<SysRoleLogModel, SysRoleEntity> {
  SysRoleLogModelMapper INSTANCE = Mappers.getMapper(SysRoleLogModelMapper.class);

  /**
   * map SysRoleLogModel.
   *
   * @param sysRole     SysRoleEntity
   * @param permissions list of SysPermissionEntity
   * @return SysRoleLogModel
   */
  default SysRoleLogModel map(SysRoleEntity sysRole, List<SysPermissionEntity> permissions) {
    return new SysRoleLogModel(sysRole.getName(), sysRole.getDescription(), permissions.stream().map(
        permission -> new SysPermissionLogModel(
            permission.getModule(), permission.getAction(), permission.getRoleId(),
            permission.getType(), permission.getModuleId(), permission.getModuleParentId())).toList());
  }
}
