package vn.com.mbbank.kanban.mbmonitor.common.utils;

import org.junit.jupiter.api.Test;
import java.util.List;
import static org.junit.jupiter.api.Assertions.*;

class ObjectUtilsTest {

    static class TestObject {
        private List<String> list1;
        private List<String> list2;

        public List<String> getList1() {
            return list1;
        }

        public List<String> getList2() {
            return list2;
        }

        public void setList1(List<String> list1) {
            this.list1 = list1;
        }

        public void setList2(List<String> list2) {
            this.list2 = list2;
        }
    }

    @Test
    void isAllEmpty_whenAllNull_shouldReturnTrue() {
        TestObject obj = new TestObject();
        assertTrue(ObjectUtils.isAllEmpty(obj));
    }

    @Test
    void isAllEmpty_whenAllEmpty_shouldReturnTrue() {
        TestObject obj = new TestObject();
        obj.setList1(List.of());
        obj.setList2(List.of());
        assertTrue(ObjectUtils.isAllEmpty(obj));
    }

    @Test
    void isAllEmpty_whenOneNonEmpty_shouldReturnFalse() {
        TestObject obj = new TestObject();
        obj.setList1(List.of("item"));
        obj.setList2(List.of());
        assertFalse(ObjectUtils.isAllEmpty(obj));
    }

    @Test
    void isAllEmpty_whenAllNonEmpty_shouldReturnFalse() {
        TestObject obj = new TestObject();
        obj.setList1(List.of("item1"));
        obj.setList2(List.of("item2"));
        assertFalse(ObjectUtils.isAllEmpty(obj));
    }

    @Test
    void isAllEmpty_whenObjectIsNull_shouldReturnTrue() {
        assertTrue(ObjectUtils.isAllEmpty(null));
    }
}
