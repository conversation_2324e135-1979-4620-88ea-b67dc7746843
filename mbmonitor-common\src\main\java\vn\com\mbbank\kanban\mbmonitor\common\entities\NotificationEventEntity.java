package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.KanbanAutoGenerateUlId;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationEventScheduleTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationTypeEnum;

/**
 * Entity for NOTIFICATION_EVENT table.
 */
@KanbanAutoGenerateUlId
@Data
@Entity
@Table(name = TableName.NOTIFICATION_EVENT)
@EqualsAndHashCode(callSuper = true)
public class NotificationEventEntity extends BaseEntity<String> {
  @Id
  @Column(name = "ID")
  private String id;

  @Column(name = "TITLE")
  private String title;

  @Column(name = "CONTENT")
  private String content;

  @Column(name = "NOTIFICATION_TYPE")
  @Enumerated(EnumType.STRING)
  private NotificationTypeEnum notificationType;

  @Column(name = "SCHEDULE_TYPE")
  @Enumerated(EnumType.STRING)
  private NotificationEventScheduleTypeEnum scheduleType;

  @Column(name = "TRIGGERED_DATE")
  private Date triggeredDate;

  @Column(name = "CRON_EXPRESSION")
  private String cronExpression;

  @Column(name = "ACTIVE")
  private Boolean active;
}