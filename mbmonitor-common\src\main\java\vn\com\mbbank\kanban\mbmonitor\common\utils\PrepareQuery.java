package vn.com.mbbank.kanban.mbmonitor.common.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.repository.query.EscapeCharacter;

/**
 * PrepareQuery.
 */
@Getter
@NoArgsConstructor
public class PrepareQuery {

  private final Map<String, Object> params = new HashMap<>();
  private final StringBuilder queryBuilder = new StringBuilder();

  /**
   * PrepareQuery.
   *
   * @param query a string
   */
  public PrepareQuery(String query) {
    queryBuilder.append(query);
  }

  /**
   * PrepareQuery.
   *
   * @param query     a string
   * @param newParams a map of param
   */
  public PrepareQuery(String query, Map<String, Object> newParams) {
    queryBuilder.append(query);
    params.putAll(newParams);
  }

  /**
   * PrepareQuery.
   *
   * @param query a string
   * @param key   a key of param
   * @param value a value of param
   */
  public PrepareQuery(String query, String key, Object value) {
    queryBuilder.append(query);
    params.put(key, value);
  }

  /**
   * PrepareQuery.
   *
   * @param prepareQuery a prepareQuery
   * @return PrepareQuery
   */
  public PrepareQuery append(PrepareQuery prepareQuery) {
    if (Objects.nonNull(prepareQuery)) {
      queryBuilder.append(prepareQuery.getQueryBuilder());
      params.putAll(prepareQuery.getParams());
    }
    return this;
  }


  /**
   * append prepare query if condition true.
   *
   * @param prepareQuery a prepareQuery
   * @param condition    a condition
   * @return PrepareQuery
   */
  public PrepareQuery append(PrepareQuery prepareQuery, boolean condition) {
    if (condition) {
      this.append(prepareQuery);
    }
    return this;
  }

  /**
   * append prepare query add escape character and format escape value.
   *
   * @param prepareQuery a prepareQuery
   * @param matcher      a like matcher
   * @return PrepareQuery
   */
  public PrepareQuery append(PrepareQuery prepareQuery, LikeMatcher matcher) {
    if (Objects.nonNull(prepareQuery) && Objects.nonNull(matcher)) {
      queryBuilder.append(prepareQuery.getQueryBuilder()).append(" escape '\\'");
      var parsedParams = prepareQuery.getParams().entrySet()
          .stream()
          .collect(Collectors.toMap(
              Map.Entry::getKey,
              entry -> {
                var escapeContent = EscapeCharacter.DEFAULT.escape(entry.getValue().toString());
                return switch (matcher) {
                  case STARTING -> "%" + escapeContent;
                  case ENDING -> escapeContent + "%";
                  case CONTAINING -> "%" + escapeContent + "%";
                };
              }
          ));
      params.putAll(parsedParams);
    }
    return this;
  }

  /**
   * PrepareQuery.
   *
   * @param appendQuery a string
   * @return PrepareQuery
   */
  public PrepareQuery append(String appendQuery) {
    if (Objects.nonNull(appendQuery)) {
      queryBuilder.append(appendQuery);
    }
    return this;
  }

  /**
   * PrepareQuery.
   *
   * @param appendQuery a string
   * @return PrepareQuery
   */
  public PrepareQuery append(StringBuilder appendQuery) {
    queryBuilder.append(appendQuery);
    return this;
  }

  /**
   * PrepareQuery.
   *
   * @param newQueryBuilder a StringBuilder
   * @param newParams       a map of param
   * @return PrepareQuery
   */
  public PrepareQuery append(StringBuilder newQueryBuilder, Map<String, Object> newParams) {
    queryBuilder.append(newQueryBuilder);
    params.putAll(newParams);
    return this;
  }

  /**
   * PrepareQuery.
   *
   * @param newQueryBuilder a StringBuilder
   * @param key             a key of param
   * @param value           a value of param
   * @return PrepareQuery
   */
  public PrepareQuery append(StringBuilder newQueryBuilder, String key, Object value) {
    queryBuilder.append(newQueryBuilder);
    params.put(key, value);
    return this;
  }

  /**
   * PrepareQuery.
   *
   * @param newQueryBuilder a string
   * @param key             a key of param
   * @param value           a value of param
   * @return a PrepareQuery
   */
  public PrepareQuery append(String newQueryBuilder, String key, Object value) {
    queryBuilder.append(newQueryBuilder);
    params.put(key, value);
    return this;
  }

  /**
   * get string query.
   *
   * @return query
   */
  public String getQuery() {
    return queryBuilder.toString();
  }
}