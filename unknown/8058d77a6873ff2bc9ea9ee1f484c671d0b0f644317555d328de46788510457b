package vn.com.mbbank.kanban.mbmonitor.common.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.util.Objects;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/8/2024
 */
@Converter
public class PermissionModuleConverter implements AttributeConverter<PermissionModuleEnum, String> {
  @Override
  public String convertToDatabaseColumn(PermissionModuleEnum attribute) {
    return attribute != null ? attribute.name() : null;
  }

  @Override
  public PermissionModuleEnum convertToEntityAttribute(String dbData) {
    if (Objects.isNull(dbData)) {
      return PermissionModuleEnum.UNKNOWN;
    }
    try {
      return PermissionModuleEnum.valueOf(dbData);
    } catch (IllegalArgumentException e) {
      return PermissionModuleEnum.UNKNOWN;
    }
  }
}
