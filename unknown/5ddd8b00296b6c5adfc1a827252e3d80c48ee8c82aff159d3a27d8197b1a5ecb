package vn.com.mbbank.kanban.mbmonitor.common.dtos.models;

import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AlertBaseModel extends AlertEntity {
  private String serviceNameRaw;

  private String applicationNameRaw;

  private String contentRaw;

  private String priorityRaw;

  private String recipientRaw;

  private Boolean isValid = true;

  private String description;

}
