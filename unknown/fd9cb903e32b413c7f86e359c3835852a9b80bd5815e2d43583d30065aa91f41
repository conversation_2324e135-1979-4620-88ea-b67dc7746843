package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.notifications;


import java.util.List;
import java.util.Map;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ProseMirrorNodeTypeEnum;

/**
 * Represents a ProseMirror document model.
 */
@Data
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ProseMirrorDocModel {
  /**
   * The type of the document node.
   */
  String type = ProseMirrorNodeTypeEnum.DOC.getValue();
  /**
   * The content of the document, consisting of a list of paragraphs.
   */
  List<Map<String, Object>> content;
}
