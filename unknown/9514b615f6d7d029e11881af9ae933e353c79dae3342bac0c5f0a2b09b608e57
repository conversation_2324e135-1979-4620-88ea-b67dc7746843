package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CustomObjectTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CustomObjectLogModel {
  private String name;
  private String description;
  private CustomObjectTypeEnum type;
  private String regex;
  private Integer fromIndex;
  private Integer toIndex;
  private String fromKeyword;
  private String toKeyword;
}
