package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.telegram;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import vn.com.mbbank.kanban.mbmonitor.common.utils.telegram.TelegramMessageTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/6/2025
 */
@Data
public class TelegramSendMessageModel {
  @JsonProperty("chat_id")
  private String chatId;

  @JsonProperty("text")
  private String message;

  @JsonProperty("parse_mode")
  private TelegramMessageTypeEnum type;

  @JsonIgnore
  private boolean status;
}
