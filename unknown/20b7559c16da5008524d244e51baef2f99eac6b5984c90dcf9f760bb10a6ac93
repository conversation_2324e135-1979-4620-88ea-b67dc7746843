#!/usr/bin/env groovy
library('cicd-shared-lib-dev@tanzu') _
properties([
    parameters([
        string(name: 'text', defaultValue: 'text', description: 'cicd/tanzu-uat/Jenkinsfile')
    ])
])

import java.text.SimpleDateFormat

def list = [
    [url: "https://gitlab.mbbank.com.vn/kanban/mbmonitor/mbmonitor-server.git", folder: "mbmonitor-server"],
    [url: "https://gitlab.mbbank.com.vn/kanban/mbmonitor/mbmonitor-external/mbmonitor-external-database.git", folder: "mbmonitor-external-database"],
    [url: "https://gitlab.mbbank.com.vn/kanban/mbmonitor/mbmonitor-external/mbmonitor-external-job.git", folder: "mbmonitor-external-job"],
    [url: "https://gitlab.mbbank.com.vn/kanban/mbmonitor/mbmonitor-external/mbmonitor-external-mail.git", folder: "mbmonitor-external-mail"],
    [url: "https://gitlab.mbbank.com.vn/kanban/mbmonitor/mbmonitor-external/mbmonitor-external-webhook.git", folder: "mbmonitor-external-webhook"],
    [url: "https://gitlab.mbbank.com.vn/kanban/mbmonitor/mbmonitor-frontend.git", folder: "mbmonitor-frontend"],
    [url: "https://gitlab.mbbank.com.vn/kanban/mbmonitor/mbmonitor-gateway.git", folder: "mbmonitor-gateway"],
]

def buildLatestCommon(){
        echo '==========BUILD LATEST COMMON============='
        git_command('git checkout develop')
        git_command('git submodule update --init')
        git_command('git submodule update --remote --merge')
        git_command('git status')
        git_command('git add .')
        git_command('git commit -m "Auto update latest Common"')
        git_command('git push')
}
def buildPreRelease(){
        echo '==========BUILD PRE_RELEASE============='
        git_command('git checkout pre-release')
        git_command('git merge origin/develop')
        git_command('git status')
        //git_command('git add .')
        //git_command('git commit -m "Auto Merge From Develop To Pre-Release"')
        git_command('git push')
}
def buildMain(){
        echo '==========BUILD MAIN============='
        git_command('git checkout main')
        git_command('git merge origin/pre-release')
        git_command('git status')
        //git_command('git add .')
        //git_command('git commit -m "Auto Merge From Pre-release To Main"')
        git_command('git push')
}

node("${NODE_INTEGRATION_TEAM}") {

    def text = params.text;

    def isDeployPreRelease = text == 'pre-release';
    def isDeployMain = text == 'main';

    try {
        list.each { item -> 
            stage(' Async ' + item.folder) {
                git_command("git  clone " + item.url)
                dir(item.folder){
                    try{
                        if(isDeployPreRelease){
                            buildPreRelease();
                        } else if(isDeployMain){
                            buildMain();
                        } else {
                            if(item.folder == 'mbmonitor-frontend' || item.folder == 'mbmonitor-gateway'){
                                echo 'By pass Common ' + item.folder
                            } else {
                                buildLatestCommon();
                            }
                        }
                    } catch(Exception e){
                        echo "Git exception: ${e.getMessage()}"
                    }

                }
            }
        }
   
    } catch (Exception e){
        echo e
    } finally {
        cleanWs()
    }

}

