package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.notifications;

import java.util.List;
import java.util.Map;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ProseMirrorNodeTypeEnum;

/**
 * Represents a text node in a ProseMirror document.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TextNodeModel {

  String type = ProseMirrorNodeTypeEnum.TEXT.getValue();
  String text;
  List<Map<String, Object>> marks;

  /**
   * Constructs a new TextNodeModel with the given text.
   *
   * @param text the text content
   */
  public TextNodeModel(String text) {
    this.text = text;
  }

  /**
   * Creates a TextNodeModel with a link mark.
   *
   * @param text the text content of the link
   * @param href the URL of the link
   * @return a new TextNodeModel with a link mark
   */
  public static TextNodeModel withLink(String text, String href) {
    Map<String, Object> attrs = Map.of(
        "href", href,
        "rel", "noopener noreferrer nofollow"
    );
    Map<String, Object> mark = Map.of(
        "type", ProseMirrorNodeTypeEnum.LINK.getValue(),
        "attrs", attrs
    );
    return new TextNodeModel(ProseMirrorNodeTypeEnum.TEXT.getValue(), text, List.of(mark));
  }
}
