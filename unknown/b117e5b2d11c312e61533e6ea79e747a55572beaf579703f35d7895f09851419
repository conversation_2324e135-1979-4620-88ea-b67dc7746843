package vn.com.mbbank.kanban.mbmonitor.common.dtos.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogFunctionEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/29/2024
 */
@Data
@Accessors(chain = true)
public class SysLogModel {
  @JsonProperty("id")
  private String id;

  @JsonProperty("function")
  private LogFunctionEnum function;

  @JsonProperty("action")
  private LogActionEnum action;

  @JsonProperty("logBy")
  private String logBy;

  @JsonProperty("logDate")
  private String logDate;

  @JsonProperty("message")
  private SysLogMessageModel message;
}
