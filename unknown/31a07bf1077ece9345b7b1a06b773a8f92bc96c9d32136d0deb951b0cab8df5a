package vn.com.mbbank.kanban.mbmonitor.common.antlr4;

import java.util.ArrayList;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.tree.ParseTreeWalker;
import vn.com.mbbank.kanban.mbmonitor.common.antlr4.listener.PythonImportListener;
import vn.com.mbbank.kanban.mbmonitor.common.antlr4.listener.SyntaxErrorListener;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.PythonAnalysisModel;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 1/9/2025
 */
public class PythonParserUtils {

  /**
   * Analyzes a Python file for syntax errors and collects import statements and environment variables.
   *
   * @param pythonScript script
   * @return PythonAnalysisResult containing validation result and collected data
   */
  public static PythonAnalysisModel analyzePythonScript(String pythonScript) {
    PythonAnalysisModel result = new PythonAnalysisModel();

    // Create a lexer that feeds off the character stream
    PythonLexer lexer = new PythonLexer(CharStreams.fromString(pythonScript));
    lexer.removeErrorListeners();
    SyntaxErrorListener lexerErrorListener = new SyntaxErrorListener();
    lexer.addErrorListener(lexerErrorListener);

    // Create a token stream from the lexer
    CommonTokenStream tokenStream = new CommonTokenStream(lexer);

    // Create a parser that feeds off the token stream
    PythonParser parser = new PythonParser(tokenStream);
    parser.removeErrorListeners();
    SyntaxErrorListener parserErrorListener = new SyntaxErrorListener();
    parser.addErrorListener(parserErrorListener);

    // Parse the file
    PythonParser.File_inputContext tree = parser.file_input();

    // Check if there were any syntax errors
    if (!lexerErrorListener.getSyntaxErrors().isEmpty() || !parserErrorListener.getSyntaxErrors().isEmpty()) {
      result.setValid(false);
      result.setSyntaxErrors(new ArrayList<>(lexerErrorListener.getSyntaxErrors()));
      result.getSyntaxErrors().addAll(parserErrorListener.getSyntaxErrors());
      return result;
    }

    // If valid, collect imports and environment variables
    result.setValid(true);

    // Use listener to collect imports and env variables
    PythonImportListener importListener = new PythonImportListener();
    ParseTreeWalker walker = new ParseTreeWalker();
    walker.walk(importListener, tree);

    result.setImports(importListener.getImports());
    result.setEnvironmentVariables(importListener.getEnvironmentVariables());
    result.setBuiltInFunctions(importListener.getOsAndBuiltInFunctions());
    return result;
  }

}