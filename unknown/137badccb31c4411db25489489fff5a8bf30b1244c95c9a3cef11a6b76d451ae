package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;

import java.util.List;
import lombok.Data;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TelegramAlertConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TelegramConfigEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/12/2025
 */
@Data
public class TelegramAlertConfigDetailRequest {
  private TelegramConfigEntity config;
  private List<TelegramAlertConfigEntity> alertsConfig;
}
