package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.com.mbbank.kanban.mbmonitor.common.enums.DatabaseConnectionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.OracleDatabaseConnectType;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DatabaseConnectionLogModel {
  private String name;
  private String description;
  private DatabaseConnectionTypeEnum type;
  private String host;
  private Integer port;
  private String sid;
  private String serviceName;
  private Boolean isActive;
  private OracleDatabaseConnectType oracleConnectType;
  private String userName;
}
