package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.telegram;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/6/2025
 */
@Data
public class TelegramUserInGroupModel {
  @JsonProperty("ok")
  private boolean status;
  @JsonAlias("result")
  private ResultWrapper result;

  @Data
  class ResultWrapper {
    @JsonProperty("user")
    private TelegramUserDetailModel user;
    @JsonProperty("status")
    private String role;

  }
}


