package vn.com.mbbank.kanban.mbmonitor.common.configs;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 1/9/2025
 */
@Component
public class KanbanPropertyConfigUtils {
  private static Environment env;

  /**
   * Init constructor.
   *
   * @param environment environment
   */
  @Autowired
  public KanbanPropertyConfigUtils(Environment environment) {
    KanbanPropertyConfigUtils.env = environment;
  }

  /**
   * get data from application.properties.
   *
   * @param key key
   * @return data
   */
  public static String getProperty(String key) {
    return env.getProperty(key);
  }

  /**
   * get data  with default value from application.properties.
   *
   * @param key key
   * @param defaultValue defaultValue
   * @return data
   */
  public static String getProperty(String key, String defaultValue) {
    return env.getProperty(key, defaultValue);
  }
}
