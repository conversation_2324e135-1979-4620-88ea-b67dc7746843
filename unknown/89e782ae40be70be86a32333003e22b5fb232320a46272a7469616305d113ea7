package vn.com.mbbank.kanban.mbmonitor.common.converter;

import jakarta.persistence.AttributeConverter;
import java.util.Objects;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/8/2024
 */
public class PermissionActionConverter implements AttributeConverter<PermissionActionEnum, String> {
  @Override
  public String convertToDatabaseColumn(PermissionActionEnum attribute) {
    return attribute != null ? attribute.name() : null;
  }

  @Override
  public PermissionActionEnum convertToEntityAttribute(String dbData) {
    if (Objects.isNull(dbData)) {
      return PermissionActionEnum.UNKNOWN;
    }
    try {
      return PermissionActionEnum.valueOf(dbData);
    } catch (IllegalArgumentException e) {
      // Return a default value if the database value is invalid
      return PermissionActionEnum.UNKNOWN; // Replace with your default value
    }
  }
}
