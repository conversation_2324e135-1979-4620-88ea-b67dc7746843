package vn.com.mbbank.kanban.mbmonitor.common.configs.httpclients.interceptors;

import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Enumeration;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;

/**
 * Intercepts outgoing HTTP requests to forward specific headers from the
 * current context or
 * incoming request.
 */
@Component
@RequiredArgsConstructor
public class ForwardHeadersInterceptor implements ClientHttpRequestInterceptor {
  Logger logger = LoggerFactory.getLogger(this.getClass());

  private final HttpServletRequest httpServletRequest;

  @Override
  public @NonNull ClientHttpResponse intercept(@NonNull HttpRequest request, @NonNull byte[] body,
                                               @NonNull ClientHttpRequestExecution execution)
      throws IOException {

    // Ensure all headers from HttpServletRequest are copied to HttpRequest
    HttpHeaders headers = request.getHeaders();
    Enumeration<String> headerNames = httpServletRequest.getHeaderNames();

    if (headerNames != null) {
      while (headerNames.hasMoreElements()) {
        String headerName = headerNames.nextElement();
        String headerValue = httpServletRequest.getHeader(headerName);
        headers.add(headerName, headerValue);
      }
    }
    // Proceed with the request execution and return the response
    logger.info(
        "Request Api internal url: " + request.getURI() + KanbanCommonUtil.beanToString(request));
    logger.info("Request body Api internal url: " + request.getURI()
        +
        KanbanCommonUtil.beanToString(body));
    var result = execution.execute(request, body); // @NonNull guaranteed here
    logger.info(
        "Response Api internal url: " + request.getURI() + KanbanCommonUtil.beanToString(result));
    return result;
  }
}