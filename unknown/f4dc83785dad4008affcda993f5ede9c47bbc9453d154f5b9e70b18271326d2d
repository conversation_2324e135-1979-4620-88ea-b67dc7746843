package vn.com.mbbank.kanban.mbmonitor.common.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Used to mark a parameter in a function as being subject to change..
 *
 * <AUTHOR>
 * @created_date 3/26/2025
 */
@Target(ElementType.PARAMETER)
@Retention(RetentionPolicy.RUNTIME)
public @interface ModifyField {
}
