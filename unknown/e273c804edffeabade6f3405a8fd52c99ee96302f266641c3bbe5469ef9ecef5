package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * PythonAnalysisModel.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PythonAnalysisModel {
  private boolean isValid;
  @Builder.Default
  private List<String> syntaxErrors = new ArrayList<>();
  @Builder.Default
  private Set<String> imports = new HashSet<>();
  @Builder.Default
  private Set<String> environmentVariables = new HashSet<>();
  @Builder.Default
  private Set<String> builtInFunctions = new HashSet<>();
}
