package vn.com.mbbank.kanban.mbmonitor.common.dtos.responses;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseConnectionEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/6/2024
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonIgnoreProperties({"password"})
public class DatabaseConnectionResponse extends DatabaseConnectionEntity {
}
