package vn.com.mbbank.kanban.mbmonitor.common.services.systems.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.utils.KanbanApplicationUtils;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.acl.AclPermissionModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysPermissionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysUserEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.systems.CommonAclPermissionService;
import vn.com.mbbank.kanban.mbmonitor.common.services.systems.CommonUserService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 9/30/2024
 */
@Service
public class CommonAclPermissionServiceImpl implements CommonAclPermissionService {

  @Lazy
  @Autowired
  CommonUserService sysUserService;

  @Override
  public boolean isAnyPermission(List<AclPermissionModel> aclPermissionModels, boolean isMapAll)
      throws BusinessException {
    var userInfo = sysUserService.findOrCreateNew(KanbanApplicationUtils.getUserName());
    if (KanbanCommonUtil.isEmpty(userInfo)) {
      return false;
    }
    if (!userInfo.getIsActive()) {
      return false;
    }
    // return true when user is admin
    if (userInfo.getIsAdmin()) {
      return true;
    }
    List<SysPermissionEntity> permissionsOfUser = new ArrayList<>();
    for (var role : userInfo.getRoles()) {
      if (!KanbanCommonUtil.isEmpty(role.getPermissions())) {
        permissionsOfUser.addAll(role.getPermissions());
      }
    }

    if (KanbanCommonUtil.isEmpty(permissionsOfUser)) {
      return false;
    }
    if (isMapAll) {
      return aclPermissionModels.stream().allMatch(acl -> permissionsOfUser.stream().anyMatch(
          p -> {
            return isMatchPermission(p, acl);
          }));
    }
    return aclPermissionModels.stream().anyMatch(acl -> permissionsOfUser.stream().anyMatch(
        p -> {
          return isMatchPermission(p, acl);
        }));
  }


  @Override
  public boolean isAnyPermission(List<AclPermissionModel> aclPermissionModels)
      throws BusinessException {
    return isAnyPermission(aclPermissionModels, false);
  }

  @Override
  public boolean isSupperAdmin() throws BusinessException {
    return isSupperAdmin(KanbanApplicationUtils.getUserName());
  }

  @Override
  public boolean isSupperAdmin(String userName) throws BusinessException {
    SysUserEntity userInfo = sysUserService.findByUserName(userName)
        .orElseThrow(() -> new BusinessException(ErrorCode.USER_NOT_FOUND));
    if (!userInfo.getIsActive()) {
      return false;
    }
    // return true when user is admin
    if (userInfo.getIsAdmin()) {
      return true;
    }
    return false;
  }

  /**
   * check match permission from entity and model.
   *
   * @param entity   entity
   * @param aclModel aclModel
   * @return match or not match
   */
  protected boolean isMatchPermission(SysPermissionEntity entity, AclPermissionModel aclModel) {
    // check sub module
    if (PermissionTypeEnum.SUB_MODULE.equals(entity.getType())) {
      // check case setting parent module
      if (KanbanCommonUtil.isEmpty(entity.getModuleParentId())) {
        return Objects.equals(aclModel.getModule(), entity.getModule())
            &&
            Objects.equals(aclModel.getAction(), entity.getAction())
            &&
            Objects.equals(aclModel.getParentId(), entity.getModuleId());
      }
      // case check child module
      return Objects.equals(aclModel.getModule(), entity.getModule())
          &&
          Objects.equals(aclModel.getAction(), entity.getAction())
          &&
          Objects.equals(aclModel.getId(), entity.getModuleId())
          &&
          Objects.equals(aclModel.getParentId(), entity.getModuleParentId());

    }
    return entity.getModule().equals(aclModel.getModule())
        &&
        entity.getAction().equals(aclModel.getAction());
  }
}
