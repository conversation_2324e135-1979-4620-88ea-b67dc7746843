package vn.com.mbbank.kanban.mbmonitor.common.services.impl;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.configs.ApplicationContextProvider;
import vn.com.mbbank.kanban.mbmonitor.common.configs.RestTemplateConfig;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.telegram.TelegramSendMessageModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.telegram.TelegramUserInGroupModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.telegram.TelegramUserInfoModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.outbounds.urls.TelegramUrls;
import vn.com.mbbank.kanban.mbmonitor.common.services.RestApiService;
import vn.com.mbbank.kanban.mbmonitor.common.services.TelegramService;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.CommonKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.RestTemplateFactoryUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.telegram.TelegramMessageTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.telegram.TelegramUtils;

@Service
@RequiredArgsConstructor
public class TelegramServiceImpl implements TelegramService {
  private final RestApiService restApiService;

  @Qualifier(RestTemplateConfig.REST_TEMPLATE_NO_INTERCEPTOR)
  private final RestTemplate restTemplate;
  @Value("${mbmonitor.telegram.local.dns.ip:''}")
  private String dnsIp;

  @Value("${mbmonitor.telegram.local.dns.port:80}")
  private Integer dnsPort;

  Logger logger = LoggerFactory.getLogger(this.getClass());

  @Override
  public ResponseEntity<String> sendMessage(String botToken, String groupId, String message) {
    return sendMessage(botToken, groupId, message, TelegramMessageTypeEnum.HTML);
  }

  @Override
  public ResponseEntity<String> sendMessage(String botToken, String groupId, String message,
                                            TelegramMessageTypeEnum type) {

    TelegramSendMessageModel telegramSendMessageModel = new TelegramSendMessageModel();
    telegramSendMessageModel.setMessage(TelegramUtils.sanitizeHtml(message));
    telegramSendMessageModel.setType(type);
    telegramSendMessageModel.setChatId(groupId);
    ResponseEntity<String>
        response =
        restApiService.post(TelegramUrls.getUrl(botToken).getSendMessageUrl(),
            telegramSendMessageModel, String.class);
    return response;
  }

  @Override
  public TelegramUserInfoModel getMe(String botToken) {
    return restApiService.get(TelegramUrls.getUrl(botToken).getMeUrl(), TelegramUserInfoModel.class)
        .getBody();
  }

  @Override
  public TelegramUserInGroupModel getChatMember(String botToken, String groupId, String userId) {
    return restApiService.get(TelegramUrls.getUrl(botToken).getChatMemberUrl(groupId, userId),
        TelegramUserInGroupModel.class).getBody();
  }

  @Override
  public void sendAlertKafka(List<AlertEntity> alerts) {
    if (KanbanCommonUtil.isEmpty(alerts)) {
      return;
    }
    for (AlertEntity alert : alerts) {
      sendKafka(alert);
    }
  }

  @PostConstruct
  public void init() {
    this.restApiService.setCustomRestTemplate(new RestTemplate(restTemplate.getRequestFactory()));
    this.restApiService.setIsErrorHandler(false);
    if (!KanbanCommonUtil.isEmpty(dnsIp)) {
      Map<String, String> dnsMapping = new HashMap<>();
      dnsMapping.put("telegram.org", dnsIp);
      dnsMapping.put("api.telegram.org", dnsIp);
      var factory = RestTemplateFactoryUtils.localDnsHosts(dnsMapping, dnsPort);
      this.restApiService.setRequestFactory(factory);
    }
  }

  /**
   * send message kafka send alert to telegram .
   *
   * @param alert alert
   */
  private void sendKafka(AlertEntity alert) {
    try {
      var data = new BaseKafkaModel<AlertEntity>();
      data.setType(KafkaTypeEnum.TELEGRAM);
      data.setValue(alert);

      var commonKafkaProducerService =
          ApplicationContextProvider.getBean(CommonKafkaProducerService.class);
      commonKafkaProducerService.send(data);
      logger.info("send telegram done");
    } catch (Exception ex) {
      logger.error("send telegram error ", ex);
    }
  }
}
