package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;

@Entity
@Data
@Table(name = TableName.EXECUTION_PARAM)
@EqualsAndHashCode(callSuper = true)
public class ExecutionParamEntity extends BaseEntity<String> {

  @Id
  @Column(name = "ID")
  private String id = GeneratorUtil.generateId();

  @Column(name = "NAME")
  private String name;

  @Column(name = "EXECUTION_ID")
  private String executionId;

  @Override
  public String getId() {
    return id;
  }
}
