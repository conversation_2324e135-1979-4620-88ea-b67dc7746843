package vn.com.mbbank.kanban.mbmonitor.common.services.kafka;

import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/17/2025
 */
@Component
public interface CommonBaseConsumerService {

  /**
   * setting consumer multiple node.
   *
   * @return true/false
   */
  default boolean isKafkaMultipleGroup() {
    return false;
  }


  /**
   * Setting on/off retry message kafka.
   *
   * @return true/false
   */
  default boolean isKafkaRetry() {
    return false;
  }

  /**
   * Execute logic consumer kafka.
   *
   * @param data data
   * @param <T> generic
   * @throws Exception exception
   */
  <T> void kafkaExecute(BaseKafkaModel<T> data) throws Exception;

  /**
   * Kafka type.
   *
   * @return KafkaTypeEnum
   */
  KafkaTypeEnum getKafkaType();
}
