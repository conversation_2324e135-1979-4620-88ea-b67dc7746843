package vn.com.mbbank.kanban.mbmonitor.common.mapper.log;

import java.util.Date;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.AlertLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AlertLogModelMapper extends
    KanbanBaseMapper<AlertLogModel, AlertBaseModel> {
  AlertLogModelMapper INSTANCE = Mappers.getMapper(AlertLogModelMapper.class);

  /**
   * map AlertBaseModel to AlertLogModel.
   *
   * @param alert AlertBaseModel.
   * @return AlertLogModel
   */
  default AlertLogModel map(AlertBaseModel alert) {
    return AlertLogModel.builder()
        .content(alert.getContentRaw())
        .contact(alert.getRecipientRaw())
        .priority(alert.getPriorityRaw())
        .service(alert.getServiceNameRaw())
        .application(alert.getApplicationNameRaw())
        .source(alert.getSource())
        .createDate(DateUtils.formatDate(new Date()))
        .build();
  }
}
