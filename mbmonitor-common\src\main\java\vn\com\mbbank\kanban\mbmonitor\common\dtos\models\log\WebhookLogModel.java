package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WebHookConfigDataTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WebHookConfigMapTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WebhookLogModel {
  private String name;
  private WebHookConfigDataTypeEnum dataType;
  private String token;
  private WebHookConfigMapTypeEnum serviceType;
  private String customService;
  private String fromSourceService;
  private WebHookConfigMapTypeEnum applicationType;
  private String customApplication;
  private String fromSourceApplication;
  private WebHookConfigMapTypeEnum contentType;
  private String customContent;
  private String fromSourceContent;
  private WebHookConfigMapTypeEnum contactType;
  private String customContact;
  private String fromSourceContact;
  private WebHookConfigMapTypeEnum priorityType;
  private String customPriority;
  private String fromSourcePriority;
}
