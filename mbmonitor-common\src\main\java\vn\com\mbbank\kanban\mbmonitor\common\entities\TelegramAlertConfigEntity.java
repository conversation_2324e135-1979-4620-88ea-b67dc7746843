package vn.com.mbbank.kanban.mbmonitor.common.entities;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TelegramAlertConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/6/2025
 */
@Entity
@Data
@Table(name = TableName.TELEGRAM_ALERT_CONFIG)
public class TelegramAlertConfigEntity extends BaseEntity<String> {

  @Id
  @Column(name = "ID")
  private String id = GeneratorUtil.generateId();

  @Column(name = "TELEGRAM_CONFIG_ID")
  private String telegramConfigId;

  @Column(name = "SERVICE_ID")
  private String serviceId;

  @Column(name = "APPLICATION_ID")
  private String applicationId;

  @Column(name = "TYPE")
  @Enumerated(EnumType.STRING)
  private TelegramAlertConfigTypeEnum type;

  @Column(name = "GROUP_CHAT_ID")
  private String groupChatId;

  @JsonProperty("isActive")
  @Column(name = "ACTIVE")
  private Boolean active;


}
