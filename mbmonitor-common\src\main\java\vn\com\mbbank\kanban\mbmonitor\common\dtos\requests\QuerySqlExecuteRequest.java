package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;


import java.util.Map;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;

/**
 * Request view pagination and name search.
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class QuerySqlExecuteRequest {
  Long id;
  Map<String, Object> params;
  PaginationRequestDTO paginationRequest;
}
