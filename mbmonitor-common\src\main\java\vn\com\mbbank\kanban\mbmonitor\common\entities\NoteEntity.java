package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;

@Data
@Entity
@Table(name = TableName.NOTE)
@EqualsAndHashCode(callSuper = true)
public class NoteEntity extends BaseEntity<Long> {
  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "NOTE_SEQ", sequenceName = "NOTE_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "NOTE_SEQ")
  private Long id;

  @Column(name = "CONTENT")
  private String content;

  @Column(name = "ALERT_GROUP_ID")
  private Long alertGroupId;

  @Override
  public Long getId() {
    return id;
  }
}
