package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import java.sql.Timestamp;
import lombok.Getter;
import lombok.Setter;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/18/2024
 */
@Getter
@Setter
@Entity
@Table(name = "DATABASE_COLLECT_TEMP")
public class DatabaseCollectTempEntity extends BaseEntity<Long> {
  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "DATABASE_COLLECT_TEMP_SEQ", sequenceName = "DATABASE_COLLECT_TEMP_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "DATABASE_COLLECT_TEMP_SEQ")
  private Long id;

  @Column(name = "ALERT_COLLECT_ID")
  private String alertCollectId;

  @Column(name = "DATABASE_COLLECT_ID")
  private Long databaseCollectId;
  
  @Column(name = "ALERT_COLLECT_DATE")
  private Timestamp alertCollectDate;
}
