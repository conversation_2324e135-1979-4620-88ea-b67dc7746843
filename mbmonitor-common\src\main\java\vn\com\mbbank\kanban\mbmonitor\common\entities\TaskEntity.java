package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import vn.com.mbbank.kanban.core.entities.core.BaseSoftEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskShiftEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskTimeTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskTypeEnum;

@Data
@Entity
@Table(name = TableName.TASK)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TaskEntity extends BaseSoftEntity<Long> {

  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "TASK_SEQ", sequenceName = "TASK_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TASK_SEQ")
  private Long id;

  @Column(name = "NAME", nullable = false)
  private String name;

  @Column(name = "DESCRIPTION")
  private String description;

  @Column(name = "TYPE")
  @Enumerated(EnumType.STRING)
  private TaskTypeEnum type;

  @Column(name = "TIME_TYPE")
  @Enumerated(EnumType.STRING)
  private TaskTimeTypeEnum timeType;

  @Column(name = "STATUS")
  @Enumerated(EnumType.STRING)
  private TaskStatusEnum status;

  @Column(name = "START_TIME")
  @Temporal(TemporalType.TIMESTAMP)
  private Date startTime;

  @Column(name = "END_TIME")
  @Temporal(TemporalType.TIMESTAMP)
  private Date endTime;

  @Column(name = "CURRENT_ASSIGNEE_USER_NAME")
  private String currentAssigneeUserName;

  @Column(name = "SHIFT")
  @Enumerated(EnumType.STRING)
  private TaskShiftEnum shift;

  @Override
  public Long getId() {
    return id;
  }
}