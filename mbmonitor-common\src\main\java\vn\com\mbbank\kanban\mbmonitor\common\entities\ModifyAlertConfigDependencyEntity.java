package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ModifyAlertConfigDependencyTypeEnum;

@Entity
@Data
@Table(name = TableName.MODIFY_ALERT_CONFIG_DEPENDENCY)
@EqualsAndHashCode(callSuper = true)
public class ModifyAlertConfigDependencyEntity extends BaseEntity<Long> {
  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "MODIFY_ALERT_CONFIG_DEPENDENCY_SEQ",
      sequenceName = "MODIFY_ALERT_CONFIG_DEPENDENCY_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "MODIFY_ALERT_CONFIG_DEPENDENCY_SEQ")
  private Long id;

  @Column(name = "MODIFY_ALERT_CONFIG_ID")
  private Long modifyAlertConfigId;

  @Column(name = "TYPE")
  @Enumerated(EnumType.STRING)
  private ModifyAlertConfigDependencyTypeEnum type;

  @Column(name = "DEPENDENCY_ID")
  private String dependencyId;

  @Override
  public Long getId() {
    return id;
  }
}
