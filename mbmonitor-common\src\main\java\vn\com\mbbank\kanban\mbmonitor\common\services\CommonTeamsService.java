package vn.com.mbbank.kanban.mbmonitor.common.services;

import java.util.List;
import org.springframework.context.annotation.Scope;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.TeamsConfigModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.TeamsGroupChatModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.TeamsTokenModel;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/14/2025
 */
@Scope("prototype")
@Service
public interface CommonTeamsService {
  /**
   * init teams.
   *
   * @param teamsConfig teamsConfig
   */

  void init(TeamsConfigModel teamsConfig);

  /**
   * get token teams.
   *
   * @return token Bearer string
   */
  String getToken();

  /**
   * Call api login and get token from teams.
   *
   * @return TeamsTokenModel
   */
  TeamsTokenModel getTokenFromTeams();

  /**
   * send message.
   *
   * @param message message
   * @param chatId chatId
   * @return ResponseEntity
   */
  ResponseEntity<String> sendMessage(String message, String chatId);


  /**
   * send message with body data.
   *
   * @param body body
   * @param chatId chatId
   * @return ResponseEntity
   */
  ResponseEntity<String> sendMessageWithBody(String body, String chatId);


  /**
   * create group chat id.
   *
   * @param users users
   * @param isAutoRemoveUserInvalid isAutoRemoveUserInvalid
   * @return TeamsGroupChatModel
   */
  TeamsGroupChatModel createGroupChat(List<String> users, Boolean isAutoRemoveUserInvalid)
      throws BusinessException;


  /**
   * Get list group details by url.
   *
   * @return list TeamsGroupChatModel
   */
  List<TeamsGroupChatModel> getAllGroupChat();

}
