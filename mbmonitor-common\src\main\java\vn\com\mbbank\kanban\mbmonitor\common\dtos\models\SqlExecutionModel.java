package vn.com.mbbank.kanban.mbmonitor.common.dtos.models;

import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SqlExecutionResponse;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/14/2024
 */
@Data
@Accessors(chain = true)
public class SqlExecutionModel {
  private List<SqlExecutionResponse.SqlDataMapping> listDataMappings;
  List<String> listColumns;

}
