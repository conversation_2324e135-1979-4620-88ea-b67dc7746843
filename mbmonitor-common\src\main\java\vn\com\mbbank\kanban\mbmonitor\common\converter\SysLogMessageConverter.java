package vn.com.mbbank.kanban.mbmonitor.common.converter;

import com.alibaba.fastjson2.JSON;
import jakarta.persistence.AttributeConverter;
import java.util.ArrayList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.SysLogMessageModel;

/**
 * SysLogMessageConverter.
 */
public class SysLogMessageConverter implements AttributeConverter<SysLogMessageModel, String> {
  private static final Logger logger = LoggerFactory.getLogger(SysLogMessageConverter.class);

  @Override
  public String convertToDatabaseColumn(SysLogMessageModel sysLogMessageModel) {
    return JSON.toJSONString(sysLogMessageModel);
  }

  @Override
  public SysLogMessageModel convertToEntityAttribute(String dbData) {
    try {
      return JSON.parseObject(dbData, SysLogMessageModel.class);
    } catch (Exception e) {
      logger.warn("Convert to RuleGroupType error fallback to null");
      return new SysLogMessageModel(dbData, new ArrayList<>());
    }
  }
}