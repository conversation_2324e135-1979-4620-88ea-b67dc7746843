package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.telegram;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/6/2025
 */
@Data
public class TelegramUserDetailModel {
  @JsonProperty("id")
  private Long id;

  @JsonProperty("is_bot")
  private Boolean isBot;

  @JsonProperty("first_name")
  private String firstName;

  @JsonProperty("username")
  private String username;
}
