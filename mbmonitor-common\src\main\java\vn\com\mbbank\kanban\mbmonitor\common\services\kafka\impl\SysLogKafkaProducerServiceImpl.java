package vn.com.mbbank.kanban.mbmonitor.common.services.kafka.impl;

import java.text.MessageFormat;
import java.util.Date;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.utils.KanbanApplicationUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.SysLogMessageModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.SysLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.CommonKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/19/2025
 */
@Service
@RequiredArgsConstructor
public class SysLogKafkaProducerServiceImpl implements SysLogKafkaProducerService {

  private final CommonKafkaProducerService commonKafkaProducerService;
  private final Logger logger = LoggerFactory.getLogger(this.getClass());
  @Value("${kanban.kafka.topic.jobs}")
  String topic;

  @Override
  public void send(LogActionEnum action, Object... params) {
    try {
      var log = new SysLogModel()
          .setId(GeneratorUtil.generateId())
          .setFunction(action.getFunction())
          .setAction(action)
          .setLogBy(KanbanApplicationUtils.getUserName())
          .setLogDate(DateUtils.formatDate(new Date()))
          .setMessage(new SysLogMessageModel(action.getMessageTemplate(), List.of(params)));
      var message = new BaseKafkaModel<SysLogModel>();
      message.setType(KafkaTypeEnum.LOG);
      message.setValue(log);
      commonKafkaProducerService.send(message);
    } catch (Exception e) {
      logger.error(MessageFormat
          .format("Send Syslog message type {0} error: {1}", action.name(), e.getMessage()), e);
    }
  }
}
