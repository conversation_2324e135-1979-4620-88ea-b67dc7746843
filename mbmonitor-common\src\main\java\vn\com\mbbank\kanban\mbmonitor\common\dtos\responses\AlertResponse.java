package vn.com.mbbank.kanban.mbmonitor.common.dtos.responses;

import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertStatusEnum;

/**
 * Model view alert response.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlertResponse {
  Long id;
  String serviceName;
  String applicationName;
  String content;
  String createdDate;
  String recipient;
  Long priorityConfigId;
  String serviceId;
  String applicationId;
  String priorityName;
  String priorityColor;
  AlertStatusEnum status;
  Long alertGroupId;
  String closedDate;
  String closedBy;
  List<NoteResponse> notes;
  String closedDuration;
}

