package vn.com.mbbank.kanban.mbmonitor.common.dtos.models;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsConfigEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/14/2025
 */
@Data
public class TeamsConfigModel extends TeamsConfigEntity {

  @JsonProperty("tenantId")
  private String tenantId;

  @JsonProperty("client_id")
  private String clientId;

  @JsonProperty("client_secret")
  private String clientSecret;

  private String clientSecretPlaceholder;

  @JsonProperty("username")
  private String email;

  @JsonProperty("password")
  private String password;
}
