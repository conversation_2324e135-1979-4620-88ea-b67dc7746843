package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.configs.caches.SysUserRoleEntityCacheListener;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 9/23/2024
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@EntityListeners(SysUserRoleEntityCacheListener.class)
@Table(name = TableName.SYS_USER_ROLE)
public class SysUserRoleEntity extends BaseEntity<Long> {
  @Id
  @Column(name = "id")
  @SequenceGenerator(name = "SYS_USER_ROLE_SEQ", sequenceName = "SYS_USER_ROLE_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SYS_USER_ROLE_SEQ")
  private Long id;

  @Column(name = "USER_ID")
  private Long userId;

  @Column(name = "ROLE_ID")
  private Long roleId;
}
