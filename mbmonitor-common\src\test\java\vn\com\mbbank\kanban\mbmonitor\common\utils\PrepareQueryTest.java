package vn.com.mbbank.kanban.mbmonitor.common.utils;

import java.util.Map;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

class PrepareQueryTest {

  PrepareQuery prepareQuery;

  @BeforeEach
  void setUp() {
    prepareQuery = new PrepareQuery();
  }

  @Test
  void append1_success() {
    PrepareQuery result = prepareQuery.append(new PrepareQuery("query", "key", "value"));
    Assertions.assertNotNull(result.getParams().get("key"));
  }

  @Test
  void append2_success_conditionTrue() {
    PrepareQuery result = prepareQuery.append(new PrepareQuery("query", "key", "value"), true);
    Assertions.assertNotNull(result.getParams().get("key"));
  }

  @Test
  void append2_success_conditionFalse() {
    PrepareQuery result = prepareQuery.append(new PrepareQuery("query", Map.of("key", "value")), false);
    Assertions.assertNull(result.getParams().get("key"));
  }

  @Test
  void append3_success() {
    PrepareQuery result = prepareQuery.append("query");
    Assertions.assertEquals("query", result.getQueryBuilder().toString());
  }

  @Test
  void append4_success() {
    PrepareQuery result = prepareQuery.append(new StringBuilder("query"));
    Assertions.assertEquals("query", result.getQueryBuilder().toString());
  }

  @Test
  void append5_success_queryNotNU() {
    Map<String, Object> params = Map.of("test", "test");
    PrepareQuery result = prepareQuery.append(new StringBuilder("query"), params);
    Assertions.assertNotNull(result.getParams().get("test"));
  }

  @Test
  void append5_success() {
    Map<String, Object> params = Map.of("test", "test");
    PrepareQuery result = prepareQuery.append(new StringBuilder("query"), params);
    Assertions.assertNotNull(result.getParams().get("test"));
  }

  @Test
  void append6_success() {
    PrepareQuery result = prepareQuery.append(new StringBuilder("query"), "test", "test");
    Assertions.assertNotNull(result.getParams().get("test"));
  }

  @Test
  void append7_success() {
    PrepareQuery result = prepareQuery.append("query", "test", "test");
    Assertions.assertNotNull(result.getParams().get("test"));
  }

  @EnumSource(value = LikeMatcher.class)
  @ParameterizedTest
  void append8_success(LikeMatcher matcher) {
    Map<String, Object> params = Map.of("test", "test");
    var prepareQuery1 = new PrepareQuery("query", params);
    PrepareQuery result = prepareQuery.append(prepareQuery1, matcher);
    Assertions.assertNotNull(result.getParams().get("test"));
  }

  @Test
  void append8_success_casePrepareQueryNull() {
    PrepareQuery appendPrepareQuery = null;
    PrepareQuery result = prepareQuery.append(appendPrepareQuery, LikeMatcher.CONTAINING);
    Assertions.assertNotNull(result);
  }

  @Test
  void append8_success_caseMatcherNull() {
    LikeMatcher matcher = null;
    PrepareQuery result = prepareQuery.append(new PrepareQuery(), matcher);
    Assertions.assertNotNull(result);
  }

  @Test
  void getQuery_success() {
    var res = prepareQuery.getQuery();
    Assertions.assertEquals(res, "");
  }
}
