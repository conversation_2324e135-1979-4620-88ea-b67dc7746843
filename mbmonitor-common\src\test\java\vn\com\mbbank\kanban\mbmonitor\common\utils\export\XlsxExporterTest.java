package vn.com.mbbank.kanban.mbmonitor.common.utils.export;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AttributeInfoDto;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportFileDto;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class XlsxExporterTest {
  @InjectMocks
  XlsxExporter xlsxExporter;
  @Mock
  private ExportFileDto exportFileDto;
  @Mock
  private SXSSFWorkbook workbook;
  private ByteArrayOutputStream outputStream;

  @Mock
  private Sheet sheet;
  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    outputStream = new ByteArrayOutputStream();

  }


  @Test
  void export_success() throws IOException {
    List<String> data = Arrays.asList("test1", "test2");
    when(exportFileDto.getAttributes()).thenReturn(Arrays.asList(new AttributeInfoDto()));
    List<String> batchData = Arrays.asList("data1", "data2");
    List<AttributeInfoDto> attributes = Arrays.asList(new AttributeInfoDto());
    List<Object> transformedData = Arrays.asList();

    when(exportFileDto.getAttributes()).thenReturn(attributes);

    try (MockedStatic<ExcelUtils> excelUtilsMock = mockStatic(ExcelUtils.class)) {
      excelUtilsMock.when(() -> ExcelUtils.transformDataExport(batchData, attributes))
          .thenReturn(transformedData);

      StreamingResponseBody response = xlsxExporter.export(data, exportFileDto);
      response.writeTo(outputStream);
      verify(exportFileDto, atLeastOnce()).getAttributes();
    }
  }

  @Test
  void init_WithTitle_success() throws IOException {
    List<AttributeInfoDto> attributes = Arrays.asList(new AttributeInfoDto());
    when(exportFileDto.getAttributes()).thenReturn(attributes);
    when(exportFileDto.getTitle()).thenReturn(Arrays.asList("Title1", "Title2"));

    xlsxExporter.init(exportFileDto, outputStream);

    verify(exportFileDto, atLeastOnce()).getAttributes();
  }

  @Test
  void init_WithoutTitle_success() throws IOException {
    List<AttributeInfoDto> attributes = Arrays.asList(new AttributeInfoDto());
    when(exportFileDto.getAttributes()).thenReturn(attributes);
    when(exportFileDto.getTitle()).thenReturn(null);

    xlsxExporter.init(exportFileDto, outputStream);

    verify(exportFileDto, times(1)).getTitle();
    verify(exportFileDto, atLeastOnce()).getAttributes();
  }

  @Test
  void writeBatch_success() {
    List<String> batchData = Arrays.asList("data1", "data2");
    List<AttributeInfoDto> attributes = Arrays.asList(new AttributeInfoDto());
    List<Object> transformedData = Arrays.asList();

    when(exportFileDto.getAttributes()).thenReturn(attributes);

    try (MockedStatic<ExcelUtils> excelUtilsMock = mockStatic(ExcelUtils.class)) {
      excelUtilsMock.when(() -> ExcelUtils.transformDataExport(batchData, attributes))
          .thenReturn(transformedData);

      xlsxExporter.writeBatch(batchData, exportFileDto, outputStream);

      excelUtilsMock.verify(() -> ExcelUtils.transformDataExport(batchData, attributes));
    }

    verify(exportFileDto, atLeastOnce()).getAttributes();
  }

  @Test
  void finish_success() throws IOException {
    xlsxExporter.close(outputStream);
  }

  @Test
  void export_EmptyData_success() throws IOException {
    List<String> data = Arrays.asList();
    when(exportFileDto.getAttributes()).thenReturn(Arrays.asList(new AttributeInfoDto()));

    StreamingResponseBody response = xlsxExporter.export(data, exportFileDto);
    response.writeTo(outputStream);

    verify(exportFileDto, atLeastOnce()).getAttributes();
  }


  @Test
  public void writeDataExportFile_sucess() {
    List<AttributeInfoDto> rowData1 = new ArrayList<>();
    rowData1.add(new AttributeInfoDto("row1_attribute1", "row1_name1", "row1_value1"));
    rowData1.add(new AttributeInfoDto("row1_attribute2", "row1_name2", "row1_value2"));

    List<List<AttributeInfoDto>> transformedData = new ArrayList<>();
    transformedData.add(rowData1);

    Workbook workbook = new XSSFWorkbook();
    Sheet sheet = workbook.createSheet("TestData");

    xlsxExporter.writeDataExportFile(sheet, 1, transformedData);
    assertEquals(1, sheet.getLastRowNum());

    Row row1 = sheet.getRow(1);
    assertEquals("row1_value1", row1.getCell(0).getStringCellValue());
    assertEquals("row1_value2", row1.getCell(1).getStringCellValue());

    try {
      workbook.close();
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Test
  public void writeTitleExportFile_success() {
    String title = "Export Title";
    XSSFWorkbook workbook = new XSSFWorkbook();
    Sheet sheet = workbook.createSheet();
    XlsxExporter.writeTitleExportFile(0, sheet, List.of(title), workbook);
    Row titleRow = sheet.getRow(0);
    Cell titleCell = titleRow.getCell(0);
    assertEquals(title, titleCell.getStringCellValue());
    CellStyle cellStyle = titleCell.getCellStyle();
    Font font = workbook.getFontAt(cellStyle.getFontIndex());
    assertTrue(font.getBold());
    assertTrue(font.getItalic());
    assertEquals(IndexedColors.BLUE.getIndex(), font.getColor());
  }

  @Test
  public void writeHeaderExportFile_checkWidthColumn_success() {
    List<AttributeInfoDto> rowData = new ArrayList<>();
    rowData.add(new AttributeInfoDto("row1_attribute1", "row1_name1", "row1_value1"));
    rowData.add(new AttributeInfoDto("row1_attribute2", "row1_name2", "row1_value2"));
    List<List<AttributeInfoDto>> transformedData = new ArrayList<>();
    transformedData.add(rowData);
    Workbook workbook = new XSSFWorkbook();
    Sheet sheet = workbook.createSheet("TestData");
    Row headerRow = sheet.createRow(0);
    xlsxExporter.writeHeaderExportFile(headerRow, rowData, workbook);
    Row row1 = sheet.getRow(0);
    assertEquals("row1_name1", row1.getCell(0).getStringCellValue());
    assertEquals("row1_name2", row1.getCell(1).getStringCellValue());
    try {
      workbook.close();
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Test
  public void setColumnWidth_success() {
    Workbook workbook = new XSSFWorkbook();
    Sheet sheet = workbook.createSheet("TestData");
    List<AttributeInfoDto> attributeInfoList = List.of(
        AttributeInfoDto.builder()
            .width(2060).build(),
        AttributeInfoDto.builder()
            .width(5120).build(),
        new AttributeInfoDto()
    );
    xlsxExporter.setColumnWidth(attributeInfoList, sheet);
    assertEquals(2060, sheet.getColumnWidth(0));
    assertEquals(5120, sheet.getColumnWidth(1));

  }

  @Test
  void rowAndCellCreationWithValidAndInvalidDates_sucess() {
    Sheet sheet = mock(Sheet.class);
    Row row = mock(Row.class);
    when(sheet.createRow(anyInt())).thenReturn(row);
    Cell cell = mock(Cell.class);
    when(row.createCell(anyInt())).thenReturn(cell);
    List<AttributeInfoDto> row1 = new ArrayList<>(Arrays.asList(
        AttributeInfoDto.builder()
            .value("2024-10-07 09:44:31.123").build(),
        AttributeInfoDto.builder()
            .value("invalid_date").build()

    ));
    List<List<AttributeInfoDto>> transformedData = new ArrayList<>();
    transformedData.add(row1);
    int rowNum = 0;
    xlsxExporter.writeDataExportFile(sheet, rowNum, transformedData);
    verify(sheet, times(1)).createRow(rowNum);
    verify(cell, times(1)).setCellValue("2024-10-07 09:44:31.123");
    verify(cell, times(1)).setCellValue("invalid_date");
  }

  @Test
  void exportFile_WithNullTitle() throws Exception {
    when(exportFileDto.getTitle()).thenReturn(null);
    List<AttributeInfoDto> attributes = new ArrayList<>();
    when(exportFileDto.getAttributes()).thenReturn(attributes);
    StreamingResponseBody result = xlsxExporter.export(attributes, exportFileDto);
    assertNotNull(result);
  }

  @Test
  void exportFile_WithEmptyTitle() throws Exception {
    when(exportFileDto.getTitle()).thenReturn(new ArrayList<>());
    List<AttributeInfoDto> attributes = new ArrayList<>();
    when(exportFileDto.getAttributes()).thenReturn(attributes);
    Workbook workbook = new XSSFWorkbook();
    StreamingResponseBody result = xlsxExporter.export(attributes, exportFileDto);
    assertNotNull(result);
  }
}
