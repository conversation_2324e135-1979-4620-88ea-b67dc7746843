package vn.com.mbbank.kanban.mbmonitor.common.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/4/2024
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AclPermission {
  /**
   * Module permission.
   *
   * @return data
   */
  PermissionModuleEnum module();

  /**
   * Module action.
   *
   * @return PermissionActionEnum
   */
  PermissionActionEnum action();
}
