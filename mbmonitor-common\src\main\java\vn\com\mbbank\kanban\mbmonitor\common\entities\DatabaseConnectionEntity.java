package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.annotations.KanbanDisableColumnSearch;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.core.utils.KanbanEncryptorUtils;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.DatabaseConnectionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.OracleDatabaseConnectType;


/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/5/2024
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = TableName.DATABASE_CONNECTION)
public class DatabaseConnectionEntity extends BaseEntity<Long> {
  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "DATABASE_CONNECTION_SEQ", sequenceName = "DATABASE_CONNECTION_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "DATABASE_CONNECTION_SEQ")
  private Long id;
  private String name;
  private String description;
  private DatabaseConnectionTypeEnum type;
  private String host;
  private Integer port;
  private Boolean isActive;
  private String password;
  private String userName;
  //for oracle db
  private OracleDatabaseConnectType oracleConnectType;
  private String sid;
  private String serviceName;
  //for ms sql db
  private String databaseName;

  @Override
  public Long getId() {
    return id;
  }

  @Column(name = "NAME")
  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  @Column(name = "DESCRIPTION")
  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  @Basic
  @Access(AccessType.PROPERTY)
  @Column(name = "PASSWORD")
  @KanbanDisableColumnSearch
  public String getPassword() {
    return KanbanEncryptorUtils.decrypt(this.password);
  }

  public void setPassword(String password) {
    this.password = KanbanEncryptorUtils.encrypt(password);
  }


  @Column(name = "TYPE")
  @Enumerated(EnumType.STRING)
  @Access(AccessType.PROPERTY)
  public DatabaseConnectionTypeEnum getType() {
    return type;
  }

  public void setType(DatabaseConnectionTypeEnum type) {
    this.type = type;
  }

  @Column(name = "HOST")
  @Access(AccessType.PROPERTY)
  public String getHost() {
    return host;
  }

  public void setHost(String host) {
    this.host = host;
  }

  @Column(name = "PORT")
  @Access(AccessType.PROPERTY)
  public Integer getPort() {
    return port;
  }

  public void setPort(Integer port) {
    this.port = port;
  }

  @Column(name = "SID")
  @Access(AccessType.PROPERTY)
  @KanbanDisableColumnSearch
  public String getSid() {
    return sid;
  }

  public void setSid(String sid) {
    this.sid = sid;
  }

  @Column(name = "SERVICE_NAME")
  @Access(AccessType.PROPERTY)
  @KanbanDisableColumnSearch
  public String getServiceName() {
    return serviceName;
  }

  public void setServiceName(String serviceName) {
    this.serviceName = serviceName;
  }

  @Column(name = "IS_ACTIVE")
  @Access(AccessType.PROPERTY)
  @KanbanDisableColumnSearch
  public Boolean getIsActive() {
    return isActive;
  }

  public void setIsActive(Boolean active) {
    isActive = active;
  }


  @Column(name = "ORACLE_CONNECTION_TYPE")
  @Enumerated(EnumType.STRING)
  @Access(AccessType.PROPERTY)
  @KanbanDisableColumnSearch
  public OracleDatabaseConnectType getOracleConnectType() {
    return oracleConnectType;
  }

  public void setOracleConnectType(
      OracleDatabaseConnectType oracleConnectType) {
    this.oracleConnectType = oracleConnectType;
  }

  @Column(name = "USER_NAME")
  @Access(AccessType.PROPERTY)
  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }

  @Column(name = "DATABASE_NAME")
  @Access(AccessType.PROPERTY)
  @KanbanDisableColumnSearch
  public String getDatabaseName() {
    return databaseName;
  }

  public void setDatabaseName(String databaseName) {
    this.databaseName = databaseName;
  }

}
