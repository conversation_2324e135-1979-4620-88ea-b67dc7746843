package vn.com.mbbank.kanban.mbmonitor.common.dtos.models;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.ValidEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExportFileTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 09/26/2024
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExportFileDto {
  @NotNull(message = "List attribute info cannot be empty.!")
  private List<AttributeInfoDto> attributes;
  private List<String> title;
  @ValidEnum(enumClass = ExportFileTypeEnum.class, message = "Invalid file type")
  private ExportFileTypeEnum typeFile;
}
