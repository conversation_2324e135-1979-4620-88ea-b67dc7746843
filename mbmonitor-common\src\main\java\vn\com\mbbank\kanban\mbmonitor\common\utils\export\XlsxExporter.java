package vn.com.mbbank.kanban.mbmonitor.common.utils.export;

import java.io.IOException;
import java.io.OutputStream;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AttributeInfoDto;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportFileDto;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 03/04/2024
 */
public class XlsxExporter implements FileExporter {
  private SXSSFWorkbook workbook;
  private Sheet sheet;
  private int rowIndex;

  @Override
  public <T> StreamingResponseBody export(List<T> data, ExportFileDto exportFileDto) throws IOException {
    return outputStream -> {
      init(exportFileDto, outputStream);
      writeBatch(data, exportFileDto, outputStream);
      close(outputStream);
    };
  }

  @Override
  public void init(ExportFileDto exportFileDto, OutputStream outputStream) throws IOException {
    workbook = new SXSSFWorkbook(100);
    sheet = workbook.createSheet();
    rowIndex = 0;

    var attributes = exportFileDto.getAttributes().stream()
        .sorted(Comparator.comparingLong(AttributeInfoDto::getPosition))
        .collect(Collectors.toList());
    setColumnWidth(attributes, sheet);

    if (exportFileDto.getTitle() != null && !exportFileDto.getTitle().isEmpty()) {
      writeTitleExportFile(rowIndex, sheet, exportFileDto.getTitle(), workbook);
      rowIndex += exportFileDto.getTitle().size() + 1;
    }

    Row headerRow = sheet.createRow(rowIndex);
    writeHeaderExportFile(headerRow, attributes, workbook);
    rowIndex++;
  }

  @Override
  public <T> void writeBatch(List<T> batchData, ExportFileDto exportFileDto, OutputStream outputStream) {
    var transformedData = ExcelUtils.transformDataExport(batchData, exportFileDto.getAttributes());
    writeDataExportFile(sheet, rowIndex, transformedData);
    rowIndex += transformedData.size();
  }

  @Override
  public void close(OutputStream outputStream) throws IOException {
    workbook.write(outputStream);
    outputStream.flush();
    workbook.dispose();
    workbook.close();
  }

  /**
   * Writes a list of titles to an Excel sheet starting from the specified row number.
   *
   * @param rowNum   the starting row number where the titles will be written.
   * @param sheet    the {@link Sheet} object where the titles will be written.
   * @param titles   a list of titles to be written to the sheet.
   * @param workbook the {@link Workbook} object used to create styles and fonts for the cells.
   */
  public static void writeTitleExportFile(int rowNum, Sheet sheet, List<String> titles, Workbook workbook) {
    CellStyle cellStyle = workbook.createCellStyle();
    Font font = workbook.createFont();
    font.setBold(true);
    font.setItalic(true);
    font.setColor(IndexedColors.BLUE.getIndex());
    cellStyle.setFont(font);

    for (int i = 0; i < titles.size(); i++) {
      String title = titles.get(i);

      Row titleRow = sheet.createRow(rowNum);
      Cell cell = titleRow.createCell(0);
      cell.setCellStyle(cellStyle);

      cell.setCellValue(title);
      rowNum++;
    }
  }

  /**
   * Writes header to an export file using the given sheet and transformed data.
   *
   * @param headerRow         The headerRow to which the data will be written.
   * @param attributeInfoList The attributeInfoList data to get header be written to the sheet.
   * @param workbook          The workbook be applied style.
   */
  public static void writeHeaderExportFile(Row headerRow,
                                           List<AttributeInfoDto> attributeInfoList, Workbook workbook) {
    CellStyle headerStyle = workbook.createCellStyle();
    headerStyle.setFillForegroundColor(IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex());
    headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

    Font headerFont = workbook.createFont();
    headerFont.setBold(true);
    headerStyle.setFont(headerFont);

    int cellNum = 0;
    for (AttributeInfoDto attribute : attributeInfoList) {
      Cell cell = headerRow.createCell(cellNum);
      cell.setCellValue(attribute.getAttributeName());
      cell.setCellStyle(headerStyle);
      cellNum++;
    }
  }

  /**
   * Writes data to an export file using the given sheet and transformed data.
   *
   * @param sheet           The sheet to which the data will be written.
   * @param rowNum          The rowNum to which the data will be written.
   * @param transformedData The transformed data to be written to the sheet.
   */
  public static void writeDataExportFile(Sheet sheet, int rowNum,
                                         List<List<AttributeInfoDto>> transformedData) {
    int rowNumClone = rowNum;
    for (List<AttributeInfoDto> row : transformedData) {
      Row excelRow = sheet.createRow(rowNumClone);
      rowNumClone++;
      int cellNum = 0;
      for (AttributeInfoDto attribute : row) {
        Cell cell = excelRow.createCell(cellNum);
        cell.setCellValue(attribute.getValue());
        cellNum++;
      }
    }
  }

  /**
   * Set column width for attribute.
   *
   * @param sheet              The sheet to which the data will be written.
   * @param attributeInfoClone The data will be written.
   */
  public static void setColumnWidth(List<AttributeInfoDto> attributeInfoClone, Sheet sheet) {
    for (int i = 0; i < attributeInfoClone.size(); i++) {
      AttributeInfoDto attribute = attributeInfoClone.get(i);
      if (attribute.getWidth() != null) {
        sheet.setColumnWidth(i, attribute.getWidth());
      }
    }
  }

}
