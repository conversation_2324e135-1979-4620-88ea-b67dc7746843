package vn.com.mbbank.kanban.mbmonitor.common.condition;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mockStatic;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleConverterUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupConverter;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;

class RuleGroupConverterUtilsTest {

  @InjectMocks
  private RuleGroupConverter converter;

  @Mock
  private Logger logger;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    converter = new RuleGroupConverter();
  }

  @Test
  void testConvertToDatabaseColumn_shouldReturnJsonString() {
    RuleGroupType ruleGroupType = new RuleGroupType();
    String json = converter.convertToDatabaseColumn(ruleGroupType);

    Assertions.assertNotNull(json);
    Assertions.assertFalse(json.contains("testValue"));
  }

  @Test
  void testConvertToEntityAttribute_shouldReturnRuleGroupTypeObject() {
    String json = "{\"combinator\":\"testValue\"}";
    RuleGroupType ruleGroupType = converter.convertToEntityAttribute(json);
    Assertions.assertNull(ruleGroupType);
  }

  @Test
  void testConvertToEntityAttribute_shouldReturnRuleGroupTypeObject_success() {
    String json = "{\"someProperty\":\"testValue\"}";
    RuleGroupType mockRuleGroupType = new RuleGroupType();
    try (MockedStatic<RuleConverterUtils> mockedStatic = Mockito.mockStatic(RuleConverterUtils.class)) {
      mockedStatic.when(() -> RuleConverterUtils.convertStringToRuleGroupType(json))
          .thenReturn(mockRuleGroupType);
      RuleGroupType result = converter.convertToEntityAttribute(json);
      Assertions.assertNotNull(result);
    }
  }
}
