package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.KanbanAutoGenerateUlId;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/16/2025
 */
@Entity
@Table(name = "TEAMS_GROUP_CONFIG")
@Data
@KanbanAutoGenerateUlId
public class TeamsGroupConfigEntity extends BaseEntity<String> {
  @Id
  @Column(name = "ID")
  private String id;

  @Column(name = "TEAMS_CONFIG_ID")
  private String teamsConfigId;

  @Column(name = "TEAMS_GROUP_ID")
  private String teamsGroupId;

  @Column(name = "TEAMS_GROUP_NAME")
  private String teamsGroupName;

  @Column(name = "POSITION")
  private Integer position;

}
