package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;

@Entity
@Data
@Table(name = TableName.SYS_USER_REFRESH_TOKEN)
@EqualsAndHashCode(callSuper = true)
public class SysUserRefreshTokenEntity extends BaseEntity<String> {
  @Id
  @Column(name = "ID")
  private String id = GeneratorUtil.generateId();

  @Column(name = "USERNAME", nullable = false)
  private String userName;

  @Column(name = "REFRESH_TOKEN", nullable = false)
  private String refreshToken;

  @Column(name = "EXPIRED_DATE", nullable = false)
  @Temporal(TemporalType.TIMESTAMP)
  private Date expiredDate;
}
