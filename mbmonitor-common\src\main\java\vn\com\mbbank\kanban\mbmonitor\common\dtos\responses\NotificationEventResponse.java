package vn.com.mbbank.kanban.mbmonitor.common.dtos.responses;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationTypeEnum;

/**
 * Response DTO for notification event.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationEventResponse {
  private String id;
  private String title;
  private String content;
  private NotificationTypeEnum notificationType;
  private String scheduleType;
  private Date triggeredDate;
  private String cronExpression;
  private Boolean active;
  @Builder.Default
  private List<String> userNames = new ArrayList<>();
  @Builder.Default
  private List<Long> roleIds = new ArrayList<>();
}