package vn.com.mbbank.kanban.mbmonitor.common.dtos.responses;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeUnitEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;

/**
 * AlertGroupConfigResponse.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MaintenanceTimeConfigResponse {
  Long id;
  String name;
  String description;
  @Builder.Default
  Boolean active = true;
  MaintenanceTimeConfigTypeEnum type;
  Integer nextTime;
  MaintenanceTimeUnitEnum unit;
  String cronExpression;
  Date startTime;
  Date endTime;
  RuleGroupType ruleGroup;

  @Builder.Default
  List<String> serviceIds = new ArrayList<>();
  @Builder.Default
  List<String> applicationIds = new ArrayList<>();
  String status;
  String value;
  Date createdDate;
}
