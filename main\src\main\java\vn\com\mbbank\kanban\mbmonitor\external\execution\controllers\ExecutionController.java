package vn.com.mbbank.kanban.mbmonitor.external.execution.controllers;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecutionScriptRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionScriptResponse;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ExternalExecutionUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.external.execution.services.ExecutionService;

/**
 * ExecutionController.
 */
@RestController
@RequestMapping(ExternalExecutionUrl.EXECUTE_SCRIPT)
@RequiredArgsConstructor
public class ExecutionController extends BaseController {

  private final ExecutionService executionService;

  /**
   * Execute script.
   *
   * @param request executionRunningRequest.
   * @return ExecutionResponse.
   */
  @PostMapping
  ResponseData<ExecutionScriptResponse> execute(@RequestBody @Valid ExecutionScriptRequest request)
      throws BusinessException {
    return ResponseUtils.success(executionService.execute(request));
  }

}
