package vn.com.mbbank.kanban.mbmonitor.common.utils;

import vn.com.mbbank.kanban.core.utils.KanbanEncryptorUtils;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 1/7/2025
 */
public class RedisUtils {
  /**
   * Get key response key.
   *
   * @param prefix   prefix
   * @param userName userName
   * @return key
   */
  public static String MONITOR_USER_RESPONSE_PREFIX = "MONITOR_USER_RESPONSE";

  public static final String MONITOR_TEAMS_TOKEN_PREFIX = "MONITOR_TEAMS_TOKEN";

  /**
   * Get key case api get me.
   *
   * @param userName userName
   * @return string key
   */
  public static String getUserResponseKey(String userName) {
    return String.format("%s_%s", MONITOR_USER_RESPONSE_PREFIX, userName);
  }

  /**
   * get teams token key.
   *
   * @param clientId clientId
   * @param email email
   * @return key
   */
  public static String getTeamsTokenRedisKey(String clientId, String email) {
    return String.format("%s_%s_%s", MONITOR_TEAMS_TOKEN_PREFIX, clientId,
        KanbanEncryptorUtils.encrypt(email));
  }
}
