package vn.com.mbbank.kanban.mbmonitor.common.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.DatabaseCollectRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseCollectEntity;


/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/14/2024
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DatabaseCollectRequestToEntityMapper extends
    KanbanBaseMapper<DatabaseCollectRequest, DatabaseCollectEntity> {
  DatabaseCollectRequestToEntityMapper INSTANCE =
      Mappers.getMapper(DatabaseCollectRequestToEntityMapper.class);
}
