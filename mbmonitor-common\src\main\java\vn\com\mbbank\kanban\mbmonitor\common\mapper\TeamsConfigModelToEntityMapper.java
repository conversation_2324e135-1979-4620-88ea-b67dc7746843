package vn.com.mbbank.kanban.mbmonitor.common.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.TeamsConfigModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsConfigEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/16/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TeamsConfigModelToEntityMapper extends
    KanbanBaseMapper<TeamsConfigModel, TeamsConfigEntity> {
  TeamsConfigModelToEntityMapper INSTANCE = Mappers.getMapper(TeamsConfigModelToEntityMapper.class);
}