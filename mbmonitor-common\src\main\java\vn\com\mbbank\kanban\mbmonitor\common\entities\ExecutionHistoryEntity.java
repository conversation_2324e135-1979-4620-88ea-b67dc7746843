package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.converter.ExecuteScriptParamConverter;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecuteScriptParamModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;

@Entity
@Data
@Table(name = TableName.EXECUTION_HISTORY)
@EqualsAndHashCode(callSuper = true)
public class ExecutionHistoryEntity extends BaseEntity<String> {

  @Id
  @Column(name = "ID")
  private String id = GeneratorUtil.generateId();

  @Column(name = "EXECUTION_NAME")
  private String executionName;

  @Column(name = "EXECUTION_DESCRIPTION")
  private String executionDescription;

  @Column(name = "EXECUTION_SCRIPT")
  private String executionScript;

  @Column(name = "EXECUTION_TYPE")
  @Enumerated(EnumType.STRING)
  private ExecutionTypeEnum executionType;

  @Column(name = "EXECUTION_PARAMS")
  @Convert(converter = ExecuteScriptParamConverter.class)
  private List<ExecuteScriptParamModel> executionParams;

  @Column(name = "STATUS")
  @Enumerated(EnumType.STRING)
  private ExecutionStatusEnum status;

  @Column(name = "RESULT")
  private String result;

  @Column(name = "ERROR")
  private String error;

  @Column(name = "EXECUTION_BY")
  private String executionBy;

  @Column(name = "START_TIME")
  @Temporal(TemporalType.TIMESTAMP)
  private Date startTime;

  @Column(name = "END_TIME")
  @Temporal(TemporalType.TIMESTAMP)
  private Date endTime;

  @Override
  public String getId() {
    return id;
  }
}
