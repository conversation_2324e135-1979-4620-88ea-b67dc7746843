package vn.com.mbbank.kanban.mbmonitor.common.enums;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 9/23/2024
 */
public enum PermissionModuleEnum {
  WEBHOOK_CONFIG,
  INPUT_DATA_CONFIG,
  M<PERSON><PERSON><PERSON>_ALERT,
  R<PERSON>ORT,
  DAS<PERSON><PERSON>AR<PERSON>,
  <PERSON>ER_MANAGEMENT,
  <PERSON><PERSON><PERSON>_MANAGEMENT,
  APPL<PERSON>ATION_MANAGEMENT,
  SERVICE_MANAGEMENT,
  CUSTOM_OBJECT,
  PRIORITY_CONFIG,
  DATABASE_CONNECTION,
  EMAIL_PARTNER_CONFIG,
  EMAIL_TEMPLATE_CONFIG,
  EMAIL_CONFIG,
  SEND_EMAIL,
  ALERT_GROUP_CONFIG,
  EMAIL_COLLECT,
  EMAIL_CONNECTION,
  DATABASE_COLLECT,
  MAINTENANCE_TIME_CONFIG,
  TASK,
  FILTER_ALERT_CONFIG,
  TELEGRAM_ALERT_CONFIG,

  TEAMS_ALERT_CONFIG,
  MODIFY_ALERT_CONFIG,
  DATABASE_THRESHOLD_CONFIG,
  SYSLOG,
  EXECUTION,
  EXEC<PERSON>ION_GROUP,
  VA<PERSON><PERSON>LE,
  RUN_EXECUTION,
  EXECUTION_HISTORY,
  UNKNOWN,
  AUTO_TRIGGER_ACTION_CONFIG,
  ALERT_REQUEST,
  RPA_CONFIG,
  MONITOR_WEB_CONFIG,
  NOTIFICATION_EVENT
}
