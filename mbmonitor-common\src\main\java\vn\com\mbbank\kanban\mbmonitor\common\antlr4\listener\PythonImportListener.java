package vn.com.mbbank.kanban.mbmonitor.common.antlr4.listener;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.antlr4.PythonParser;
import vn.com.mbbank.kanban.mbmonitor.common.antlr4.PythonParserBaseListener;


/**
 * Listener to collect import statements and potential environment variables.
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@Builder
public class PythonImportListener extends PythonParserBaseListener {
  private static final Set<String> PYTHON_BUILT_INS = Set.of(
      "print", "len", "type", "str", "int", "float", "list", "dict", "set", "tuple",
      "range", "open", "input", "abs", "all", "any", "bool", "bytes", "callable",
      "chr", "classmethod", "compile", "complex", "delattr", "dir", "divmod",
      "enumerate", "eval", "exec", "filter", "format", "frozenset", "getattr",
      "globals", "hasattr", "hash", "help", "hex", "id", "isinstance", "issubclass",
      "iter", "locals", "map", "max", "memoryview", "min", "next", "object", "oct",
      "ord", "pow", "property", "repr", "reversed", "round", "setattr", "slice",
      "sorted", "staticmethod", "sum", "super", "vars", "zip", "__import__"
  );
  private final Set<String> imports = new HashSet<>();
  private final Set<String> environmentVariables = new HashSet<>();
  private final Set<String> osAndBuiltInFunctions = new HashSet<>();

  @Override
  public void enterImport_stmt(PythonParser.Import_stmtContext ctx) {
    if (Objects.nonNull(ctx.import_name())) {
      // Regular import statements
      PythonParser.Dotted_as_namesContext dottedAsNames = ctx.import_name().dotted_as_names();
      if (Objects.nonNull(dottedAsNames)) {
        for (PythonParser.Dotted_as_nameContext dottedAsName : dottedAsNames.dotted_as_name()) {
          if (Objects.nonNull(dottedAsName.dotted_name())) {
            imports.add(dottedAsName.dotted_name().getText());
          }
        }
      }
    } else if (Objects.nonNull(ctx.import_from())) {
      // From import statements
      PythonParser.Import_fromContext importFrom = ctx.import_from();
      if (Objects.nonNull(importFrom.dotted_name())) {
        imports.add(importFrom.dotted_name().getText());
      }
    }
  }

  /**
   * Check for subscript operations like:
   * os.environ['VAR_NAME']
   * os.environ.get('KEY')
   * os.environ.get('KEY', default)
   * os.getenv('KEY')
   * os.getenv('KEY', default)
   * get buildin function name
   */
  @Override
  public void enterPrimary(PythonParser.PrimaryContext ctx) {

    var leftPar = ctx.LPAR(); // token '('
    var rightPar = ctx.RPAR(); // token ')'
    var leftSqb = ctx.LSQB(); // token '['
    var rightSqb = ctx.RSQB(); // token ']'
    var primary = ctx.primary();
    var arguments = ctx.arguments();

    // get all os method
    if (ctx.getText().startsWith("os.") && Objects.nonNull(primary)) {
      if ((Objects.nonNull(leftPar) && Objects.nonNull(rightPar))
          || (Objects.nonNull(leftSqb) && Objects.nonNull(rightSqb))) {
        var functionName = primary.getText();
        if (!KanbanCommonUtil.isEmpty(functionName)) {
          osAndBuiltInFunctions.add(functionName);
        }
      }

    }

    if (Objects.nonNull(leftPar) && Objects.nonNull(rightPar) && Objects.nonNull(primary)) {

      // os.environ.get('KEY')
      // os.environ.get('KEY', default)
      // os.getenv('KEY')
      // os.getenv('KEY', default)
      if (("os.environ.get".equals(primary.getText()) || "os.getenv".equals(primary.getText()))
          && Objects.nonNull(arguments)) {
        var args = arguments.args();
        if (Objects.nonNull(args) && args.getChildCount() >= 1) {
          extractEnvironmentVarName(args.getChild(0).getText());
        }
      }

      // check buildin function
      if (Objects.nonNull(primary.atom()) && Objects.nonNull(primary.atom().name())) {
        String functionName = primary.atom().name().getText();
        if (PYTHON_BUILT_INS.contains(functionName)) {
          osAndBuiltInFunctions.add(functionName);
        }
      }
    }

    // os.environ['VAR_NAME']
    if (Objects.nonNull(leftSqb) && Objects.nonNull(rightSqb) && Objects.nonNull(primary)) {
      if ("os.environ".equals(primary.getText()) && ctx.getChildCount() >= 4) {
        extractEnvironmentVarName(ctx.getChild(2).getText());
      }
    }
  }

  /**
   * Process string literals that may be environment variable names.
   *
   * @param text input
   */
  private void extractEnvironmentVarName(String text) {
    // Remove quotes (both single and double) if they exist
    if ((text.startsWith("\"") && text.endsWith("\"")) || (text.startsWith("'") && text.endsWith("'"))) {
      String envName = text.substring(1, text.length() - 1);
      environmentVariables.add(envName);
    }
  }
}
