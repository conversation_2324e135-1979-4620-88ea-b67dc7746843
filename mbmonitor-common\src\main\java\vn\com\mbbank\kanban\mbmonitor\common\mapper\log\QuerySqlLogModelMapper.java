package vn.com.mbbank.kanban.mbmonitor.common.mapper.log;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.QuerySqlLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.QuerySqlEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface QuerySqlLogModelMapper extends
    KanbanBaseMapper<QuerySqlLogModel, QuerySqlEntity> {
  QuerySqlLogModelMapper INSTANCE = Mappers.getMapper(QuerySqlLogModelMapper.class);
}
