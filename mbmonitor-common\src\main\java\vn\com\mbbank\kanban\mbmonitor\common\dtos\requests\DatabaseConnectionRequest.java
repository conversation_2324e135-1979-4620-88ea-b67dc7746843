package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.enums.DatabaseConnectionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.OracleDatabaseConnectType;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/5/2024
 */
@Data
public class DatabaseConnectionRequest {

  private Long id;

  @NotNull
  @NotBlank
  @Size(min = 1, max = CommonConstants.DATABASE_CONNECTION_NAME_MAX_LENGTH)
  private String name;

  @Size(max = CommonConstants.DATABASE_CONNECTION_DESCRIPTION_MAX_LENGTH)
  private String description;

  @NotNull
  private DatabaseConnectionTypeEnum type;

  @NotNull
  @NotBlank
  @Size(min = 1, max = CommonConstants.DATABASE_CONNECTION_HOST_MAX_LENGTH)
  private String host;

  @NotNull
  @Min(CommonConstants.DATABASE_CONNECTION_PORT_MIN)
  @Max(CommonConstants.DATABASE_CONNECTION_PORT_MAX)
  private Integer port;

  @NotNull
  @NotBlank
  @Size(min = 1, max = CommonConstants.DATABASE_CONNECTION_USERNAME_MAX_LENGTH)
  private String userName;

  @NotNull
  @NotBlank
  @Size(min = 1, max = CommonConstants.DATABASE_CONNECTION_PASSWORD_MAX_LENGTH)
  private String password;

  private boolean isActive;

  // for oracle connection
  private OracleDatabaseConnectType oracleConnectType;

  @Size(max = CommonConstants.DATABASE_CONNECTION_ORACLE_SERVICE_NAME_MAX_LENGTH)
  private String sid;
  @Size(max = CommonConstants.DATABASE_CONNECTION_ORACLE_SERVICE_NAME_MAX_LENGTH)
  private String serviceName;

  // for ms sql connection
  @Size(max = CommonConstants.DATABASE_CONNECTION_DATABASE_NAME_MAX_LENGTH)
  private String databaseName;
}
