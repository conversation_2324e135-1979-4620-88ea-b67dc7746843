CREATE SEQUENCE MBMONITOR.FILTER_ALERT_CONFIG_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.GROUP_QUERY_SQL_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/
CREATE SEQUENCE MBMONITOR.QUERY_SQL_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.MAINTENANCE_TIME_CONFIG_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.MAINTENANCE_TIME_CONFIG_DEPENDENCY_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.TASK_REFERENCE_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.TASK_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.TASK_USER_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.PARAM_QUERY_SQL_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.MODIFY_ALERT_CONFIG_MODIFY_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.MODIFY_ALERT_CONFIG_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.MODIFY_ALERT_CONFIG_POSITION_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.MODIFY_ALERT_CONFIG_DEPENDENCY_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE TABLE MBMONITOR.GROUP_QUERY_SQL
(
    ID            NUMBER DEFAULT MBMONITOR.GROUP_QUERY_SQL_SEQ.NEXTVAL NOT NULL
        CONSTRAINT GROUP_QUERY_SQL_pk
            PRIMARY KEY,
    NAME          VARCHAR2(100 CHAR),
    DESCRIPTION   VARCHAR2(300 CHAR),
    ACTIVE        NUMBER,
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255)
)
/
CREATE TABLE MBMONITOR.QUERY_SQL
(
    ID                     NUMBER DEFAULT MBMONITOR.QUERY_SQL_SEQ.NEXTVAL NOT NULL
        CONSTRAINT QUERY_SQL_pk
            PRIMARY KEY,
    NAME                   VARCHAR2(100 CHAR),
    DESCRIPTION            VARCHAR2(300 CHAR),
    ACTIVE                 NUMBER,
    COMMAND                VARCHAR2(4000 CHAR),
    GROUP_QUERY_SQL_ID     NUMBER,
    DATABASE_CONNECTION_ID NUMBER,
    CREATED_DATE           TIMESTAMP(6),
    CREATED_BY             VARCHAR2(255),
    MODIFIED_DATE          TIMESTAMP(6),
    MODIFIED_BY            VARCHAR2(255)
)
/

CREATE INDEX MBMONITOR.QUERY_SQL_GROUP_QUERY_SQL_ID_INDEX
    ON MBMONITOR.QUERY_SQL (GROUP_QUERY_SQL_ID) TABLESPACE INDEXS
/
CREATE INDEX MBMONITOR.QUERY_SQL_DATABASE_CONNECTION_ID_INDEX
    ON MBMONITOR.QUERY_SQL (DATABASE_CONNECTION_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.MAINTENANCE_TIME_CONFIG
(
    ID              NUMBER       DEFAULT MBMONITOR.MAINTENANCE_TIME_CONFIG_SEQ.NEXTVAL,
    NAME            VARCHAR2(100 CHAR),
    DESCRIPTION     VARCHAR2(300 CHAR),
    TYPE            VARCHAR2(30 BYTE),
    NEXT_TIME       NUMBER(3, 0),
    UNIT            VARCHAR2(30 BYTE),
    CRON_EXPRESSION VARCHAR2(30 BYTE),
    START_TIME      TIMESTAMP(6),
    END_TIME        TIMESTAMP(6),
    CREATED_DATE    TIMESTAMP(6),
    CREATED_BY      VARCHAR2(100 BYTE),
    MODIFIED_DATE   TIMESTAMP(6),
    MODIFIED_BY     VARCHAR2(100 BYTE),
    ACTIVE          NUMBER(1, 0) DEFAULT 1,
    CONDITION_TYPE  VARCHAR2(30 BYTE),
    RULE_GROUP      CLOB
)
/

CREATE TABLE MBMONITOR.MAINTENANCE_TIME_CONFIG_DEPENDENCY
(
    ID                         NUMBER DEFAULT MBMONITOR.MAINTENANCE_TIME_CONFIG_DEPENDENCY_SEQ.NEXTVAL,
    DEPENDENCY_ID              VARCHAR2(10 BYTE),
    CREATED_DATE               TIMESTAMP(6),
    CREATED_BY                 VARCHAR2(100 BYTE),
    MODIFIED_DATE              TIMESTAMP(6),
    MODIFIED_BY                VARCHAR2(100 BYTE),
    MAINTENANCE_TIME_CONFIG_ID NUMBER,
    TYPE                       VARCHAR2(50 BYTE)
)
/

CREATE INDEX MBMONITOR.MAINTENANCE_TIME_CONFIG_DEPENDENCY_MAINTENANCE_TIME_CONFIG_ID_TYPE_DEPENDENCE_ID_index
    ON MBMONITOR.MAINTENANCE_TIME_CONFIG_DEPENDENCY (MAINTENANCE_TIME_CONFIG_ID, TYPE, DEPENDENCY_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.TASK
(
    ID                         NUMBER DEFAULT MBMONITOR.TASK_SEQ.NEXTVAL NOT NULL,
    NAME                       VARCHAR2(100),
    DESCRIPTION                VARCHAR2(300 CHAR),
    STATUS                     VARCHAR2(30),
    START_TIME                 TIMESTAMP(6),
    END_TIME                   TIMESTAMP(6),
    TYPE                       VARCHAR2(30),
    TIME_TYPE                  VARCHAR2(30),
    CURRENT_ASSIGNEE_USER_NAME VARCHAR2(255 CHAR),
    CREATED_DATE               TIMESTAMP(6),
    CREATED_BY                 VARCHAR2(30),
    MODIFIED_DATE              TIMESTAMP(6),
    MODIFIED_BY                VARCHAR2(255),
    SHIFT                      VARCHAR2(30),
    DELETED                    NUMBER,
    DELETED_BY                 VARCHAR2(30),
    DELETED_DATE               TIMESTAMP(6)
)
    PARTITION BY RANGE (START_TIME) INTERVAL (NUMTOYMINTERVAL(1, 'MONTH'))
(
    PARTITION PARTBASE VALUES LESS THAN (TO_DATE('2024-10-01 00:00:00', 'SYYYY-MM-DD HH24:MI:SS',
                                                 'NLS_CALENDAR=GREGORIAN'))
)
/

ALTER TABLE MBMONITOR.TASK
    ADD CONSTRAINT TASK_PK PRIMARY KEY (ID, START_TIME) USING INDEX LOCAL
/

CREATE INDEX MBMONITOR.TASK_CURRENT_ASSIGNEE_USER_NAME_INDEX
    ON MBMONITOR.TASK (CURRENT_ASSIGNEE_USER_NAME) LOCAL TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.TASK_START_TIME_INDEX
    ON MBMONITOR.TASK (START_TIME) LOCAL TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.TASK_END_TIME_INDEX
    ON MBMONITOR.TASK (END_TIME) LOCAL TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.TASK_CREATED_BY_INDEX
    ON MBMONITOR.TASK (CREATED_BY) LOCAL TABLESPACE INDEXS
/

CREATE INDEX TASK_CREATED_DATE_ID_INDEX
    ON TASK (CREATED_DATE, ID) LOCAL TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.TASK_REFERENCE
(
    ID               NUMBER DEFAULT MBMONITOR.TASK_REFERENCE_SEQ.NEXTVAL NOT NULL
        PRIMARY KEY,
    PARENT_TASK_ID   NUMBER,
    CHILDREN_TASK_ID NUMBER,
    CREATED_DATE     TIMESTAMP(6),
    CREATED_BY       VARCHAR2(30),
    MODIFIED_DATE    TIMESTAMP(6),
    MODIFIED_BY      VARCHAR2(255)
)
/

CREATE INDEX MBMONITOR.TASK_REFERENCE_CHILDREN_TASK_ID_INDEX
    ON MBMONITOR.TASK_REFERENCE (CHILDREN_TASK_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.TASK_REFERENCE_PARENT_TASK_ID_INDEX
    ON MBMONITOR.TASK_REFERENCE (PARENT_TASK_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.TASK_USER
(
    ID            NUMBER DEFAULT MBMONITOR.TASK_USER_SEQ.NEXTVAL NOT NULL
        PRIMARY KEY,
    USER_NAME     VARCHAR2(255 CHAR),
    TASK_ID       NUMBER,
    TYPE          VARCHAR2(30),
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(30),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255)
)
/

CREATE INDEX MBMONITOR.TASK_USER_TASK_ID_INDEX
    ON MBMONITOR.TASK_USER (TASK_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.TASK_USER_USER_NAME_INDEX
    ON MBMONITOR.TASK_USER (USER_NAME) TABLESPACE INDEXS
/
-- update permission
ALTER TABLE MBMONITOR.SYS_ROLE_PERMISSION
    ADD (
        TYPE VARCHAR2(100),
        MODULE_ID NUMBER(22, 0),
        MODULE_PARENT_ID NUMBER(22, 0)
        )
/

UPDATE MBMONITOR.SYS_ROLE_PERMISSION
SET TYPE = 'MODULE'
/

CREATE INDEX MBMONITOR.SYS_ROLE_PERMISSION_MODULE_ID_INDEX ON MBMONITOR.SYS_ROLE_PERMISSION (MODULE_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.SYS_ROLE_PERMISSION_MODULE_PARENT_ID_INDEX ON MBMONITOR.SYS_ROLE_PERMISSION (MODULE_PARENT_ID) TABLESPACE INDEXS
/

-- end update permission

CREATE TABLE MBMONITOR.FILTER_ALERT_CONFIG
(
    ID            NUMBER    DEFAULT MBMONITOR.FILTER_ALERT_CONFIG_SEQ.NEXTVAL NOT NULL
        CONSTRAINT FILTER_ALERT_CONFIG_pk
            PRIMARY KEY,
    NAME          VARCHAR2(100 CHAR)                                          NOT NULL,
    DESCRIPTION   VARCHAR2(300 CHAR),
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(100),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(100),
    ACTIVE        NUMBER(1) DEFAULT 1,
    RULE_GROUP    CLOB
)
/

CREATE TABLE MBMONITOR.PARAM_QUERY_SQL
(
    ID            NUMBER DEFAULT MBMONITOR.PARAM_QUERY_SQL_SEQ.NEXTVAL NOT NULL
        CONSTRAINT PARAM_QUERY_SQL_pk
            PRIMARY KEY,
    value         VARCHAR2(300 CHAR),
    QUERY_SQL_ID  NUMBER,
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255)
)
/

CREATE INDEX MBMONITOR.PARAM_QUERY_SQL_QUERY_SQL_ID_INDEX
    ON MBMONITOR.PARAM_QUERY_SQL (QUERY_SQL_ID) TABLESPACE INDEXS
/

DROP INDEX MBMONITOR.ALERT_CREATED_DATE_ID_INDEX
/

CREATE INDEX MBMONITOR.ALERT_CREATED_DATE_ID_INDEX
    ON ALERT (CREATED_DATE, ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.ALERT_CLOSED_DURATION_INDEX
    ON ALERT (CLOSED_DURATION) TABLESPACE INDEXS
/

ALTER TABLE MBMONITOR.ALERT
    ADD SOURCE          VARCHAR2(30)
    ADD CLOSED_DATE     TIMESTAMP(6)
    ADD CLOSED_BY       VARCHAR2(255)
    ADD CLOSED_DURATION NUMBER
/

CREATE INDEX MBMONITOR.ALERT_CLOSED_BY_INDEX
    ON MBMONITOR.ALERT (CLOSED_BY) LOCAL TABLESPACE INDEXS
/

ALTER TABLE MBMONITOR.COLLECT_EMAIL_CONFIG
    ADD INTERVAL_TIME_SEC NUMBER;

UPDATE MBMONITOR.COLLECT_EMAIL_CONFIG
SET INTERVAL_TIME_SEC = CASE INTERVAL_TIME
                            WHEN 'FIFTEEN_SECONDS' THEN 15
                            WHEN 'THIRTY_SECONDS' THEN 30
                            WHEN 'ONE_MINUTE' THEN 60
                            WHEN 'TWO_MINUTES' THEN 120
                            WHEN 'THREE_MINUTES' THEN 180
                            WHEN 'FIVE_MINUTES' THEN 300
                            WHEN 'TEN_MINUTES' THEN 600
                            WHEN 'THIRTY_MINUTES' THEN 1800
                            ELSE NULL
    END;

ALTER TABLE MBMONITOR.COLLECT_EMAIL_CONFIG
    DROP COLUMN INTERVAL_TIME;
ALTER TABLE MBMONITOR.COLLECT_EMAIL_CONFIG RENAME COLUMN INTERVAL_TIME_SEC TO INTERVAL_TIME;

ALTER TABLE MBMONITOR.EMAIL_CONFIG
    ADD INTERVAL_TIME_SEC NUMBER;
UPDATE MBMONITOR.EMAIL_CONFIG
SET INTERVAL_TIME_SEC = CASE INTERVAL_TIME
                            WHEN 'FIFTEEN_SECONDS' THEN 15
                            WHEN 'THIRTY_SECONDS' THEN 30
                            WHEN 'ONE_MINUTE' THEN 60
                            WHEN 'TWO_MINUTES' THEN 120
                            WHEN 'THREE_MINUTES' THEN 180
                            WHEN 'FIVE_MINUTES' THEN 300
                            WHEN 'TEN_MINUTES' THEN 600
                            WHEN 'THIRTY_MINUTES' THEN 1800
                            ELSE NULL
    END;

ALTER TABLE MBMONITOR.EMAIL_CONFIG
    DROP COLUMN INTERVAL_TIME;

ALTER TABLE MBMONITOR.EMAIL_CONFIG
    RENAME COLUMN INTERVAL_TIME_SEC TO INTERVAL_TIME;

CREATE TABLE MBMONITOR.MODIFY_ALERT_CONFIG
(
    ID            NUMBER    DEFAULT MBMONITOR.MODIFY_ALERT_CONFIG_SEQ.NEXTVAL          NOT NULL
        CONSTRAINT MODIFY_ALERT_CONFIG_pk
            PRIMARY KEY,
    NAME          VARCHAR2(100 CHAR)                                                   NOT NULL,
    DESCRIPTION   VARCHAR2(300 CHAR),
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(100),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(100),
    ACTIVE        NUMBER(1) DEFAULT 1,
    RULE_GROUP    CLOB,
    POSITION      NUMBER    DEFAULT MBMONITOR.MODIFY_ALERT_CONFIG_POSITION_SEQ.NEXTVAL NOT NULL
)
/

CREATE TABLE MBMONITOR.MODIFY_ALERT_CONFIG_DEPENDENCY
(
    ID                     NUMBER DEFAULT MBMONITOR.MODIFY_ALERT_CONFIG_DEPENDENCY_SEQ.NEXTVAL NOT NULL
        CONSTRAINT MODIFY_ALERT_CONFIG_DEPENDENCY_PK
            PRIMARY KEY,
    DEPENDENCY_ID          VARCHAR2(10)                                                        NOT NULL,
    CREATED_DATE           TIMESTAMP(6),
    CREATED_BY             VARCHAR2(100),
    MODIFIED_DATE          TIMESTAMP(6),
    MODIFIED_BY            VARCHAR2(100),
    MODIFY_ALERT_CONFIG_ID NUMBER(10)                                                          NOT NULL,
    TYPE                   VARCHAR2(50)                                                        NOT NULL
)
/


CREATE INDEX MBMONITOR.MODIFY_ALERT_CONFIG_DEPENDENCY_MODIFY_ALERT_CONFIG_ID_DEPENDENCE_ID_INDEX
    ON MBMONITOR.MODIFY_ALERT_CONFIG_DEPENDENCY (MODIFY_ALERT_CONFIG_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.MODIFY_ALERT_CONFIG_MODIFY
(
    ID                     NUMBER DEFAULT MBMONITOR.MODIFY_ALERT_CONFIG_MODIFY_SEQ.NEXTVAL NOT NULL
        CONSTRAINT MODIFY_ALERT_CONFIG_MODIFY_SEQ_PK
            PRIMARY KEY,
    FIELD_NAME           VARCHAR2(10)                                                    NOT NULL,
    CREATED_DATE           TIMESTAMP(6),
    CREATED_BY             VARCHAR2(100),
    MODIFIED_DATE          TIMESTAMP(6),
    MODIFIED_BY            VARCHAR2(100),
    MODIFY_ALERT_CONFIG_ID NUMBER(10)                                                      NOT NULL,
    FIELD_VALUE           VARCHAR2(2000 CHAR)                                                  NOT NULL,
    CONTENT_HTML           VARCHAR2(4000 CHAR)
)
/
CREATE INDEX MBMONITOR.MODIFY_ALERT_CONFIG_MODIFY_MODIFY_ALERT_CONFIG_ID_ID_INDEX
    ON MBMONITOR.MODIFY_ALERT_CONFIG_MODIFY (MODIFY_ALERT_CONFIG_ID) TABLESPACE INDEXS
/


ALTER TABLE MBMONITOR.ALERT_GROUP_CONFIG_DEPENDENCY
    MODIFY TYPE VARCHAR2(50)
/

CREATE TABLE MBMONITOR.TELEGRAM_ALERT_CONFIG
(
    ID                 VARCHAR2(50) NOT NULL ENABLE PRIMARY KEY,
    TELEGRAM_CONFIG_ID VARCHAR2(50),
    SERVICE_ID         VARCHAR2(50),
    APPLICATION_ID     VARCHAR2(50),
    TYPE               VARCHAR2(255),
    GROUP_CHAT_ID      VARCHAR2(500),
    "ACTIVE"           NUMBER(1, 0),
    CREATED_DATE       TIMESTAMP(6),
    CREATED_BY         VARCHAR2(30),
    MODIFIED_DATE      TIMESTAMP(6),
    MODIFIED_BY        VARCHAR2(255),
    CHECK (ACTIVE IN (0, 1)) ENABLE
)
/

CREATE INDEX MBMONITOR.TELEGRAM_ALERT_CONFIG_TELEGRAM_CONFIG_ID_INDEX
    ON MBMONITOR.TELEGRAM_ALERT_CONFIG (TELEGRAM_CONFIG_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.TELEGRAM_ALERT_CONFIG_SERVICE_ID_INDEX
    ON MBMONITOR.TELEGRAM_ALERT_CONFIG (SERVICE_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.TELEGRAM_ALERT_CONFIG_APPLICATION_ID_INDEX
    ON MBMONITOR.TELEGRAM_ALERT_CONFIG (APPLICATION_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.TELEGRAM_CONFIG
(
    ID                    VARCHAR2(50) NOT NULL ENABLE PRIMARY KEY,
    BOT_TOKEN             VARCHAR2(3000 CHAR),
    DEFAULT_GROUP_CHAT_ID VARCHAR2(500),
    DESCRIPTION           VARCHAR2(3000 CHAR),
    "ACTIVE"              NUMBER(1, 0),
    "TYPE"                VARCHAR2(50),
    CREATED_DATE          TIMESTAMP(6),
    CREATED_BY            VARCHAR2(30),
    MODIFIED_DATE         TIMESTAMP(6),
    MODIFIED_BY           VARCHAR2(255),
    CHECK (ACTIVE IN (0, 1)) ENABLE
)
/
CREATE INDEX MBMONITOR.TELEGRAM_CONFIG_DEFAULT_GROUP_CHAT_ID_INDEX ON MBMONITOR.TELEGRAM_CONFIG (DEFAULT_GROUP_CHAT_ID) TABLESPACE INDEXS

DROP TABLE MBMONITOR.PRIORITY_RAW_VALUE_CONFIG;
DROP SEQUENCE MBMONITOR.RAW_VALUE_CONFIG_SEQ;

CREATE INDEX MBMONITOR.TELEGRAM_CONFIG_DEFAULT_GROUP_CHAT_ID_INDEX
    ON MBMONITOR.TELEGRAM_CONFIG (DEFAULT_GROUP_CHAT_ID) TABLESPACE INDEXS
/
ALTER TABLE MBMONITOR.MODIFY_ALERT_CONFIG_MODIFY
    MODIFY FIELD_VALUE VARCHAR2(2000 CHAR)
/


ALTER TABLE MBMONITOR.APPLICATION
    MODIFY NAME VARCHAR2(100 CHAR)
/
ALTER TABLE MBMONITOR.SERVICE
    MODIFY NAME VARCHAR2(100 CHAR)
/

ALTER TABLE MBMONITOR.ALERT_GROUP_CONFIG
    MODIFY NAME VARCHAR2(100 CHAR)
/

ALTER TABLE MBMONITOR.ALERT_PRIORITY_CONFIG
    MODIFY NAME VARCHAR2(100 CHAR)
/

ALTER TABLE MBMONITOR.CUSTOM_OBJECT
    MODIFY NAME VARCHAR2(100 CHAR)
/
ALTER TABLE MBMONITOR.DATABASE_COLLECT
    MODIFY NAME VARCHAR2(100 CHAR)
/
ALTER TABLE MBMONITOR.COLLECT_EMAIL_CONFIG
    MODIFY NAME VARCHAR2(100 CHAR)
/
ALTER TABLE MBMONITOR.EMAIL_PARTNER
    MODIFY NAME VARCHAR2(100 CHAR)
/
ALTER TABLE MBMONITOR.EMAIL_TEMPLATE
    MODIFY NAME VARCHAR2(100 CHAR)
/

-- index application, service
CREATE INDEX MBMONITOR.APPLICATION_NAME_INDEX ON MBMONITOR.APPLICATION (LOWER(NAME)) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.SERVICE_NAME_INDEX ON MBMONITOR.SERVICE (LOWER(NAME)) TABLESPACE INDEXS
/

-- MODIFY INDEX ALERT TABLE
DROP INDEX MBMONITOR.ALERT_ALERT_PRIORITY_CONFIG_ID_SERVICE_ID_APPLICATION_ID_ID_INDEX
/

CREATE INDEX MBMONITOR.ALERT_APPLICATION_ID_SERVICE_ID_ALERT_PRIORITY_CONFIG_ID_INDEX
    ON MBMONITOR.ALERT (APPLICATION_ID, SERVICE_ID, ALERT_PRIORITY_CONFIG_ID) TABLESPACE INDEXS
/

DROP INDEX MBMONITOR.ALERT_SERVICE_ID_APPLICATION_ID_ID_INDEX
/

CREATE INDEX MBMONITOR.ALERT_SERVICE_ID_APPLICATION_ID_INDEX
    ON MBMONITOR.ALERT (SERVICE_ID, APPLICATION_ID) TABLESPACE INDEXS
/

DROP INDEX MBMONITOR.ALERT_ALERT_PRIORITY_CONFIG_ID_APPLICATION_ID_ID_INDEX
/

CREATE INDEX MBMONITOR.ALERT_APPLICATION_ID_ALERT_PRIORITY_CONFIG_ID_INDEX
    ON MBMONITOR.ALERT (APPLICATION_ID, ALERT_PRIORITY_CONFIG_ID) TABLESPACE INDEXS
/

DROP INDEX MBMONITOR.ALERT_ALERT_PRIORITY_CONFIG_ID_SERVICE_ID_ID_INDEX
/

CREATE INDEX MBMONITOR.ALERT_SERVICE_ID_ALERT_PRIORITY_CONFIG_ID_INDEX
    ON MBMONITOR.ALERT (SERVICE_ID, ALERT_PRIORITY_CONFIG_ID) TABLESPACE INDEXS
/