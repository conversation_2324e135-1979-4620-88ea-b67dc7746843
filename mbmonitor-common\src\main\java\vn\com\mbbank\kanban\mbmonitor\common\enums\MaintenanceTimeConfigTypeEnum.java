package vn.com.mbbank.kanban.mbmonitor.common.enums;

import lombok.Getter;

/**
 * Enum representing the MaintenanceTimeConfigType.
 */
@Getter
public enum MaintenanceTimeConfigTypeEnum {
  NEXT_TIME("Next Time"),
  FROM_TIME_TO_TIME("From Time to Time"),
  CRON_JOB("Cron Job");

  private final String description;

  MaintenanceTimeConfigTypeEnum(String description) {
    this.description = description;
  }
}
