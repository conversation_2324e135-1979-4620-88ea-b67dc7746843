package vn.com.mbbank.kanban.mbmonitor.common.utils.export.factory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExportFileTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.export.CsvExporter;
import vn.com.mbbank.kanban.mbmonitor.common.utils.export.FileExporter;
import vn.com.mbbank.kanban.mbmonitor.common.utils.export.XlsxExporter;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 09/27/2024
 */
public class FileExporterFactory {
  /**
   * Returns the appropriate {@link FileExporter} implementation based on the provided file type.
   *
   * @param fileType an instance of {@link ExportFileTypeEnum} representing the type of file (CSV or XLSX).
   * @return an instance.
   */
  public static FileExporter getFileExporter(ExportFileTypeEnum fileType) throws BusinessException {
    switch (fileType) {
      case CSV:
        return new CsvExporter();
      case XLSX:
        return new XlsxExporter();
      default:
        throw new BusinessException(ErrorCode.FILE_EXPORT_IS_NOT_VALID);
    }
  }

  /**
   * Returns the name of file.
   *
   * @param componentName component Name (ALERT, APPLICATION, SERVICE).
   * @param fileName name of file.
   * @return an final name of file.
   */
  public static String generateFileExportName(String componentName, String fileName) {
    String trimmedFileName = (fileName != null) ? fileName.trim() : "";

    if (!trimmedFileName.isEmpty()) {
      return trimmedFileName;
    } else {
      String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtils.FORMAT_YYYY_MM_DD__HH_MM_SS));
      return componentName + "-" + timestamp;
    }
  }
}
