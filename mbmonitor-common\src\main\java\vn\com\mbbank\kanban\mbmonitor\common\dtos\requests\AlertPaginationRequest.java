package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;

import jakarta.validation.constraints.Max;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertCursor;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertDurationCompareOperator;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertDurationCompareUnit;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertStatusEnum;

/**
 * Model view attribute to filter in alert table tab report.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@FieldDefaults(level = AccessLevel.PRIVATE)
@EqualsAndHashCode(callSuper = true)
public class AlertPaginationRequest extends AlertCursor {
  List<String> serviceIds;
  List<String> applicationIds;
  String content;
  String recipient;
  List<Long> alertPriorityConfigIds;
  List<AlertStatusEnum> statuses;
  String fromDate;
  String toDate;
  Long alertGroupId;
  List<String> closedBy;
  Long closedDuration;
  AlertDurationCompareOperator closeDurationOperator;
  AlertDurationCompareUnit closeDurationUnit;
  @Builder.Default
  private String rangeDate = "All time";
  @Builder.Default
  @Max(value = CommonConstants.MAX_ALERT_EXPORT_ROWS_LIMIT, message = "Size cannot be greater than"
      + CommonConstants.MAX_ALERT_EXPORT_ROWS_LIMIT)
  int pageSize = 10;
}
