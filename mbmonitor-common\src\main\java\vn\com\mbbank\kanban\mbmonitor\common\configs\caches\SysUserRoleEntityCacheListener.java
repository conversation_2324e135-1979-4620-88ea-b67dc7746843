package vn.com.mbbank.kanban.mbmonitor.common.configs.caches;

import jakarta.persistence.PostRemove;
import jakarta.persistence.PostUpdate;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.core.mapper.KanbanMapperUtils;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SysUserResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysUserEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysUserRoleEntity;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonRedisService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.RedisUtils;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 1/8/2025
 */
@Component
public class SysUserRoleEntityCacheListener {
  @Autowired
  private CommonRedisService commonRedisService;

  /**
   * trigger when edit, remove data table SysUserRoleEntity.
   *
   * @param entity entity
   */
  @PostUpdate
  @PostRemove
  public void clearCache(SysUserRoleEntity entity) {
    var users = commonRedisService.getByPrefix(RedisUtils.MONITOR_USER_RESPONSE_PREFIX, SysUserResponse.class);
    var userEntities = KanbanMapperUtils.modelToEntity(users, SysUserEntity.class);
    var userEntity =
        userEntities.stream().filter(obj -> Objects.equals(entity.getUserId(), obj.getId())).findFirst()
            .orElse(null);
    //reset case
    if (!KanbanCommonUtil.isEmpty(userEntity)) {
      commonRedisService.delete(RedisUtils.getUserResponseKey(userEntity.getUserName()));
    }

  }
}
