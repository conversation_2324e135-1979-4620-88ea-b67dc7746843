package vn.com.mbbank.kanban.mbmonitor.common.mapper.log;

import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.EmailTemplateLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailTemplateEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface EmailTemplateLogModelMapper extends
    KanbanBaseMapper<EmailTemplateLogModel, EmailTemplateEntity> {
  EmailTemplateLogModelMapper INSTANCE = Mappers.getMapper(EmailTemplateLogModelMapper.class);

  /**
   * map EmailTemplateEntity to EmailTemplateLogModel.
   *
   * @param config AlertGroupConfigEntity.
   * @param to list to.
   * @param cc list cc
   * @return EmailTemplateLogModel
   */
  default EmailTemplateLogModel map(EmailTemplateEntity config, List<String> to, List<String> cc) {
    return EmailTemplateLogModel.builder()
        .name(config.getName())
        .description(config.getDescription())
        .subject(config.getSubject())
        .content(config.getContent())
        .to(to)
        .cc(cc)
        .build();
  }
}
