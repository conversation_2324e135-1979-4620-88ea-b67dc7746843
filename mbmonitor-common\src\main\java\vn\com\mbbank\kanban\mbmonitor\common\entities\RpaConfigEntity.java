package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.KanbanAutoGenerateUlId;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 29/05/2025
 */
@KanbanAutoGenerateUlId
@Getter
@Setter
@Entity
@Table(name = TableName.RPA_CONFIG)
public class RpaConfigEntity extends BaseEntity<String> {
  @Id
  @Column(name = "ID")
  private String id;

  @Column(name = "INTERVAL")
  private Integer interval;

  @Column(name = "NUMBER_OF_RETRY")
  private Integer numberOfRetry;

  @Column(name = "ACTIVE")
  private Boolean active;

  @Override
  public String getId() {
    return id;
  }
}
