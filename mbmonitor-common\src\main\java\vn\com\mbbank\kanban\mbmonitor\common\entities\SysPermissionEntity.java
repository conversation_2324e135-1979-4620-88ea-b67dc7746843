package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.configs.caches.SysPermissionEntityCacheListener;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.converter.PermissionActionConverter;
import vn.com.mbbank.kanban.mbmonitor.common.converter.PermissionModuleConverter;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 9/23/2024
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = TableName.SYS_ROLE_PERMISSION)
@EntityListeners(SysPermissionEntityCacheListener.class)
public class SysPermissionEntity extends BaseEntity<Long> {
  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "SYS_PERMISSION_SEQ", sequenceName = "SYS_PERMISSION_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SYS_PERMISSION_SEQ")
  private Long id;

  @Column(name = "MODULE_NAME")
  @Convert(converter = PermissionModuleConverter.class)
  private PermissionModuleEnum module;

  @Column(name = "ACTION")
  @Convert(converter = PermissionActionConverter.class)
  private PermissionActionEnum action;

  @Column(name = "ROLE_ID")
  private Long roleId;

  @Column(name = "TYPE")
  @Enumerated(EnumType.STRING)
  private PermissionTypeEnum type;

  @Column(name = "MODULE_ID")
  private String moduleId;

  @Column(name = "MODULE_PARENT_ID")
  private String moduleParentId;
}
