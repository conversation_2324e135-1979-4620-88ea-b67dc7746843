package vn.com.mbbank.kanban.mbmonitor.common.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/23/2025
 */
public class TeamsUtils {
  /**
   * extract email error from response send message.
   *
   * @param errorMessage errorMessage
   * @return List email
   */
  public static List<String> extractEmailsCreateGroupInvalid(String errorMessage) {
    List<String> emails = new ArrayList<>();

    Pattern pattern = Pattern.compile("'(.*?)'");
    Matcher matcher = pattern.matcher(errorMessage);

    if (matcher.find()) {
      String emailsString = matcher.group(1);
      String[] emailArray = emailsString.split(",");

      for (String email : emailArray) {
        emails.add(email.trim());
      }
    }
    return emails;
  }
}
