package vn.com.mbbank.kanban.mbmonitor.common.mapper;

import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.FilterAlertConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FilterAlertConfigEntity;

/**
 * Mapper interface for mapping between `FilterAlertConfigResponse` and `FilterAlertConfigEntity`.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FilterAlertConfigResponseMapper extends
    KanbanBaseMapper<FilterAlertConfigResponse, FilterAlertConfigEntity> {
  FilterAlertConfigResponseMapper INSTANCE = Mappers.getMapper(FilterAlertConfigResponseMapper.class);


  /**
   * map from FilterAlertConfigEntity to FilterAlertConfigResponse.
   *
   * @param entity         FilterAlertConfigEntity.
   * @param serviceIds     list of service id
   * @param applicationIds list of application id
   * @return FilterAlertConfigResponse
   */
  default FilterAlertConfigResponse map(FilterAlertConfigEntity entity,
                                        List<String> serviceIds,
                                        List<String> applicationIds) {

    var filterAlertConfigResponse = this.map(entity);
    filterAlertConfigResponse.setServiceIds(serviceIds);
    filterAlertConfigResponse.setApplicationIds(applicationIds);
    return filterAlertConfigResponse;
  }
}
