package vn.com.mbbank.kanban.mbmonitor.common.services.impl;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.DefaultResponseErrorHandler;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.configs.RestTemplateConfig;
import vn.com.mbbank.kanban.mbmonitor.common.services.RestApiService;

@Service
@RequiredArgsConstructor
@Scope("prototype")
public class RestApiServiceImpl implements RestApiService {
  @Qualifier(RestTemplateConfig.REST_TEMPLATE_SKIP_SSL)
  private final RestTemplate defaultRestTemplate;

  private RestTemplate customTemplate;


  @Override
  public void setCustomRestTemplate(RestTemplate restTemplate) {
    this.customTemplate = restTemplate;
  }

  public RestTemplate getRestTemplate() {
    return this.customTemplate != null ? customTemplate : defaultRestTemplate;
  }


  @Override
  public void setRequestFactory(SimpleClientHttpRequestFactory factory) {
    if (this.customTemplate != null) {
      this.customTemplate.setRequestFactory(factory);
    } else {
      this.defaultRestTemplate.setRequestFactory(factory);
    }
  }

  @Override
  public void setRequestFactory(HttpComponentsClientHttpRequestFactory factory) {
    if (this.customTemplate != null) {
      this.customTemplate.setRequestFactory(factory);
    } else {
      this.defaultRestTemplate.setRequestFactory(factory);
    }
  }

  @Override
  public void setIsErrorHandler(Boolean isErrorHandler) {
    if (this.customTemplate != null) {
      this.customTemplate.setErrorHandler(new DefaultResponseErrorHandler() {
        @Override
        public boolean hasError(org.springframework.http.client.ClientHttpResponse response) {
          return isErrorHandler;
        }
      });
    } else {
      this.defaultRestTemplate.setErrorHandler(new DefaultResponseErrorHandler() {
        @Override
        public boolean hasError(org.springframework.http.client.ClientHttpResponse response) {
          return isErrorHandler;
        }
      });
    }

  }

  // ========= GET =========
  @Override
  public <T> ResponseEntity<T> get(String url, Class<T> responseType) {
    return getRestTemplate().exchange(url, HttpMethod.GET, null, responseType);
  }

  @Override
  public <T> ResponseEntity<T> get(String url, Map<String, String> params, Class<T> responseType) {
    UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
    if (!KanbanCommonUtil.isEmpty(params)) {
      params.forEach(builder::queryParam);
    }
    return this.getRestTemplate()
        .exchange(builder.toUriString(), HttpMethod.GET, null, responseType);
  }

  @Override
  public <T> ResponseEntity<T> get(String url, HttpHeaders headers, Class<T> responseType) {
    HttpEntity<String> entity = new HttpEntity<>(headers);
    return this.getRestTemplate().exchange(url, HttpMethod.GET, entity, responseType);
  }

  @Override
  public <T> ResponseEntity<T> get(String url, Map<String, String> params, HttpHeaders headers,
                                   Class<T> responseType) {
    UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
    if (!KanbanCommonUtil.isEmpty(params)) {
      params.forEach(builder::queryParam);
    }
    HttpEntity<String> entity = new HttpEntity<>(headers);
    return this.getRestTemplate().exchange(builder.toUriString(), HttpMethod.GET, entity,
        responseType);
  }

  // ========= POST =========
  @Override
  public <T> ResponseEntity<T> post(String url, Object body, Class<T> responseType) {
    HttpEntity<Object> entity = new HttpEntity<>(body);
    return this.getRestTemplate().exchange(url, HttpMethod.POST, entity, responseType);
  }

  @Override
  public <T> ResponseEntity<T> post(String url, Object body, HttpHeaders headers,
                                    Class<T> responseType) {
    HttpEntity<Object> entity = new HttpEntity<>(body, headers);
    return this.getRestTemplate().exchange(url, HttpMethod.POST, entity, responseType);
  }

  // ========= PUT =========
  @Override
  public <T> ResponseEntity<T> put(String url, Object body, Class<T> responseType) {
    HttpEntity<Object> entity = new HttpEntity<>(body);
    return this.getRestTemplate().exchange(url, HttpMethod.PUT, entity, responseType);
  }

  @Override
  public <T> ResponseEntity<T> put(String url, Object body, HttpHeaders headers,
                                   Class<T> responseType) {
    HttpEntity<Object> entity = new HttpEntity<>(body, headers);
    return this.getRestTemplate().exchange(url, HttpMethod.PUT, entity, responseType);
  }

  // ========= DELETE =========
  @Override
  public <T> ResponseEntity<T> delete(String url, Class<T> responseType) {
    return this.getRestTemplate().exchange(url, HttpMethod.DELETE, null, responseType);
  }

  @Override
  public <T> ResponseEntity<T> delete(String url, Map<String, String> params,
                                      Class<T> responseType) {
    UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
    if (!KanbanCommonUtil.isEmpty(params)) {
      params.forEach(builder::queryParam);
    }
    return this.getRestTemplate().exchange(builder.toUriString(), HttpMethod.DELETE, null,
        responseType);
  }

  @Override
  public <T> ResponseEntity<T> delete(String url, HttpHeaders headers, Class<T> responseType) {
    HttpEntity<String> entity = new HttpEntity<>(headers);
    return this.getRestTemplate().exchange(url, HttpMethod.DELETE, entity, responseType);
  }

  @Override
  public <T> ResponseEntity<T> delete(String url, Map<String, String> params, HttpHeaders headers,
                                      Class<T> responseType) {
    UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
    if (!KanbanCommonUtil.isEmpty(params)) {
      params.forEach(builder::queryParam);
    }
    HttpEntity<String> entity = new HttpEntity<>(headers);
    return this.getRestTemplate().exchange(builder.toUriString(), HttpMethod.DELETE, entity,
        responseType);
  }

}
