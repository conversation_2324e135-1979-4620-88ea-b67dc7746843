package vn.com.mbbank.kanban.mbmonitor.common.services.kafka.impl;


import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.KafkaJobModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.CommonKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.JobKafkaProducerService;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class JobKafkaProducerServiceImpl implements JobKafkaProducerService {
  final CommonKafkaProducerService commonKafkaProducerService;
  @Value("${kanban.kafka.topic.jobs}")
  private String topic;

  @Override
  public void notifyJobUpdate(KafkaTypeEnum type, KafkaJobModel data) throws BusinessException {
    var dataKafka = new BaseKafkaModel<KafkaJobModel>();
    dataKafka.setType(type);
    dataKafka.setValue(data);
    commonKafkaProducerService.sendAndWait(topic, dataKafka);
  }
}


