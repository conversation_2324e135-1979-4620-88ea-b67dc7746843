package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 06/12/2025
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlertRequestDatabaseRequest {
  String alertRequestId;
  @NotNull
  @NotBlank
  @Size(min = 1, max = CommonConstants.COMMON_NAME_MAX_LENGTH)
  String name;
  @NotNull
  Long databaseConnectionId;
  @NotNull
  String sqlCommand;
}