package vn.com.mbbank.kanban.mbmonitor.common.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.QuerySqlResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.QuerySqlEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 17/12/2024
 */

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface QuerySqlResponseMapper extends KanbanBaseMapper<QuerySqlResponse, QuerySqlEntity> {
  QuerySqlResponseMapper INSTANCE = Mappers.getMapper(QuerySqlResponseMapper.class);
}


