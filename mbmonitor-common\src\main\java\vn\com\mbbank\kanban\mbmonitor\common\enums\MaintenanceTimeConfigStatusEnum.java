package vn.com.mbbank.kanban.mbmonitor.common.enums;

import lombok.Getter;

/**
 * Enum representing the MaintenanceTimeConfigStatusEnum.
 */
@Getter
public enum MaintenanceTimeConfigStatusEnum {
  NEW("New"),
  IN_PROGRESS("In Progress"),
  INACTIVE("Inactive"),
  EXPIRED("Expired"),
  NEVER_EXPIRE("Never Expire");

  private final String description;

  MaintenanceTimeConfigStatusEnum(String description) {
    this.description = description;
  }
}
