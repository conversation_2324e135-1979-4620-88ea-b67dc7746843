package vn.com.mbbank.kanban.mbmonitor.common.utils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import org.apache.logging.log4j.util.Strings;
import vn.com.mbbank.kanban.core.utils.KanbanDateUtils;
import vn.com.mbbank.kanban.core.utils.KanbanStringUtils;

/**
 * Utility class for Date operations.
 */
public class DateUtils extends KanbanDateUtils {
  public static final String FORMAT_YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
  public static final String FORMAT_YYYY_MM_DD_T_HH_MM_SS = "yyyy-MM-dd'T'HH:mm:ss";
  public static final String FORMAT_YYYY_MM_DD_HH_MM_A = "dd/MM/yyyy hh:mm a";
  public static String FORMAT_YYYY_MM_DD = "yyyy-MM-dd";
  public static String FORMAT_YYYY_MM_DD__HH_MM_SS = "yyyy_MM_dd-HH_mm_ss";


  /**
   * Converts a date-time string into a Date object.
   *
   * @param dateString The date-time string to be converted
   * @return The Date object, or null if the string is not in the correct format
   */
  public static Date convertStringToDate(String dateString) {
    return convertStringToDate(dateString, FORMAT_YYYY_MM_DD_HH_MM_SS);
  }

  /**
   * Converts a date string from one format to another format.
   *
   * @param date    the date string to convert. Must not be {@code null} or empty.
   * @param pattern the target pattern for formatting the output date string. Must not be {@code null}.
   * @return a string representation of the date in the target format.
   */
  public static String convertDefaultStringDateToFormatedString(String date, String pattern) {
    if (KanbanStringUtils.isNullOrEmpty(date) || KanbanStringUtils.isNullOrEmpty(pattern)) {
      return Strings.EMPTY;
    }
    try {
      LocalDateTime localDateTime = LocalDateTime.parse(date, DateUtils.formatterDateWithMilliseconds());
      return localDateTime.format(DateTimeFormatter.ofPattern(pattern));
    } catch (DateTimeParseException e) {
      return Strings.EMPTY;
    }
  }

  /**
   * Creates a {@link DateTimeFormatter} that can parse date and time strings with optional
   * fractional seconds (milliseconds) ranging from 1 to 6 digits.
   *
   * @return a {@link DateTimeFormatter} configured to parse date-time strings with optional fractional seconds.
   */
  public static DateTimeFormatter formatterDateWithMilliseconds() {
    return new DateTimeFormatterBuilder()
        .appendPattern(FORMAT_YYYY_MM_DD_HH_MM_SS)
        .optionalStart()
        .appendLiteral(".")
        .appendFraction(
            java.time.temporal.ChronoField.NANO_OF_SECOND,
            1, 6, false)
        .optionalEnd()
        .toFormatter();
  }

  /**
   * Utility method to truncate a {@link Date} object to the nearest minute.
   * This removes the seconds and milliseconds, keeping only year, month, day, hour, and minute.
   *
   * @param time the {@link Date} object to be truncated; if {@code null}, the method returns {@code null}.
   * @return a new {@link Date} object truncated to minutes, or {@code null} if input is {@code null}.
   */
  public static Date truncateToMinutes(Date time) {
    return time != null ? Date.from(
        time.toInstant()
            .atZone(ZoneId.systemDefault())
            .truncatedTo(ChronoUnit.MINUTES)
            .toInstant()) : null;
  }

  /**
   * format duration to string 100s -> 1m 40s.
   *
   * @param time duration.
   * @param unit {@link ChronoUnit} unit
   * @return a format of duration.
   */
  public static String formatDuration(long time, ChronoUnit unit) {
    Duration duration = Duration.of(time, unit);
    long days = duration.toDays();
    long hours = duration.minusDays(days).toHours();
    long minutes = duration.minusDays(days).minusHours(hours).toMinutes();
    long seconds = duration.minusDays(days).minusHours(hours).minusMinutes(minutes).getSeconds();
    var result = new StringBuilder();
    if (days > 0) {
      result.append(days).append("d ");
    }
    if (hours > 0) {
      result.append(hours).append("h ");
    }
    if (minutes > 0) {
      result.append(minutes).append("m ");
    }
    if (seconds > 0) {
      result.append(seconds).append("s ");
    }
    return result.toString();
  }

}
