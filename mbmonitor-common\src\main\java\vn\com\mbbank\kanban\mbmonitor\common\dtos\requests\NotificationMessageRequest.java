package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationTypeEnum;

/**
 * NotificationRequest.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class NotificationMessageRequest {
  @Builder.Default
  List<String> ids = new ArrayList<>();
  @Size(min = 1, max = CommonConstants.NOTIFICATION_TITLE_MAX_LENGTH)
  @NotNull
  @NotBlank
  String title;
  @Size(min = 1, max = CommonConstants.NOTIFICATION_CONTENT_MAX_LENGTH)
  @NotNull
  @NotBlank
  String content;
  @NotNull
  NotificationTypeEnum type;
  @NotNull
  String sourceId;
  @NotNull
  NotificationSourceTypeEnum sourceType;
  @Builder.Default
  List<String> userNames = new ArrayList<>();
}
