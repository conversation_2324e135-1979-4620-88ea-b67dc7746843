package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;


@Data
@Entity
@Table(name = "PARAM_QUERY_SQL")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ParamQuerySqlEntity extends BaseEntity<Long> {

  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "GROUP_QUERY_SQL_SEQ", sequenceName = "GROUP_QUERY_SQL_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "GROUP_QUERY_SQL_SEQ")
  private Long id;

  @Column(name = "QUERY_SQL_ID", nullable = false)
  private Long querySqlId;

  @Column(name = "VALUE")
  private String value;

  @Override
  public Long getId() {
    return id;
  }
}