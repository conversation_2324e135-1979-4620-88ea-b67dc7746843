package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeConfigTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AutoTriggerActionConfigLogModel {
  private String name;
  private String description;
  private ConfigDependencyLogModel dependency;
  private String condition;
  private List<String> executions;
  private MaintenanceTimeConfigTypeEnum type;
  private Long timeSinceLastTrigger;
  private Boolean active;
}
