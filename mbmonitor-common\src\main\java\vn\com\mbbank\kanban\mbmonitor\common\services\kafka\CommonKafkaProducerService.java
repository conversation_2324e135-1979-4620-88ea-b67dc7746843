package vn.com.mbbank.kanban.mbmonitor.common.services.kafka;

import java.util.concurrent.CompletableFuture;
import org.springframework.kafka.support.SendResult;
import org.springframework.scheduling.annotation.Async;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.constants.BeanNameConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/19/2025
 */
public interface CommonKafkaProducerService {
  /**
   * send message kafka.
   *
   * @param data data payload.
   * @param <T>  generic
   */

  @Async(BeanNameConstants.COMMON_TASK_EXECUTOR)
  <T> void send(BaseKafkaModel<T> data);

  /**
   * send message kafka.
   *
   * @param topic topic
   * @param key   key
   * @param data  data
   * @param <T>   generic
   * @return CompletableFuture
   */
  @Async(BeanNameConstants.COMMON_TASK_EXECUTOR)
  <T> CompletableFuture<SendResult<String, String>> sendAsync(String topic, String key, BaseKafkaModel<T> data);

  /**
   * send message kafka.
   *
   * @param topic topic
   * @param data  data
   * @param <T>   generic
   */
  <T> void send(String topic, BaseKafkaModel<T> data);

  /**
   * send message kafka.
   *
   * @param topic topic
   * @param key   key
   * @param data  data
   * @param <T>   generic
   */
  <T> void send(String topic, String key, BaseKafkaModel<T> data);

  /**
   * Sends a Kafka message to a specific topic with a key synchronously,
   * waiting for the send operation to complete successfully.
   *
   * @param data  the data payload.
   * @param <T>   generic type.
   * @throws BusinessException if sending the message fails.
   */
  <T> void sendAndWait(BaseKafkaModel<T> data) throws BusinessException;

  /**
   * Send message to a specific topic synchronously.
   *
   * @param topic topic name.
   * @param data  data payload.
   * @param <T>   generic type.
   * @throws BusinessException if sending fails.
   */
  <T> void sendAndWait(String topic, BaseKafkaModel<T> data) throws BusinessException;

  /**
   * Send message with a key synchronously.
   *
   * @param topic topic name.
   * @param key   message key.
   * @param data  data payload.
   * @param <T>   generic type.
   * @throws BusinessException if sending fails.
   */
  <T> void sendAndWait(String topic, String key, BaseKafkaModel<T> data) throws BusinessException;
}
