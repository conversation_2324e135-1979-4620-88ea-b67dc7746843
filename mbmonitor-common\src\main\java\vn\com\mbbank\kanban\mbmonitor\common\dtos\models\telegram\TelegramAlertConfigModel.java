package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.telegram;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.Data;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TelegramAlertConfigTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/7/2025
 */
@Data
public class TelegramAlertConfigModel {

  private String serviceName;

  private String applicationName;

  private String telegramConfigId;

  private String serviceId;

  private String applicationId;

  @Enumerated(EnumType.STRING)
  private TelegramAlertConfigTypeEnum type;

  private String groupChatId;

  @JsonProperty("isActive")
  private Boolean active;


}
