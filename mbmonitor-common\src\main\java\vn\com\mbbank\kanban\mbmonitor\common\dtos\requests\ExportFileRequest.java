package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AttributeInfoDto;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportDataModel;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/04/2025
 */
@Getter
@Setter
public class ExportFileRequest {
  @NotNull(message = "List attribute info cannot be empty.!")
  private List<AttributeInfoDto> attributes;
  private List<String> title;
  @Valid
  private ExportDataModel exportDataModel;
  @Max(value = CommonConstants.MAX_SIZE_FILE_RECORD_EXPORT)
  private int numberOfResults;
}