package vn.com.mbbank.kanban.mbmonitor.common.utils;

import java.util.Locale;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanStringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.constants.DatabaseCollectConstants;
import vn.com.mbbank.kanban.mbmonitor.common.enums.DatabaseConnectionTypeEnum;

/**
 * Utility class for building SQL queries with limit, paging and count,
 * supporting both Oracle and MS SQL dialects.
 *
 * <AUTHOR> and baonh2
 * @created_date 11/14/2024
 * @updated_date 05/28/2025
 */
public class DatabaseCollectUtils {

  private static final String DATABASE_COLLECT_PREFIX = "DATABASE_COLLECT_";

  /**
   * check query sql with param createDate.
   *
   * @param sql    sql
   * @param column column
   * @param param  param
   * @return true/false
   */
  public static boolean containsWhereClause(String sql, String column, String param) {
    String regex = "(?i)\\bwhere\\b.*\\b" + column.trim() + "\\b\\s*>=\\s*"
        +
        DatabaseCollectConstants.CREATE_DATE_PARAMETER + "\\b";
    return !KanbanCommonUtil.isEmpty(column) && sql != null && sql.matches("(?s).*" + regex + ".*");
  }


  /**
   * Check select query.
   *
   * @param sql sql
   * @return true/false
   */
  public static boolean isSelectQuery(String sql) {
    if (KanbanStringUtils.isNullOrEmpty(sql)) {
      return false;
    }
    boolean isSelect = sql.toUpperCase(Locale.ROOT).contains("SELECT ")
        && sql.toUpperCase(Locale.ROOT).indexOf("SELECT ") < 8;
    // Tempary query from beginning of query
    if (sql.toUpperCase(Locale.ROOT).trim().startsWith("WITH ")) {
      isSelect = true;
    }
    return isSelect;
  }

  /**
   * Get job key.
   *
   * @param id id
   * @return job key
   */
  public static String getJobKey(Long id) {
    return DATABASE_COLLECT_PREFIX + id;
  }

  /**
   * Get id collect from job key.
   *
   * @param jobKey jobKey
   * @return Long id
   */
  public static Long getId(String jobKey) {
    return Long.valueOf(jobKey.replaceAll(DATABASE_COLLECT_PREFIX, ""));
  }

  /**
   * Adds a LIMIT clause to the SQL depending on the database type.
   * For Oracle uses ROWNUM; for MS SQL uses TOP.
   *
   * @param sql      sql
   * @param rowLimit rowLimit
   * @param dbType type of db (oracle, ms sql)
   * @return new query
   */
  public static String getQueryWithLimit(String sql, int rowLimit, DatabaseConnectionTypeEnum dbType) {
    return switch (dbType) {
      case ORACLE -> String.format(
          "WITH GROUPDATA AS ( %s ) SELECT * FROM GROUPDATA WHERE ROWNUM <= %d", sql, rowLimit
      );
      case Microsoft_SQL -> String.format(
          "SELECT TOP %d * FROM ( %s ) AS GROUPDATA", rowLimit, sql
      );
    };
  }

  /**
   * Constructs a paginated SQL query.
   *
   * @param sql               The original SQL query.
   * @param paginationRequest The pagination request containing page and size.
   * @return The paginated query.
   */
  public static String getQueryWithPaging(String sql, PaginationRequestDTO paginationRequest) {
    if (KanbanCommonUtil.isEmpty(paginationRequest)) {
      return sql.trim();
    }

    int offset = paginationRequest.getPage() * paginationRequest.getSize();
    int limit = paginationRequest.getSize();

    return String.format(
        "SELECT * FROM (%s) temp_query ORDER BY 1 OFFSET %d ROWS FETCH NEXT %d ROWS ONLY",
        sql.trim(),
        offset,
        limit
    );
  }

  /**
   * Constructs a SQL query to count the total number of records.
   *
   * @param sql The original SQL query.
   * @return The query to count total records.
   */
  public static String getQueryWithCount(String sql) {
    return String.format(
        "SELECT COUNT(1) AS total FROM (%s) original_query",
        sql.trim()
    );
  }

}
