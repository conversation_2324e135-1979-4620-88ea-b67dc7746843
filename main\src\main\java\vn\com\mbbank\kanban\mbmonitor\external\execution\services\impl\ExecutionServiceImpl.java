package vn.com.mbbank.kanban.mbmonitor.external.execution.services.impl;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import jakarta.annotation.Nullable;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Select;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanEncryptorUtils;
import vn.com.mbbank.kanban.mbmonitor.common.configs.QueryHikariDataSourceConfig;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecuteScriptParamModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.DatabaseConnectionRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecutionScriptRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SqlExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionScriptResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionHistoryEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.DatabaseConnectionRequestToEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.DatabaseQueryService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.DatabaseCollectUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.SqlUtils;
import vn.com.mbbank.kanban.mbmonitor.external.execution.mapper.ExecutionHistoryEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.external.execution.repositories.ExecutionRepository;
import vn.com.mbbank.kanban.mbmonitor.external.execution.services.DatabaseConnectionService;
import vn.com.mbbank.kanban.mbmonitor.external.execution.services.ExecutionHistoryService;
import vn.com.mbbank.kanban.mbmonitor.external.execution.services.ExecutionService;

@Service
@RequiredArgsConstructor
public class ExecutionServiceImpl implements ExecutionService {
  private static final int BUFFER_SIZE = 8192;
  private final Logger logger = LoggerFactory.getLogger(this.getClass());
  private final ExecutionHistoryService executionHistoryService;
  private final ExecutionRepository executionRepository;
  private final DatabaseConnectionService databaseConnectionService;
  private final QueryHikariDataSourceConfig queryHikariDataSourceConfig;
  private final DatabaseQueryService databaseQueryService;
  private final ExecutionHistoryEntityMapper executionHistoryEntityMapper = ExecutionHistoryEntityMapper.INSTANCE;
  @Value("${mbmonitor.execution.maxOutputSize:524288}")
  private int maxOutputSize;
  @Value("${mbmonitor.execution.timeout:5}")
  private int executeTimeOut;

  @Override
  public ExecutionScriptResponse execute(ExecutionScriptRequest request) throws BusinessException {
    validateRequest(request);
    return runExecution(request);
  }

  protected void validateRequest(ExecutionScriptRequest request) throws BusinessException {
    var execution = executionRepository.findById(request.getExecutionId())
        .orElseThrow(() -> new BusinessException(ErrorCode.EXECUTION_NOT_FOUND));
    if (ExecutionTypeEnum.SQL.equals(execution.getType())) {
      var databaseConnection = databaseConnectionService.findById(execution.getDatabaseConnectionId());
      if (Objects.isNull(databaseConnection)) {
        throw new BusinessException(ErrorCode.DATABASE_CONNECTION_NOT_FOUND);
      }
      if (!databaseConnection.getIsActive()) {
        throw new BusinessException(ErrorCode.DATABASE_COLLECT_CONNECTION_INACTIVE);
      }
    }
    var isInvalidParams = request.getParams().stream()
        .anyMatch(param -> KanbanCommonUtil.isEmpty(param.getName()) || KanbanCommonUtil.isEmpty(param.getValue()));
    if (isInvalidParams) {
      throw new BusinessException(ErrorCode.EXECUTION_PARAM_IS_INVALID);
    }
  }


  protected ExecutionScriptResponse runExecution(ExecutionScriptRequest request) throws BusinessException {
    var execution = executionRepository.getReferenceById(request.getExecutionId());
    logger.info("Starting run execution : {} executionBy : {} ", request.getExecutionId(), request.getExecutionBy());
    // save start execution history
    var startHistory = canStoreHistory(execution)
        ? executionHistoryService.save(executionHistoryEntityMapper.map(request, execution))
        : null;

    var response = switch (execution.getType()) {
      case API -> callApi(request);
      case SQL -> runSqlScript(request);
      case PYTHON -> runPythonScript(request);
    };
    saveCompleteExecutionHistory(startHistory, request, response);
    logger.info("Completed run execution : {} executionBy : {} ", request.getExecutionId(), request.getExecutionBy());
    return response;
  }

  protected boolean canStoreHistory(ExecutionEntity execution) {
    return ExecutionTypeEnum.PYTHON.equals(execution.getType());
  }

  protected void saveCompleteExecutionHistory(@Nullable ExecutionHistoryEntity executionHistory,
                                              ExecutionScriptRequest request,
                                              ExecutionScriptResponse response) {
    if (Objects.nonNull(executionHistory)) {
      var execution = executionRepository.getReferenceById(request.getExecutionId());
      if (canStoreHistory(execution)) {
        executionHistory.setResult(response.getResult());
        executionHistory.setEndTime(new Date());
        executionHistory.setStatus(response.getStatus());
        executionHistory.setError(response.getError());
        executionHistory.setExecutionBy(request.getExecutionBy());
        executionHistoryService.save(executionHistory);
      }
    }
  }

  private ExecutionScriptResponse runPythonScript(ExecutionScriptRequest request) {
    var execution = executionRepository.getReferenceById(request.getExecutionId());
    var res = new ExecutionScriptResponse();
    File pythonFile = null;
    var script = execution.getScript();
    var params = request.getParams();

    try {
      // Create temp python file
      pythonFile = File.createTempFile("python_script_", ".py");

      // Write content to file
      try (FileWriter writer = new FileWriter(pythonFile)) {
        writer.write(script);
      }

      // Create ProcessBuilder to run Python
      ProcessBuilder processBuilder = new ProcessBuilder("python", "-X", "utf8", pythonFile.getAbsolutePath());

      // Add ENV
      if (!params.isEmpty()) {
        Map<String, String> env = processBuilder.environment();
        env.putAll(
            params.stream()
                .collect(Collectors.toMap(ExecuteScriptParamModel::getName, ExecuteScriptParamModel::getValue)));
      }

      // Run process
      Process process = processBuilder.start();

      // Use CompletableFuture for async output reading with limits
      CompletableFuture<String> outputFuture = CompletableFuture.supplyAsync(() ->
          readStreamWithLimit(process.getInputStream(), "stdout"));

      CompletableFuture<String> errorFuture = CompletableFuture.supplyAsync(() ->
          readStreamWithLimit(process.getErrorStream(), "stderr"));

      // Wait for process with timeout
      boolean exited = process.waitFor(executeTimeOut, TimeUnit.MINUTES);

      if (!exited) {
        process.destroyForcibly();
        res.setStatus(ExecutionStatusEnum.FAILED);
      } else {
        int exitCode = process.exitValue();
        if (exitCode == 0) {
          res.setStatus(ExecutionStatusEnum.COMPLETED);
        } else {
          res.setStatus(ExecutionStatusEnum.FAILED);
        }
      }

      // Get outputs (with timeout to avoid hanging)
      try {
        String output = outputFuture.get(executeTimeOut, TimeUnit.MINUTES);
        String error =
            errorFuture.get(executeTimeOut, TimeUnit.MINUTES)
                + (exited ? "" : "\nPython script execution timed out");
        res.setResult(output);
        res.setError(error);
      } catch (TimeoutException e) {
        outputFuture.cancel(true);
        errorFuture.cancel(true);
        res.setError(res.getError() + "\nOutput reading timed out - output may be truncated");
      }
    } catch (Exception e) {
      logger.error(e.getMessage(), e);
      res.setStatus(ExecutionStatusEnum.FAILED);
      res.setError("Error executing Python script: " + e.getMessage());
    } finally {
      if (Objects.nonNull(pythonFile)) {
        try {
          pythonFile.delete();
        } catch (Exception ex) {
          logger.error("Can not delete temp execution file ", ex);
        }
      }
    }
    return res;
  }

  private ExecutionScriptResponse callApi(ExecutionScriptRequest request) {
    // TODO: Call API
    return new ExecutionScriptResponse();
  }


  // Helper method to read stream with size limit
  private String readStreamWithLimit(InputStream inputStream, String streamName) {
    StringBuilder output = new StringBuilder();
    byte[] buffer = new byte[BUFFER_SIZE];
    int totalBytes = 0;
    boolean truncated = false;

    try (BufferedInputStream bis = new BufferedInputStream(inputStream)) {
      int bytesRead;
      while ((bytesRead = bis.read(buffer)) != -1) {
        if (totalBytes + bytesRead > maxOutputSize) {
          // Calculate how many bytes we can still read
          int remainingBytes = maxOutputSize - totalBytes;
          if (remainingBytes > 0) {
            output.append(new String(buffer, 0, remainingBytes, StandardCharsets.UTF_8));
          }
          truncated = true;
          break;
        }
        output.append(new String(buffer, 0, bytesRead, StandardCharsets.UTF_8));
        totalBytes += bytesRead;
      }
    } catch (IOException e) {
      logger.error("Error reading " + streamName, e);
      output.append("\nError reading ").append(streamName).append(": ").append(e.getMessage());
    }

    if (truncated) {
      output.append("\n[OUTPUT TRUNCATED - Exceeded ").append(maxOutputSize).append(" bytes limit]");
    }

    return output.toString();
  }

  protected ExecutionScriptResponse runSqlScript(ExecutionScriptRequest request) {
    var execution = executionRepository.getReferenceById(request.getExecutionId());
    var databaseConnection = databaseConnectionService.findById(execution.getDatabaseConnectionId());
    DatabaseConnectionRequest databaseConnectionRequest =
        DatabaseConnectionRequestToEntityMapper.INSTANCE.map(databaseConnection);
    databaseConnectionRequest.setPassword(KanbanEncryptorUtils.decrypt(databaseConnectionRequest.getPassword()));
    HikariConfig dbConfig = SqlUtils.createHikariConfig(databaseConnectionRequest);
    var response = new ExecutionScriptResponse();
    SqlExecutionResponse sqlResponse = null;
    try (HikariDataSource dataSource = queryHikariDataSourceConfig.getDataSource(dbConfig.getJdbcUrl())) {
      var script = execution.getScript();
      Map<String, Object> paramMap =
          request.getParams().stream()
              .collect(Collectors.toMap(ExecuteScriptParamModel::getName, ExecuteScriptParamModel::getValue));
      sqlResponse = databaseQueryService.executeQuery(dataSource.getConnection(),
          DatabaseCollectUtils.getQueryWithPaging(script,
              isSelectQuery(script) ? request.getPaginationRequest() : null), true,
          null, paramMap);
      if (!sqlResponse.isNonQuery()) {
        var totalResponse =
            databaseQueryService.executeQuery(dataSource.getConnection(),
                DatabaseCollectUtils.getQueryWithCount(script),
                true, null, paramMap);
        sqlResponse.setTotal(Integer.valueOf(
            totalResponse.getListDataMappings().get(0).getListSqlMappingColumnDatas().get(0).getValue()));
      }
      response.setSqlExecutionResponse(sqlResponse);
      response.setStatus(ExecutionStatusEnum.COMPLETED);
    } catch (Exception ex) {
      logger.error(ex.getMessage(), ex);
      response.setStatus(ExecutionStatusEnum.FAILED);
      response.setError(ex.getMessage());
    }
    return response;
  }

  protected boolean isSelectQuery(String sql) {
    try {
      Statement statement = CCJSqlParserUtil.parse(sql);
      return statement instanceof Select;
    } catch (JSQLParserException e) {
      logger.error(e.getMessage(), e);
      return false;
    }
  }
}