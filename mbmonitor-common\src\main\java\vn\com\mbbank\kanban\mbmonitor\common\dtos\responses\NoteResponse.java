package vn.com.mbbank.kanban.mbmonitor.common.dtos.responses;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils;

/**
 * note response.
 */
@Data
@Builder
@AllArgsConstructor
public class NoteResponse {
  Long id;
  String content;
  String createdBy;
  @JsonFormat(pattern = DateUtils.FORMAT_YYYY_MM_DD_HH_MM_SS)
  Date createdDate;
}
