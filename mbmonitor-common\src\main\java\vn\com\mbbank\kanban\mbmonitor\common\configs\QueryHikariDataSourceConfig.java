package vn.com.mbbank.kanban.mbmonitor.common.configs;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import jakarta.annotation.PreDestroy;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/14/2025
 */
@Configuration
public class QueryHikariDataSourceConfig {

  @Value("${mbmonitor.database.hikari.minimumIdle:5}")
  private Integer minimumIdle;

  @Value("${mbmonitor.database.hikari.maximumPoolSize:10}")
  private Integer maximumPoolSize;

  @Value("${monitor.database.hikari.idleTimeout:1000}")
  private Integer idleTimeout;

  @Value("${mbmonitor.database.hikari.poolName:monitorCP}")
  private String poolName;

  @Value("${mbmonitor.database.hikari.maxLifetime:2000000}")
  private Integer maxLifetime;
  @Value("${mbmonitor.database.hikari.connectionTimeout:30000}")
  private Integer connectionTimeout;


  private final ConcurrentHashMap<String, HikariDataSource> dataSourceCache =
      new ConcurrentHashMap<>();

  /**
   * Get hikari data source by connection string.
   *
   * @param connectionString connectionString
   * @return HikariDataSource
   */
  public HikariDataSource getDataSource(String connectionString) {
    return dataSourceCache.compute(connectionString, (connStr, existingDataSource) -> {
      if (existingDataSource == null || existingDataSource.isClosed()) {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(connStr);
        config.setMinimumIdle(minimumIdle);
        config.setMaximumPoolSize(maximumPoolSize);
        config.setIdleTimeout(idleTimeout);
        config.setPoolName(poolName);
        config.setMaxLifetime(maxLifetime);
        config.setConnectionTimeout(connectionTimeout);

        return new HikariDataSource(config);
      }
      return existingDataSource;
    });
  }

  @PreDestroy
  private void shutdown() {
    dataSourceCache.values().forEach(HikariDataSource::close);
    dataSourceCache.clear();
  }

}
