package vn.com.mbbank.kanban.mbmonitor.common.utils;

import vn.com.mbbank.kanban.core.common.ResponseData;

/**
 * Utils Response.
 */
public class ResponseUtils {

  /**
   * create success response data.
   *
   * @param data data
   * @param <T>  generic data
   * @return response success data
   */
  public static <T> ResponseData<T> success(T data) {
    return new ResponseData<T>().success(data);
  }

  /**
   * create error response data.
   *
   * @param status           data
   * @param errorCode        errorCode
   * @param errorDescription errorDescription
   * @return response success data
   */
  public static ResponseData<?> error(int status, String errorCode, String errorDescription) {
    return new ResponseData<>().error(status, errorCode, errorDescription);
  }
}
