package vn.com.mbbank.kanban.mbmonitor.common.services;

import java.util.List;
import vn.com.mbbank.kanban.core.services.common.BaseSoftService;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/19/2025
 */

public interface CommonApplicationService extends BaseSoftService<ApplicationEntity, String> {
  
  /**
   * Find all by list applicationNames or applicationIds and serviceIds.
   *
   * @param applicationNames applicationNames
   * @param applicationIds   applicationIds
   * @param serviceIds       serviceIds
   * @return list application
   */
  List<ApplicationEntity> findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(
      List<String> applicationNames, List<String> applicationIds, List<String> serviceIds);
}
