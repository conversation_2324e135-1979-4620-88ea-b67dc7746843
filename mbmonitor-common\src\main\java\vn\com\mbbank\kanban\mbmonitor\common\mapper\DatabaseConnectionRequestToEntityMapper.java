package vn.com.mbbank.kanban.mbmonitor.common.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.DatabaseConnectionRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseConnectionEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/6/2024
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DatabaseConnectionRequestToEntityMapper extends
    KanbanBaseMapper<DatabaseConnectionRequest, DatabaseConnectionEntity> {
  DatabaseConnectionRequestToEntityMapper INSTANCE =
      Mappers.getMapper(DatabaseConnectionRequestToEntityMapper.class);
}