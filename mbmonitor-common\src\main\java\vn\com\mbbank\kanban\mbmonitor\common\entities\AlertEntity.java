package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.AlertConstants;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertStatusEnum;

@Entity
@Data
@Table(name = TableName.ALERT)
@EqualsAndHashCode(callSuper = true)
public class AlertEntity extends BaseEntity<Long> {

  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "ALERT_SEQ", sequenceName = "ALERT_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ALERT_SEQ")
  private Long id;

  @Column(name = "CONTENT")
  private String content;

  @Column(name = "RECIPIENT")
  private String recipient;

  @Column(name = "ALERT_PRIORITY_CONFIG_ID", nullable = false)
  private Long alertPriorityConfigId;

  @Column(name = "STATUS", nullable = false)
  @Enumerated(EnumType.STRING)
  private AlertStatusEnum status;

  @Column(name = "SERVICE_ID", nullable = false)
  private String serviceId;

  @Column(name = "APPLICATION_ID", nullable = false)
  private String applicationId;

  @Column(name = "ALERT_GROUP_ID")
  private Long alertGroupId = AlertConstants.DEFAULT_ALERT_GROUP_ID;

  @Column(name = "SOURCE", nullable = false)
  @Enumerated(EnumType.STRING)
  private AlertSourceTypeEnum source;

  @Column(name = "CLOSED_DATE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date closedDate;

  @Column(name = "CLOSED_BY")
  private String closedBy;

  @Column(name = "CLOSED_DURATION")
  private Long closedDuration;


  @Override
  public Long getId() {
    return id;
  }
}
