package vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SqlExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionStatusEnum;

/**
 * ExecutionResult.
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ExecutionScriptResponse {
  ExecutionStatusEnum status;
  String result;
  String error;
  // response for SQL Execution
  SqlExecutionResponse sqlExecutionResponse;
}
