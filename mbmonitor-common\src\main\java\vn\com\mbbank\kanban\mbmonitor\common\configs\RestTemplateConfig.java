package vn.com.mbbank.kanban.mbmonitor.common.configs;

import java.io.UnsupportedEncodingException;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.core5.ssl.SSLContextBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.boot.web.client.RestTemplateCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;
import vn.com.mbbank.kanban.core.services.KanbanKeycloakCentralizedService;
import vn.com.mbbank.kanban.core.utils.RestTemplateUtils;
import vn.com.mbbank.kanban.mbmonitor.common.configs.httpclients.interceptors.ForwardHeadersInterceptor;
import vn.com.mbbank.kanban.mbmonitor.common.constants.RedisKeyConstants;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 1/9/2025
 */
@Configuration
@Slf4j
public class RestTemplateConfig {
  public static final String REST_TEMPLATE_INTERNAL = "REST_TEMPLATE_INTERNAL";
  public static final String REST_TEMPLATE_KEYCLOAK_CENTRALIZED =
      "REST_TEMPLATE_KEYCLOAK_CENTRALIZED";
  public static final String REST_TEMPLATE_NO_INTERCEPTOR = "REST_TEMPLATE_NO_INTERCEPTOR";
  public static final String REST_TEMPLATE_SKIP_SSL = "REST_TEMPLATE_SKIP_SSL";
  @Autowired
  ForwardHeadersInterceptor forwardHeadersInterceptor;
  @Value("${mbmonitor.common.restemplate.max.total:1000}")
  private Integer maxTotal;
  @Value("${mbmonitor.common.restemplate.max.per.route:100}")
  private Integer defaultMaxPerRoute;
  @Value("${mbmonitor.common.restemplate.connect.timeout:30000}")
  private Integer connectTimeout;
  @Value("${mbmonitor.common.restemplate.connect.request.timeout:20000}")
  private Integer connectionRequestTimeout;
  @Value("${mbmonitor.common.restemplate.socket.timeout:300000}")
  private Integer socketTimeout;
  @Value("${mbmonitor.common.restemplate.pool.time.to.live:1800}")
  private Integer timeToLive;
  @Value("${mbmonitor.common.restemplate.ssl:false}")
  private Boolean sslVerify;
  @Value("${mbmonitor.trusted.hostnames:''}")
  private String trustedHostnames;
  @Value("${mbmonitor.security.keycloak.centralized.timeout:60000}")
  private Integer centralizedTimeout;
  @Autowired
  private KanbanKeycloakCentralizedService kanbanKeycloakCentralizedService;


  /**
   * Configures and returns a {@link ClientHttpRequestFactory} instance
   * that bypasses SSL verification. This factory can be used for making
   * HTTP requests to endpoints with self-signed certificates or without
   * SSL verification.
   *
   * @return a configured {@link ClientHttpRequestFactory} instance with SSL verification skipped
   * @throws NoSuchAlgorithmException if the cryptographic algorithm is not available
   * @throws KeyStoreException        if an error occurs when accessing the keystore
   * @throws KeyManagementException   if there is an issue with key management
   */
  public ClientHttpRequestFactory getSkipSslFactory()
      throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
    SSLContext sslContext =
        SSLContextBuilder.create().loadTrustMaterial((KeyStore) null, (chain, authType) -> true).build();
    HttpsURLConnection.setDefaultSSLSocketFactory(sslContext.getSocketFactory());
    HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
    var factory =
        (SimpleClientHttpRequestFactory) (new RestTemplateBuilder(new RestTemplateCustomizer[0])).requestFactory(
                SimpleClientHttpRequestFactory.class)
            .build().getRequestFactory();
    factory.setConnectTimeout(centralizedTimeout);
    factory.setReadTimeout(centralizedTimeout);
    return factory;
  }

  /**
   * Use when calling an API outside of Spring's thread context,
   * such as calling an API within Kafka or any other thread not managed by Spring.
   *
   * @return RestTemplate
   * @throws NoSuchAlgorithmException ex
   * @throws KeyStoreException        ex
   * @throws KeyManagementException   ex
   */
  @Bean(name = REST_TEMPLATE_NO_INTERCEPTOR)
  public RestTemplate restTemplateNoInterceptor()
      throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
    RestTemplate restTemplate = new RestTemplate(restTemplateSkipSecurity().getRequestFactory());
    restTemplate.setInterceptors(Collections.emptyList());
    return restTemplate;
  }

  /**
   * Creates a {@link RestTemplate} bean that automatically forwards specific headers
   * from the incoming request to outgoing requests using the provided {@link ForwardHeadersInterceptor}.
   * This bean is registered with the name {@link #REST_TEMPLATE_INTERNAL}.
   * The {@link ForwardHeadersInterceptor} is added as an interceptor to the {@link RestTemplate},
   * ensuring that selected headers (such as authentication tokens or request IDs) are propagated
   * when making HTTP requests to other services.
   *
   * @return a {@link RestTemplate} instance with the {@link ForwardHeadersInterceptor} applied.
   */
  @Bean(name = REST_TEMPLATE_INTERNAL)
  public RestTemplate restTemplate()
      throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
    RestTemplate restTemplate = new RestTemplate(restTemplateSkipSecurity().getRequestFactory());
    restTemplate.setInterceptors(Collections.singletonList(forwardHeadersInterceptor));
    return restTemplate;
  }

  /**
   * Call rest with skip ssl.
   *
   * @return RestTemplate
   * @throws NoSuchAlgorithmException ex
   * @throws KeyStoreException        ex
   * @throws KeyManagementException   ex
   */
  @Bean(name = REST_TEMPLATE_SKIP_SSL)
  public RestTemplate restTemplateSkipSecurity()
      throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
    return new RestTemplate(RestTemplateUtils.getSkipSSLFactory());
  }

  /**
   * Creates a RestTemplate bean.
   *
   * @return a RestTemplate object
   */
  @Bean()
  @Primary
  public RestTemplate restTemplateDefault()
      throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
    return new RestTemplate(restTemplateSkipSecurity().getRequestFactory());
  }

  /**
   * RestTemplate for Keycloak Centralized.
   *
   * @return RestTemplate
   * @throws NoSuchAlgorithmException ex
   * @throws KeyStoreException        ex
   * @throws KeyManagementException   ex
   */
  @Bean(name = REST_TEMPLATE_KEYCLOAK_CENTRALIZED)
  public RestTemplate restTemplateKeycloakCentralized()
      throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException, UnsupportedEncodingException {
    RestTemplate restTemplate = new RestTemplate(getSkipSslFactory());
    ClientHttpRequestInterceptor dynamicTokenInterceptor = (request, body, execution) -> {
      String token = getKeyCloakToken(RedisKeyConstants.REDIS_KEY_ACCESS_TOKEN_KEYCLOAK_CENTRALIZED);
      request.getHeaders().set(HttpHeaders.AUTHORIZATION, token);
      request.getHeaders().setContentType(MediaType.APPLICATION_JSON);
      return execution.execute(request, body);
    };

    List<ClientHttpRequestInterceptor> interceptors = new ArrayList<>(restTemplate.getInterceptors());
    interceptors.add(dynamicTokenInterceptor);
    restTemplate.setInterceptors(interceptors);

    return restTemplate;
  }

  /**
   * getKeyCloakToken.
   *
   * @param redisAccessTokenKey token
   * @return redisAccessTokenKey
   */
  public String getKeyCloakToken(String redisAccessTokenKey) {
    try {
      RestTemplate restTemplate = new RestTemplate(RestTemplateUtils.getSkipSSLFactory());
      return kanbanKeycloakCentralizedService.getKeyCloakToken(restTemplate, redisAccessTokenKey);
    } catch (Exception e) {
      log.error(REST_TEMPLATE_KEYCLOAK_CENTRALIZED + e.getMessage());
      return "";
    }
  }
}
