package vn.com.mbbank.kanban.mbmonitor.common.utils.export;

import org.junit.jupiter.api.Test;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExportFileTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.export.factory.FileExporterFactory;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class FileExporterFactoryTest {

  @Test
  void constructor() {
    new FileExporterFactory();
  }

  @Test
  void getFileExporter_Csv() throws BusinessException {
    FileExporter exporter = FileExporterFactory.getFileExporter(ExportFileTypeEnum.CSV);
    assertTrue(exporter instanceof CsvExporter, "Expected a CsvExporter instance");
  }

  @Test
  void getFileExporter_Xlsx() throws BusinessException {
    FileExporter exporter = FileExporterFactory.getFileExporter(ExportFileTypeEnum.XLSX);
    assertTrue(exporter instanceof XlsxExporter, "Expected an XlsxExporter instance");
  }

  @Test
  void getFileExporter_InvalidType() {
    BusinessException exception = assertThrows(BusinessException.class, () ->
        FileExporterFactory.getFileExporter(ExportFileTypeEnum.PDF)
    );
    assertEquals(ErrorCode.FILE_EXPORT_IS_NOT_VALID.getCode(), exception.getCode(),
        "Expected FILE_EXPORT_IS_NOT_VALID error code");
  }
}
