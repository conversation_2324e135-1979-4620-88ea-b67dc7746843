package vn.com.mbbank.kanban.mbmonitor.common.mapper.log;

import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.DatabaseCollectLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseCollectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseConnectionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConfigCollectMapTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DatabaseCollectLogModelMapper
    extends KanbanBaseMapper<DatabaseCollectLogModel, DatabaseCollectEntity> {
  DatabaseCollectLogModelMapper INSTANCE = Mappers.getMapper(DatabaseCollectLogModelMapper.class);

  /**
   * map DatabaseCollectEntity to DatabaseCollectLogModel.
   *
   * @param config            AlertGroupConfigEntity.
   * @param connection        database connection.
   * @param customService     service
   * @param customApplication application
   * @param customPriority    priority
   * @return DatabaseCollectLogModel
   */
  default DatabaseCollectLogModel map(DatabaseCollectEntity config,
                                      DatabaseConnectionEntity connection,
                                      ServiceEntity customService,
                                      ApplicationEntity customApplication,
                                      AlertPriorityConfigEntity customPriority) {
    var databaseConnect = DatabaseCollectLogModel.Connection
        .builder()
        .connection(connection.getName())
        .alertIdField(config.getAlertIdField())
        .createdDateField(config.getCreatedDateField())
        .interval(config.getInterval())
        .build();
    var output = DatabaseCollectLogModel.CustomOutput
        .builder()
        .priorityType(config.getPriorityType())
        .applicationNameType(config.getApplicationType())
        .serviceNameType(config.getServiceNameType())
        .contactType(config.getContactType())
        .fromSourceContent(config.getAlertMapValue());
    if (ConfigCollectMapTypeEnum.FROM_SOURCE.equals(config.getPriorityType())) {
      output.fromSourcePriority(config.getPriorityMapValue());
    } else {
      output.customPriority(Objects.isNull(customPriority) ? null : customPriority.getName());
    }
    if (ConfigCollectMapTypeEnum.FROM_SOURCE.equals(config.getServiceNameType())) {
      output.fromSourceService(config.getServiceMapValue());
    } else {
      output.customService(Objects.isNull(customService) ? null : customService.getName());
    }
    if (ConfigCollectMapTypeEnum.FROM_SOURCE.equals(config.getApplicationType())) {
      output.fromSourceApplication(config.getApplicationMapValue());
    } else {
      output.customApplication(Objects.isNull(customApplication) ? null : customApplication.getName());
    }

    return DatabaseCollectLogModel.builder()
        .name(config.getName())
        .description(config.getDescription())
        .connect(databaseConnect)
        .output(output.build())
        .active(config.getIsActive())
        .build();
  }
}
