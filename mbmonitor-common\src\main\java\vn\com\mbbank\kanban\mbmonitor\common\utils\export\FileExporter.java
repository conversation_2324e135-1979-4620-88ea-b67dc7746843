package vn.com.mbbank.kanban.mbmonitor.common.utils.export;

import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportFileDto;

/**
 * FileExporter interface for exporting data into a specific file format.
 *
 */
public interface FileExporter {

  /**
   * Exports the provided data into a specific file format.
   *
   * @param data          the list of objects to be exported
   * @param exportFileDto the list of attribute metadata that defines the headers and attributes to be exported
   * @param <T>           the type of the elements in the list
   * @return StreamingResponseBody A stream of the exported file that can be sent directly to the client.
   * @throws IOException if an I/O error occurs during the export process
   */
  <T> StreamingResponseBody export(List<T> data, ExportFileDto exportFileDto) throws IOException;

  /**
   * Initializes the export process by setting up necessary configurations.
   *
   * @param exportFileDto metadata containing export configurations
   * @param outputStream  the output stream to write the exported data
   * @throws IOException if an I/O error occurs during initialization
   */
  void init(ExportFileDto exportFileDto, OutputStream outputStream) throws IOException;

  /**
   * Writes a batch of data to the export file.
   *
   * @param batchData     the batch of objects to be written
   * @param exportFileDto metadata defining export configurations
   * @param outputStream  the output stream to write the batch data
   * @param <T>           the type of the elements in the batch
   * @throws IOException if an I/O error occurs during writing
   */
  <T> void writeBatch(List<T> batchData, ExportFileDto exportFileDto, OutputStream outputStream) throws IOException;

  /**
   * Completes the export process and finalizes the output stream.
   *
   * @param outputStream the output stream to be closed or finalized
   * @throws IOException if an I/O error occurs during finalization
   */
  void close(OutputStream outputStream) throws IOException;
}
