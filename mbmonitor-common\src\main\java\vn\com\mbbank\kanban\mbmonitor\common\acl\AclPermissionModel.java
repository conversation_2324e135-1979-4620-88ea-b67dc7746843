package vn.com.mbbank.kanban.mbmonitor.common.acl;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 9/30/2024
 */
@AllArgsConstructor
@Builder
@EqualsAndHashCode
@RequiredArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Getter
@Setter
public class AclPermissionModel {
  @NonNull
  private PermissionModuleEnum module;
  @NonNull
  private PermissionActionEnum action;
  private PermissionTypeEnum type = PermissionTypeEnum.MODULE;
  private String id;
  private String parentId;
}
