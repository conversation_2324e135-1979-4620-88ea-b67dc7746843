package vn.com.mbbank.kanban.mbmonitor.repositories;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.repositories.AlertRepositoryQuery;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import java.util.List;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;


public class AlertRepositoryQueryTest {

  @InjectMocks
  AlertRepositoryQuery alertRepositoryQuery;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void buildQueryServiceIdEqual_success_inputEmpty() {
    PrepareQuery res =
        ReflectionTestUtils.invokeMethod(alertRepositoryQuery, "buildQueryServiceIdEqual",
            List.of());
    assertNull(res);
  }

  @Test
  void buildQueryServiceIdEqual_success_inputNotEmpty() {
    PrepareQuery res =
        ReflectionTestUtils.invokeMethod(alertRepositoryQuery, "buildQueryServiceIdEqual",
            List.of("asdf"));
    assertNotNull(res);
  }

  @Test
  void buildQueryApplicationIdEqual_success_inputEmpty() {
    PrepareQuery res =
        ReflectionTestUtils.invokeMethod(alertRepositoryQuery, "buildQueryApplicationIdEqual",
            List.of());
    assertNull(res);
  }

  @Test
  void buildQueryApplicationIdEqual_success_inputNotEmpty() {
    PrepareQuery res =
        ReflectionTestUtils.invokeMethod(alertRepositoryQuery, "buildQueryApplicationIdEqual",
            List.of("asdf"));
    assertNotNull(res);
  }

  @Test
  void buildCreateTimeGreaterThan_success() {
    PrepareQuery res =
        ReflectionTestUtils.invokeMethod(alertRepositoryQuery, "buildCreateTimeGreaterThan",
            1);
    assertNotNull(res);
  }

  @Test
  void buildQueryGroupStatusEq_success_inputEmpty() {
    PrepareQuery res =
        ReflectionTestUtils.invokeMethod(alertRepositoryQuery, "buildQueryGroupStatusEq",
            (Object) null);
    assertNull(res);
  }

  @Test
  void buildQueryGroupStatusEq_success_inputNotEmpty() {
    PrepareQuery res =
        ReflectionTestUtils.invokeMethod(alertRepositoryQuery, "buildQueryGroupStatusEq",
            AlertGroupStatusEnum.NEW);
    assertNotNull(res);
  }

}
