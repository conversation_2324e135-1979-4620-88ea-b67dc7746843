package vn.com.mbbank.kanban.mbmonitor.common.services.kafka;

import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/19/2025
 */
public interface SysLogKafkaProducerService {

  /**
   * send log message to kafka.
   *
   * @param action log action
   * @param params message param
   */
  void send(LogActionEnum action, Object... params);
}
