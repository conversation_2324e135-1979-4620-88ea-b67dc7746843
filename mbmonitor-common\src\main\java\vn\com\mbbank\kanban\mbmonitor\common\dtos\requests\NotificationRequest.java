package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationTypeEnum;

/**
 * NotificationRequest.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class NotificationRequest {
  @Size(min = 1, max = CommonConstants.NOTIFICATION_TITLE_MAX_LENGTH)
  @NotNull
  @NotBlank
  String title;
  @NotNull
  @NotBlank
  String content;
  @NotNull
  NotificationTypeEnum type;
  @NotNull
  String sourceId;
  @NotNull
  NotificationSourceTypeEnum sourceType;

  String userName;
  @Builder.Default
  List<String> userNames = new ArrayList<>();
  @Builder.Default
  List<Long> roleIds = new ArrayList<>();
}
