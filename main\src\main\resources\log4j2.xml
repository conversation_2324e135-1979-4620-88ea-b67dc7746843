<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="30">
    <Properties>
        <Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} %highlight{%5p}--[%T-%-15.15t]
            [%-20X{serviceMessageId}]%-40.40c{1.} :%m%n%ex
        </Property>
    </Properties>
    <Appenders>
        <Console name="ConsoleAppender" target="SYSTEM_OUT"
                 follow="true">
            <PatternLayout pattern="${LOG_PATTERN}"/>
        </Console>
        <Kafka name="KafkaAppender" topic="log-kanban"
               syncsend="false">
            <Property name="bootstrap.servers">10.1.16.247:9092,10.1.16.248:9092,10.1.16.249:9092
            </Property>
            <JsonTemplateLayout eventTemplateUri="classpath:jsonTemplateLayout.json">
                <EventTemplateAdditionalField key="application" value="mbmonitor-external-execution"/>
            </JsonTemplateLayout>
        </Kafka>
        <!-- Async Appender cho Console -->
        <Async name="AsyncConsoleAppender">
            <AppenderRef ref="ConsoleAppender"/>
        </Async>

        <!-- Async Appender cho Kafka -->
        <Async name="AsyncKafkaAppender">
            <AppenderRef ref="KafkaAppender"/>
        </Async>
    </Appenders>
    <Loggers>
        <Logger
                name="com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver"
                level="error" additivity="false">
            <AppenderRef ref="ConsoleAppender"/>
            <AppenderRef ref="KafkaAppender"/>
        </Logger>
        <Root level="info" includeLocation="false">
            <AppenderRef ref="ConsoleAppender"/>
            <AppenderRef ref="KafkaAppender"/>
        </Root>
    </Loggers>
</Configuration>
