package vn.com.mbbank.kanban.mbmonitor.common.mapper.log;

import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.EmailPartnerLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailPartnerAddressEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailPartnerEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface EmailPartnerLogModelMapper extends
    KanbanBaseMapper<EmailPartnerLogModel, EmailPartnerEntity> {
  EmailPartnerLogModelMapper INSTANCE = Mappers.getMapper(EmailPartnerLogModelMapper.class);

  /**
   * map EmailPartnerEntity to EmailPartnerLogModel.
   *
   * @param config                AlertGroupConfigEntity.
   * @param emailPartnerAddresses email partner
   * @return EmailPartnerLogModel
   */
  default EmailPartnerLogModel map(EmailPartnerEntity config,
                                   List<EmailPartnerAddressEntity> emailPartnerAddresses) {

    return EmailPartnerLogModel.builder()
        .name(config.getName())
        .contacts(emailPartnerAddresses.stream().map(EmailPartnerAddressEntity::getAddress).toList())
        .build();
  }
}
