package vn.com.mbbank.kanban.mbmonitor.common.services.systems.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.BeanCreationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.configs.redis.RedisAdapter;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonRedisService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 1/7/2025
 */
@Service
public class CommonRedisServiceImpl implements CommonRedisService {
  Logger logger = LoggerFactory.getLogger(this.getClass());

  @Autowired(required = false)
  @Lazy
  RedisAdapter redisAdapter;


  @Override
  public <T> T get(String key, Class<T> clazz) {
    if (!isConnected()) {
      return null;
    }
    return (T) redisAdapter.getRedisTemplate().opsForValue().get(key);
  }

  @Override
  public <T> List<T> getByPrefix(String prefix, Class<T> clazz) {
    if (!isConnected()) {
      return new ArrayList<>();
    }
    Set<Object> keys = redisAdapter.getRedisTemplate().keys(prefix + "*");
    List<T> values = new ArrayList<>();
    for (Object key : keys) {
      values.add(get(String.valueOf(key), clazz));
    }
    return values;
  }

  @Override
  public <T> T get(String key, T defaultValue, Class<T> clazz) {
    // default ignore exception
    try {
      if (!isConnected()) {
        return defaultValue;
      }
      var value = get(key, clazz);
      return value != null ? value : defaultValue;
    } catch (Exception ex) {
      logger.error("redis get error", ex);
      return defaultValue;
    }
  }

  @Override
  public void save(String key, Object data) {
    if (!isConnected()) {
      return;
    }
    redisAdapter.getRedisTemplate().opsForValue().set(key, data);
  }

  @Override
  public void save(String key, Object data, Long timeOutMs) {
    if (!isConnected()) {
      return;
    }
    redisAdapter.getRedisTemplate().opsForValue().set(key, data, timeOutMs, TimeUnit.MILLISECONDS);
  }

  @Override
  public boolean delete(String key) {
    if (!isConnected()) {
      return false;
    }
    return redisAdapter.getRedisTemplate().delete(key);
  }

  @Override
  public Long deleteByPrefixKey(String prefixKey) {
    if (!isConnected()) {
      return 0L;
    }
    Set<Object> keys = redisAdapter.getRedisTemplate().keys(prefixKey + "*");
    if (!KanbanCommonUtil.isEmpty(keys)) {
      return redisAdapter.getRedisTemplate().delete(keys);
    }
    return 0L;
  }

  @Override
  public boolean isExistsByKey(String key) {
    if (!isConnected()) {
      return false;
    }
    Boolean result = redisAdapter.getRedisTemplate().hasKey(key);
    return result != null && result;
  }

  @Override
  public boolean isConnected() {
    //TODO update redisAdapter.class  equired = false
    //return redisAdapter.getRedisTemplate() != null;
    try {
      return redisAdapter.getRedisTemplate() != null;
    } catch (BeanCreationException ex) {
      return false;
    }
  }
}
