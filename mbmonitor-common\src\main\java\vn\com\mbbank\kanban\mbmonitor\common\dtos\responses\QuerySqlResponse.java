package vn.com.mbbank.kanban.mbmonitor.common.dtos.responses;

import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * Model request service to create or update service.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class QuerySqlResponse {
  Long id;
  String name;
  String description;
  Long databaseConnectionId;
  String databaseConnectionName;
  Long groupQuerySqlId;
  String groupQuerySqlName;
  String command;
  List<String> params;
}
