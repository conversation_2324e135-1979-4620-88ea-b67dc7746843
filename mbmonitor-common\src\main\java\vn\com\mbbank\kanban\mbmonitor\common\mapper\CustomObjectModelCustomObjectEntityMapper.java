package vn.com.mbbank.kanban.mbmonitor.common.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.CustomObjectModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/30/2024
 */

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CustomObjectModelCustomObjectEntityMapper
    extends Kanban<PERSON>aseMapper<CustomObjectModel, CustomObjectEntity> {
  CustomObjectModelCustomObjectEntityMapper INSTANCE =
      Mappers.getMapper(CustomObjectModelCustomObjectEntityMapper.class);
}