package vn.com.mbbank.kanban.mbmonitor.external.execution.services;

import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecutionScriptRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionScriptResponse;

/**
 * interface logic Execution.
 */
public interface ExecutionService {

  /**
   * run script.
   *
   * @param request request param
   * @return ExecutionScriptResponse.
   */
  ExecutionScriptResponse execute(ExecutionScriptRequest request) throws BusinessException;

}