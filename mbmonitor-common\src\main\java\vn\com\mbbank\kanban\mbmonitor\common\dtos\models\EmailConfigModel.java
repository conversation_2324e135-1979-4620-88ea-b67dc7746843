package vn.com.mbbank.kanban.mbmonitor.common.dtos.models;


import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolSecurityTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolTypeEnum;

/**
 * Model service to create or update email config.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EmailConfigModel {
  Long id;
  String host;
  String description;
  int port;
  String password;
  EmailProtocolTypeEnum protocolType;
  EmailProtocolSecurityTypeEnum securityType;
  Long intervalTime;
  String email;
  boolean executed;
  String username;
}

