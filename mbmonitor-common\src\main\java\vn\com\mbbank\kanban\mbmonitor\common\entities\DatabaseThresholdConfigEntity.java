package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConditionOperatorEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 21/4/2025
 */
@Getter
@Setter
@Entity
@Table(name = "DATABASE_THRESHOLD_CONFIG")
@DynamicInsert
public class DatabaseThresholdConfigEntity extends BaseEntity<String> {
  @Id
  @Column(name = "ID")
  private String id = GeneratorUtil.generateId();

  @Column(name = "NAME")
  private String name;

  @Column(name = "DESCRIPTION")
  private String description;

  @Column(name = "DATABASE_CONNECTION_ID")
  private Long databaseConnectionId;

  @Column(name = "SQL_COMMAND")
  private String sqlCommand;

  @Column(name = "CRON_TIME")
  private String cronTime;

  @Column(name = "CONDITION_OPERATOR")
  @Enumerated(EnumType.STRING)
  private ConditionOperatorEnum conditionOperator;

  @Column(name = "CONDITION_VALUE")
  private Long conditionValue;

  @Column(name = "SERVICE_ID")
  private String serviceId;

  @Column(name = "APPLICATION_ID")
  private String applicationId;

  @Column(name = "PRIORITY_ID")
  private Long priorityId;

  @Column(name = "RECIPIENT")
  private String recipient;

  @Column(name = "CONTENT")
  private String content;

  @Column(name = "CONTENT_JSON")
  private String contentJson;

  @Column(name = "ACTIVE")
  private Boolean active;

}
