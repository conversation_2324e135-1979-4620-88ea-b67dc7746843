package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupConverter;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;

@Entity
@Data
@Table(name = TableName.ALERT_GROUP_CONFIG_CONDITION)
@EqualsAndHashCode(callSuper = true)
public class AlertGroupConfigConditionEntity extends BaseEntity<Long> {

  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "ALERT_GROUP_CONFIG_CONDITION_SEQ",
      sequenceName = "ALERT_GROUP_CONFIG_CONDITION_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ALERT_GROUP_CONFIG_CONDITION_SEQ")
  private Long id;

  @Column(name = "ALERT_GROUP_CONFIG_ID")
  private Long alertGroupConfigId;

  @Column(name = "RULE_GROUP")
  @Convert(converter = RuleGroupConverter.class)
  private RuleGroupType ruleGroup;

  @Column(name = "CUSTOM_OBJECT_ID")
  private Long customObjectId;

  @Override
  public Long getId() {
    return id;
  }
}
