package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupConverter;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;

@Entity
@Data
@Table(name = TableName.MODIFY_ALERT_CONFIG)
@EqualsAndHashCode(callSuper = true)
public class ModifyAlertConfigEntity extends BaseEntity<Long> {
  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "MODIFY_ALERT_CONFIG_SEQ",
      sequenceName = "MODIFY_ALERT_CONFIG_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "MODIFY_ALERT_CONFIG_SEQ")
  private Long id;

  @Column(name = "NAME", nullable = false)
  private String name;

  @Column(name = "DESCRIPTION")
  private String description;

  @Column(name = "ACTIVE")
  private Boolean active;

  @Column(name = "RULE_GROUP")
  @Convert(converter = RuleGroupConverter.class)
  private RuleGroupType ruleGroup;

  @Column(name = "POSITION")
  private Integer position;

  @Override
  public Long getId() {
    return id;
  }

}
