package vn.com.mbbank.kanban.mbmonitor.common.mapper.notification;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.NotificationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NotificationEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 17/12/2024
 */

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface NotificationEntityMapper extends KanbanBaseMapper<NotificationEntity, NotificationRequest> {
  NotificationEntityMapper INSTANCE = Mappers.getMapper(NotificationEntityMapper.class);

  /**
   * Maps the given NotificationRequest object to a NotificationEntity object, setting specific properties as required.
   *
   * @param notificationRequest the input request object containing notification details to be mapped
   * @return a NotificationEntity object created from the provided NotificationRequest
   */
  @Mapping(target = "isRead", expression = "java(false)")
  NotificationEntity map(NotificationRequest notificationRequest);
}
