package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.notifications;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import vn.com.mbbank.kanban.core.mapper.KanbanMapperUtils;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ProseMirrorNodeTypeEnum;

/**
 * Builder for creating ProseMirror document models.
 */
public final class ProseMirrorDocBuilder {

  private final List<Map<String, Object>> paragraphs = new ArrayList<>();

  /**
   * Private constructor to prevent instantiation.
   */
  private ProseMirrorDocBuilder() {
  }

  /**
   * Creates a new instance of {@link ProseMirrorDocBuilder}.
   *
   * @return a new builder instance
   */
  public static ProseMirrorDocBuilder create() {
    return new ProseMirrorDocBuilder();
  }

  /**
   * Adds a paragraph with the given text nodes.
   *
   * @param nodes the text nodes to add to the paragraph
   * @return the builder instance
   */
  public ProseMirrorDocBuilder addParagraph(TextNodeModel... nodes) {
    Map<String, Object> paragraph = Map.of(
        "type", ProseMirrorNodeTypeEnum.PARAGRAPH.getValue(),
        "content", List.of(nodes)
    );
    paragraphs.add(paragraph);
    return this;
  }

  /**
   * Builds the {@link ProseMirrorDocModel}.
   *
   * @return the built document model
   */
  public ProseMirrorDocModel build() {
    return new ProseMirrorDocModel(ProseMirrorNodeTypeEnum.DOC.getValue(), paragraphs);
  }

  /**
   * Creates a text node with formatted text.
   *
   * @param template the message format template
   * @param args     the arguments for the template
   * @return a new text node model
   */
  public static TextNodeModel text(String template, Object... args) {
    return new TextNodeModel(MessageFormat.format(template, args));
  }

  /**
   * Creates a text node with a link.
   *
   * @param label the link label
   * @param href  the link URL
   * @return a new text node model with a link
   */
  public static TextNodeModel link(String label, String href) {
    return TextNodeModel.withLink(label, href);
  }

  /**
   * Converts a ProseMirrorDocModel object to its JSON string representation.
   *
   * @param model the ProseMirror document model
   * @return the JSON string representation of the model
   */
  public static String toJson(ProseMirrorDocModel model) {
    return KanbanMapperUtils.objectToJson(model);
  }

}
