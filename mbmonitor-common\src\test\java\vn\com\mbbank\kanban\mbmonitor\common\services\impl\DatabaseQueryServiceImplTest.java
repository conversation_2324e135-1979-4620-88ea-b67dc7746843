package vn.com.mbbank.kanban.mbmonitor.common.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.sql.Connection;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.SQLSyntaxErrorException;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SqlExecutionResponse;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/20/2025
 */
class DatabaseQueryServiceImplTest {

  @Mock
  private Connection mockConnection;

  @Mock
  private PreparedStatement mockStatement;

  @Mock
  private ResultSet mockResultSet;

  @Mock
  private ResultSetMetaData mockMetaData;

  @InjectMocks
  private DatabaseQueryServiceImpl databaseQueryServiceImpl;

  @BeforeEach
  void setup() throws SQLException {
    MockitoAnnotations.openMocks(this);
    when(mockConnection.prepareStatement(anyString())).thenReturn(mockStatement);
    when(mockStatement.execute()).thenReturn(true);
    when(mockStatement.getResultSet()).thenReturn(mockResultSet);
    when(mockResultSet.getMetaData()).thenReturn(mockMetaData);
  }


  @Test
  void executeQuery_SelectSuccess() throws Exception {
    when(mockMetaData.getColumnCount()).thenReturn(2);
    when(mockMetaData.getColumnName(1)).thenReturn("id");
    when(mockMetaData.getColumnName(2)).thenReturn("name");
    when(mockResultSet.next()).thenReturn(true, false);
    when(mockResultSet.getString("id")).thenReturn("1");
    when(mockResultSet.getString("name")).thenReturn("Test Name");
    SqlExecutionResponse response = databaseQueryServiceImpl.executeQuery(mockConnection,
        "SELECT * FROM users", true, null, new HashMap<>());

    assertNotNull(response);
    assertFalse(response.isNonQuery());
    assertEquals(List.of("id", "name"), response.getListColumns());
    assertEquals(1, response.getListDataMappings().size());
    assertEquals("1",
        response.getListDataMappings().get(0).getListSqlMappingColumnDatas().get(0).getValue());
    assertEquals("Test Name",
        response.getListDataMappings().get(0).getListSqlMappingColumnDatas().get(1).getValue());

    verify(mockStatement).execute();
    verify(mockResultSet).close();
    verify(mockStatement).close();
  }

  @Test
  void executeQuery_SelectQuery() throws SQLException, BusinessException {
    String query = "SELECT id, name FROM users WHERE id = ?";
    Map<String, Object> params = new HashMap<>();
    params.put("id", 1);

    SqlExecutionResponse response =
        databaseQueryServiceImpl.executeQuery(mockConnection, query, false, 10, params);

    assertNotNull(response);
    assertTrue(response.isNonQuery());
  }

  @Test
  void executeQuery_SelectQuery_no_param() throws SQLException, BusinessException {
    String query = "SELECT id, name FROM users WHERE id = ?";

    SqlExecutionResponse response =
        databaseQueryServiceImpl.executeQuery(mockConnection, query, false, 10);

    assertNotNull(response);
    assertTrue(response.isNonQuery());
  }

  @Test
  void executeQuery_SelectQuery_two_param() throws SQLException, BusinessException {
    String query = "SELECT id, name FROM users WHERE id = ?";

    SqlExecutionResponse response =
        databaseQueryServiceImpl.executeQuery(mockConnection, query);

    assertNotNull(response);
    assertTrue(response.isNonQuery());
  }


  @Test
  void executeQuery_SelectQuery_three_param() throws SQLException, BusinessException {
    String query = "SELECT id, name FROM users WHERE id = ?";

    SqlExecutionResponse response =
        databaseQueryServiceImpl.executeQuery(mockConnection, query, false);

    assertNotNull(response);
    assertTrue(response.isNonQuery());
  }

  @Test
  void executeQuery_UpdateQuery() throws SQLException, BusinessException {
    String query =
        "UPDATE users SET name = :name, id = :id, string = :string, Integer = :Integer, \n" +
            "Long = :Long, Double = :Double, Date = :Date, Dateutil = :Dateutil, \n" +
            "Timestamp = :Timestamp, Boolean = :Boolean, object = :object \n" +
            "WHERE name = :name AND id = :id and anul = :keynull";
    Map<String, Object> params = new HashMap<>();
    params.put("Timestamp", new Timestamp(11111));
    params.put("name", "New Name");
    params.put("id", 1);
    params.put("string", "");
    params.put("Integer", 1);
    params.put("Long", 1L);
    params.put("Double", Double.valueOf(1));
    params.put("Date", new Date(11111));
    params.put("Dateutil", new java.util.Date(11111));
    params.put("Boolean", true);
    params.put("object", Float.valueOf(1));
    params.put("keynull", null);
    when(mockStatement.execute()).thenReturn(false);

    when(mockStatement.getUpdateCount()).thenReturn(2);

    SqlExecutionResponse response =
        databaseQueryServiceImpl.executeQuery(mockConnection, query, false, 10, params);

    assertNotNull(response);
    assertTrue(response.isNonQuery());
    assertEquals(1, response.getListColumns().size());
    assertEquals("total", response.getListColumns().get(0));
    assertEquals(1, response.getListDataMappings().size());
    assertEquals("2",
        response.getListDataMappings().get(0).getListSqlMappingColumnDatas().get(0).getValue());
  }

  @Test
  void executeQuery_SQLSyntaxErrorException() throws SQLException {
    String query = "INVALID SQL SYNTAX";
    Map<String, Object> params = new HashMap<>();
    params.put("string", "");
    params.put("Integer", 1);
    params.put("Long", 1L);
    params.put("Double", Double.valueOf(1));
    params.put("Date", new Date(11111));
    params.put("Dateutil", new java.util.Date(11111));
    params.put("Timestamp", new Timestamp(11111));
    params.put("Boolean", true);
    params.put("object", Float.valueOf(1));

    when(mockStatement.execute()).thenThrow(new SQLSyntaxErrorException("Syntax error"));
    BusinessException thrown = assertThrows(BusinessException.class, () -> {
      databaseQueryServiceImpl.executeQuery(mockConnection, query, false, 10, params);
    });

    assertEquals("Syntax error", thrown.getMessage());
  }

  @Test
  void executeQuery_SQLException() throws SQLException {
    String query = "SELECT * FROM users";
    Map<String, Object> params = new HashMap<>();

    when(mockStatement.execute()).thenThrow(new SQLException("Database error"));

    BusinessException thrown = assertThrows(BusinessException.class, () -> {
      databaseQueryServiceImpl.executeQuery(mockConnection, query, false, 10, params);
    });

    assertEquals("Database error", thrown.getMessage());
  }


}
