package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 12/17/2024
 */
@Entity
@Getter
@Setter
@Table(name = TableName.JOB_HISTORY)
public class JobHistoryEntity extends BaseEntity<Long> {
  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "JOB_HISTORY_SEQ", sequenceName = "JOB_HISTORY_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "JOB_HISTORY_SEQ")
  private Long id;

  @Column(name = "JOB_TYPE")
  private String jobType;

  @Column(name = "JOB_ID")
  private Long jobId;

  @Column(name = "TOTAL")
  private Long total;

  @Column(name = "SUCCESS")
  private Long success;

  @Column(name = "START_DATE")
  private Date startDate;

  @Column(name = "END_DATE")
  private Date endDate;

  @Column(name = "DESCRIPTION")
  private String description;
}
