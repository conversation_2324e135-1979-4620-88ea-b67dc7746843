package vn.com.mbbank.kanban.mbmonitor.common.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import org.junit.jupiter.api.Test;

public class StringUtilsTest {
  @Test
  void constructor() {
    new StringUtils();
  }

  @Test
  void capitalizeFirstLetter_NullInput() {
    assertNotNull(StringUtils.capitalizeFirstLetter("  "));
  }

  @Test
  void capitalizeFirstLetter_EmptyInput() {
    assertEquals("", StringUtils.capitalizeFirstLetter(""));
  }

  @Test
  void capitalizeFirstLetter_SingleLowercaseCharacter() {
    assertEquals("A", StringUtils.capitalizeFirstLetter("a"));
  }

  @Test
  void capitalizeFirstLetter_SingleUppercaseCharacter() {
    assertEquals("A", StringUtils.capitalizeFirstLetter("A"));
  }

  @Test
  void capitalizeFirstLetter_StartsWithLowercase() {
    assertEquals("Hello", StringUtils.capitalizeFirstLetter("hello"));
  }

  @Test
  void capitalizeFirstLetter_StartsWithUppercase() {
    assertEquals("Hello", StringUtils.capitalizeFirstLetter("Hello"));
  }

  @Test
  void capitalizeFirstLetter_StartsWithNonAlphabetic() {
    assertEquals("1hello", StringUtils.capitalizeFirstLetter("1hello"));
  }
}
