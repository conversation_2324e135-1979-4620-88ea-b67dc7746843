package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.JobHistoryStatusEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 12/17/2024
 */
@Entity
@Getter
@Setter
@Table(name = TableName.JOB_HISTORY_DETAIL)
public class JobHistoryDetailEntity extends BaseEntity<Long> {
  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "JOB_HISTORY_DETAIL_SEQ", sequenceName = "JOB_HISTORY_DETAIL_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "JOB_HISTORY_DETAIL_SEQ")
  private Long id;

  @Column(name = "HISTORY_ID")
  private Long historyId;

  @Column(name = "ALERT_ID")
  private Long alertId;

  @Column(name = "STATUS")
  @Enumerated(EnumType.STRING)
  private JobHistoryStatusEnum status;

  @Column(name = "DESCRIPTION")
  private String description;
}
