package vn.com.mbbank.kanban.mbmonitor.common.mapper.log;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.ApplicationLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ApplicationLogModelMapper extends
    KanbanBaseMapper<ApplicationLogModel, ApplicationEntity> {
  ApplicationLogModelMapper INSTANCE = Mappers.getMapper(ApplicationLogModelMapper.class);

  /**
   * map  ApplicationLogModel.
   *
   * @param application ApplicationEntity
   * @param service     ServiceEntity
   * @return ApplicationLogModel
   */
  default ApplicationLogModel map(ApplicationEntity application, ServiceEntity service) {
    var res = new ApplicationLogModel();
    res.setName(application.getName());
    res.setDescription(application.getDescription());
    res.setService(service.getName());
    return res;
  }
}
