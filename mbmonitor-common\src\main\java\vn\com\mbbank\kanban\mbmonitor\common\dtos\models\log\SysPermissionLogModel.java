package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SysPermissionLogModel {
  private PermissionModuleEnum module;
  private PermissionActionEnum action;
  private Long roleId;
  private PermissionTypeEnum type;
  private String moduleId;
  private String moduleParentId;
}
