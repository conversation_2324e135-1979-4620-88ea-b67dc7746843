package vn.com.mbbank.kanban.mbmonitor.common.services;

import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.mbmonitor.common.constants.BeanNameConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.NotificationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.NotificationResponse;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/19/2025
 */
public interface PushNotificationService {

  /**
   * push notification.
   *
   * @param notificationRequest notificationRequest
   */
  @Async(BeanNameConstants.COMMON_TASK_EXECUTOR)
  void push(NotificationRequest notificationRequest);

  /**
   * push notification.
   *
   * @param notificationRequest notificationRequest
   * @return NotificationResponse
   */
  ResponseEntity<ResponseData<NotificationResponse>> pushSync(NotificationRequest notificationRequest);
}
