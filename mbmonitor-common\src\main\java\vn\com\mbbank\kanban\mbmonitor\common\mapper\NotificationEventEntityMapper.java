package vn.com.mbbank.kanban.mbmonitor.common.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.NotificationEventRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NotificationEventEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationEventScheduleTypeEnum;

/**
 * Mapper for NotificationEventEntity.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface NotificationEventEntityMapper extends
    KanbanBaseMapper<NotificationEventEntity, NotificationEventRequest> {
  NotificationEventEntityMapper INSTANCE = Mappers.getMapper(NotificationEventEntityMapper.class);

  /**
   * Merge the request into the entity.
   *
   * @param entity  the entity
   * @param request the request
   * @return the merged entity
   */
  default NotificationEventEntity merge(NotificationEventEntity entity, NotificationEventRequest request) {
    entity.setTitle(request.getTitle());
    entity.setContent(request.getContent());
    entity.setNotificationType(request.getNotificationType());
    entity.setScheduleType(request.getScheduleType());
    entity.setTriggeredDate(request.getTriggeredDate());
    entity.setCronExpression(request.getCronExpression());
    if (NotificationEventScheduleTypeEnum.ONE_TIME.equals(request.getScheduleType())) {
      entity.setCronExpression(null);
    } else if (NotificationEventScheduleTypeEnum.CRON_EXPRESSION.equals(request.getScheduleType())) {
      entity.setTriggeredDate(null);
    }
    return entity;
  }
}