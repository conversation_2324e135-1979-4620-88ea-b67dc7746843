package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;

@Data
@Entity
@Table(name = "QUERY_SQL")
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class QuerySqlEntity extends BaseEntity<Long> {

  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "QUERY_SQL_SEQ", sequenceName = "QUERY_SQL_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "QUERY_SQL_SEQ")
  private Long id;

  @Column(name = "NAME", nullable = false)
  private String name;

  @Column(name = "GROUP_QUERY_SQL_ID", nullable = false)
  private Long groupQuerySqlId;

  @Column(name = "DATABASE_CONNECTION_ID", nullable = false)
  private Long databaseConnectionId;

  @Column(name = "DESCRIPTION")
  private String description;

  @Column(name = "COMMAND")
  private String command;

  @Override
  public Long getId() {
    return id;
  }
}
