package vn.com.mbbank.kanban.mbmonitor.common.configs;

import java.util.concurrent.ThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import vn.com.mbbank.kanban.mbmonitor.common.constants.BeanNameConstants;

/**
 * Configuration class for creating ThreadPool config bean.
 */

@Configuration
@EnableAsync
public class CommonThreadPoolConfig {

  @Value("${thread-pool.pool-size.core:5}")
  private Integer corePoolSize;

  @Value("${thread-pool.pool-size.max:50}")
  private Integer maxPoolSize;

  @Value("${thread-pool.queue-capacity:500}")
  private Integer queueCapacity;

  @Value("${thread-pool.thread-name-prefix:Request-}")
  private String threadNamePrefix;

  @Value("${thread-pool.await-termination-seconds:120}")
  private int awaitTerminationSeconds;

  /**
   * Creates a taskExecutor bean.
   *
   * @return an Executor object
   */
  @Bean(name = BeanNameConstants.COMMON_TASK_EXECUTOR)
  public TaskExecutor commonTaskExecutor() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setDaemon(true);
    executor.setCorePoolSize(corePoolSize);
    executor.setMaxPoolSize(maxPoolSize);
    executor.setQueueCapacity(queueCapacity);
    executor.setAwaitTerminationSeconds(awaitTerminationSeconds);
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    executor.setThreadNamePrefix(threadNamePrefix);
    executor.initialize();
    return executor;
  }

}
