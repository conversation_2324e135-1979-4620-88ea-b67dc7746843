package vn.com.mbbank.kanban.mbmonitor.common.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.NoteResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NoteEntity;

/**
 * Mapper logic NoteResponseMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface NoteResponseMapper extends KanbanBaseMapper<NoteResponse, NoteEntity> {
  NoteResponseMapper INSTANCE = Mappers.getMapper(NoteResponseMapper.class);
}
