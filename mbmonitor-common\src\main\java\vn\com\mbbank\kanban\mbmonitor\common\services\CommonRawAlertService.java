package vn.com.mbbank.kanban.mbmonitor.common.services;

import java.util.List;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertSourceTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/19/2025
 */
public interface CommonRawAlertService {
  /**
   * Create raw alert data.
   *
   * @param alerts alerts
   * @param source source
   * @return list AlertBaseModel
   */
  List<AlertBaseModel> createRawData(List<AlertBaseModel> alerts, AlertSourceTypeEnum source);

  /**
   * set raw data Name from ID after save alert.
   *
   * @param alerts alerts
   * @return list AlertBaseModel
   */
  List<AlertBaseModel> convertIdToRawData(List<AlertBaseModel> alerts);
}
