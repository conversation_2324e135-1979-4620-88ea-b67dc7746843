package vn.com.mbbank.kanban.mbmonitor.common.services.systems.impl;

import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SysUserResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysUserEntity;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.ServerIntegration;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonRedisService;
import vn.com.mbbank.kanban.mbmonitor.common.services.systems.CommonUserService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.RedisUtils;

@Component
public class CommonUserServiceImpl implements CommonUserService {

  @Autowired
  private ServerIntegration serverIntegration;

  @Autowired
  CommonRedisService commonRedisService;

  @Override
  public SysUserResponse findOrCreateNew(String userName) {
    String key = RedisUtils.getUserResponseKey(userName);
    if (commonRedisService.isExistsByKey(key)) {
      // exist data into redis return data redis
      var dataRedis = commonRedisService.get(key, null, SysUserResponse.class);
      if (!KanbanCommonUtil.isEmpty(dataRedis)) {
        return dataRedis;
      }
    }
    //call api me and save to redis
    var dataFromApi = serverIntegration.getMe().getData();
    commonRedisService.save(key, dataFromApi);
    return dataFromApi;
  }

  @Override
  public Optional<SysUserEntity> findByUserName(String userName) {
    return Optional.ofNullable(findOrCreateNew(userName));
  }

}
