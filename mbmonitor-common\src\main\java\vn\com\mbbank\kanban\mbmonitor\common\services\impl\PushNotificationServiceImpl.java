package vn.com.mbbank.kanban.mbmonitor.common.services.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.mbmonitor.common.configs.RestTemplateConfig;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.NotificationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.NotificationResponse;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ExternalRtsUrl;
import vn.com.mbbank.kanban.mbmonitor.common.services.PushNotificationService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/19/2025
 */
@Service
public class PushNotificationServiceImpl implements PushNotificationService {
  @Autowired
  @Qualifier(RestTemplateConfig.REST_TEMPLATE_KEYCLOAK_CENTRALIZED)
  RestTemplate restTemplateKeycloakCentralized;

  @Override
  public void push(NotificationRequest notification) {
    restTemplateKeycloakCentralized.exchange(ExternalRtsUrl.getUrl(ExternalRtsUrl.NOTIFICATION),
        HttpMethod.POST, new HttpEntity<>(notification),
        new ParameterizedTypeReference<ResponseData<NotificationResponse>>() {
        });
  }

  @Override
  public ResponseEntity<ResponseData<NotificationResponse>> pushSync(NotificationRequest notification) {
    return restTemplateKeycloakCentralized.exchange(ExternalRtsUrl.getUrl(ExternalRtsUrl.NOTIFICATION),
        HttpMethod.POST, new HttpEntity<>(notification),
        new ParameterizedTypeReference<>() {
        });
  }
}
