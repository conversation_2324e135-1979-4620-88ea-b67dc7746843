package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationEventScheduleTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationTypeEnum;

/**
 * Request DTO for notification event.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationEventRequest {
  private String id;
  @Size(min = 1, max = CommonConstants.NOTIFICATION_TITLE_MAX_LENGTH)
  @NotNull
  @NotBlank
  private String title;
  @Size(min = 1, max = CommonConstants.NOTIFICATION_CONTENT_MAX_LENGTH)
  @NotNull
  @NotBlank
  private String content;
  @NotNull
  private NotificationTypeEnum notificationType;
  @NotNull
  private NotificationEventScheduleTypeEnum scheduleType;
  private Date triggeredDate;
  private String cronExpression;
  private List<String> userNames;
  private List<Long> roleIds;
}