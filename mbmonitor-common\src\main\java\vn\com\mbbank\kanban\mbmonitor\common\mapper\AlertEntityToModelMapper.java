package vn.com.mbbank.kanban.mbmonitor.common.mapper;


import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ModifyAlertModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 18/02/2025
 */

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AlertEntityToModelMapper extends KanbanBaseMapper<ModifyAlertModel, AlertEntity> {
  AlertEntityToModelMapper INSTANCE = Mappers.getMapper(AlertEntityToModelMapper.class);


}
