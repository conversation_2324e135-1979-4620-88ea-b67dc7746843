package vn.com.mbbank.kanban.mbmonitor.common.services.impl;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.SQLSyntaxErrorException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterUtils;
import org.springframework.jdbc.core.namedparam.ParsedSql;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SqlExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.services.DatabaseQueryService;

@Service
public class DatabaseQueryServiceImpl implements DatabaseQueryService {


  @Override
  public SqlExecutionResponse executeQuery(Connection conn, String query)
      throws BusinessException, SQLException {
    return executeQuery(conn, query, false);
  }

  @Override
  public SqlExecutionResponse executeQuery(Connection conn, String query, Boolean isHikari)
      throws BusinessException, SQLException {
    return executeQuery(conn, query, isHikari, 0);
  }


  /**
   * get data map string
   *
   * @param conn  connection string.
   * @param query query
   * @return SqlResultDataDto
   * @throws BusinessException exception
   * @throws SQLException      exception
   */

  @Override
  public SqlExecutionResponse executeQuery(Connection conn, String query, Boolean isHikari,
                                           Integer maxRow)
      throws BusinessException, SQLException {
    return executeQuery(conn, query, isHikari, maxRow, null);
  }

  @Override
  public SqlExecutionResponse executeQuery(Connection conn, String query, Boolean isHikari,
                                           Integer maxRow, Map<String, ?> map)
      throws BusinessException, SQLException {
    ResultSet rs = null;
    PreparedStatement stmt = null;
    ResultSetMetaData rsmd = null;
    try {
      stmt = createPreparedStatement(conn, query, map);
      //TODO thangnv9 setMaxRows it's bad solution limit data => research and update other solution in feature
      if (maxRow != null && maxRow >= 0) {
        stmt.setMaxRows(maxRow);
      }
      boolean isSelect = stmt.execute();
      if (!isSelect) {
        int columnCount = stmt.getUpdateCount();
        var result = SqlExecutionResponse.builder();
        List<SqlExecutionResponse.SqlDataMapping> listData = new ArrayList<>();
        List<SqlExecutionResponse.SqlMappingColumnData> data = new ArrayList<>();
        List<String> listColumn = new ArrayList<>();
        listColumn.add("total");
        data.add(SqlExecutionResponse.SqlMappingColumnData.builder().column("total")
            .value(String.valueOf(columnCount)).build());
        listData.add(
            SqlExecutionResponse.SqlDataMapping.builder().listSqlMappingColumnDatas(data).build());
        result.listDataMappings(listData);
        result.listColumns(listColumn);
        result.isNonQuery(true);
        if (!isHikari) {
          conn.commit();
        }
        return result.build();
      }
      rs = stmt.getResultSet();
      rsmd = rs.getMetaData();
      int columnCount = rsmd.getColumnCount();
      List<SqlExecutionResponse.SqlMappingColumnData> listSqlMappingColumnData = new ArrayList<>();
      List<SqlExecutionResponse.SqlDataMapping> listSqlDataMapping = new ArrayList<>();
      List<String> columnNameList = new ArrayList<>();
      // Get all column response
      for (int i = 1; i <= columnCount; i++) {
        columnNameList.add(rsmd.getColumnName(i));
      }
      while (rs.next()) { //&& !columnNameList.isEmpty()
        listSqlMappingColumnData.clear();
        // Get list data
        for (int i = 1; i <= columnCount; i++) {
          String data = null;
          data = rs.getString(rsmd.getColumnName(i));
          SqlExecutionResponse.SqlMappingColumnData sqlMappingColumn =
              SqlExecutionResponse.SqlMappingColumnData.builder().column(rsmd.getColumnName(i))
                  .value(data).build();
          listSqlMappingColumnData.add(sqlMappingColumn);
        }
        listSqlDataMapping.add(SqlExecutionResponse.SqlDataMapping.builder()
            .listSqlMappingColumnDatas(new ArrayList<>(listSqlMappingColumnData)).build());
      }
      if (!isHikari) {
        conn.commit();
      }
      return SqlExecutionResponse.builder().listDataMappings(listSqlDataMapping)
          .listColumns(columnNameList).isNonQuery(columnNameList.isEmpty()).build();
    } catch (SQLSyntaxErrorException e) {
      throw new BusinessException(e.getMessage(), HttpStatus.BAD_REQUEST);
    } catch (SQLException e) {
      throw new BusinessException(e.getMessage(), HttpStatus.BAD_REQUEST);
    } finally {
      conn.close();
      if (rs != null) {
        rs.close();
      }
      if (stmt != null) {
        stmt.close();
      }
    }
  }

  public PreparedStatement createPreparedStatement(Connection conn, String query,
                                                   Map<String, ?> params)
      throws SQLException, BusinessException {
    if (params == null || params.isEmpty()) {
      return conn.prepareStatement(query);
    }

    // Parse the SQL query and replace named parameters with `?`
    ParsedSql parsedSql = NamedParameterUtils.parseSqlStatement(query);
    String processedQuery =
        NamedParameterUtils.substituteNamedParameters(parsedSql, new MapSqlParameterSource(params));

    // Create the PreparedStatement
    PreparedStatement stmt = conn.prepareStatement(processedQuery);

    // Bind parameters to the PreparedStatement
    SqlParameterSource paramSource = new MapSqlParameterSource(params);
    Object[] paramValues = NamedParameterUtils.buildValueArray(parsedSql, paramSource, null);

    for (int i = 0; i < paramValues.length; i++) {
      Object value = paramValues[i];
      if (value != null) {
        if (value instanceof String) {
          stmt.setString(i + 1, (String) value);
        } else if (value instanceof Integer) {
          stmt.setInt(i + 1, (Integer) value);
        } else if (value instanceof Long) {
          stmt.setLong(i + 1, (Long) value);
        } else if (value instanceof Double) {
          stmt.setDouble(i + 1, (Double) value);
        } else if (value instanceof java.sql.Date) {
          stmt.setDate(i + 1, (java.sql.Date) value);
        } else if (value instanceof java.util.Date) {
          stmt.setDate(i + 1, new java.sql.Date(((java.util.Date) value).getTime()));
        } else if (value instanceof Boolean) {
          stmt.setBoolean(i + 1, (Boolean) value);
        } else {
          stmt.setObject(i + 1, value);
        }
      } else {
        stmt.setNull(i + 1, Types.NULL);
      }
    }

    return stmt;
  }

}
