package vn.com.mbbank.kanban.mbmonitor.common.acl;


import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.logging.log4j.core.config.Order;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;
import vn.com.mbbank.kanban.core.annotations.SkipAuthentication;
import vn.com.mbbank.kanban.core.configs.KeyCloakFilter;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.exceptions.BusinessRuntimeException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.ServerIntegration;
import vn.com.mbbank.kanban.mbmonitor.common.services.systems.CommonAclPermissionService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/4/2024
 */

@Component
@Order(Ordered.LOWEST_PRECEDENCE)
public class AclPermissionFilter extends KeyCloakFilter {
  @Autowired
  private RequestMappingHandlerMapping requestMappingHandlerMapping;

  @Autowired
  private CommonAclPermissionService commonAclPermissionService;

  @Autowired
  @Qualifier("handlerExceptionResolver")
  private HandlerExceptionResolver resolver;

  @Autowired
  @Lazy
  ServerIntegration serverIntegration;

  protected static final Logger logger = LoggerFactory.getLogger(AclPermissionFilter.class);

  @Override
  protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
                                  FilterChain filterChain) throws ServletException, IOException {

    try {
      logger.info("Before filter alc permission");
      checkPermission(request);
      logger.info("END filter alc permission");
      logger.info("Next  filter authFilter");
      filterChain.doFilter(request, response);
    } catch (BusinessRuntimeException | BusinessException e) {
      resolver.resolveException(request, response, null, e);
    }
  }

  /**
   * check permission.
   *
   * @param request request
   * @throws BusinessException ex
   */
  public void checkPermission(HttpServletRequest request) throws BusinessException {
    try {
      HandlerMethod handlerMethod =
          (HandlerMethod) requestMappingHandlerMapping.getHandler(request).getHandler();

      HasPermission hasPermission = handlerMethod.getMethodAnnotation(HasPermission.class);
      SkipAuthentication skipAuthentication =
          handlerMethod.getMethodAnnotation(SkipAuthentication.class);
      if (skipAuthentication != null) {
        return;
      }
      if (hasPermission != null && hasPermission.value() != null) {
        var permissions = Arrays.stream(hasPermission.value())
            .map(obj -> new AclPermissionModel(obj.module(), obj.action()))
            .collect(Collectors.toList());
        // Only check permission when use @HasPermission
        checkPermissionByAcl(permissions, hasPermission.mappingAll());
      }
    } catch (BusinessRuntimeException e) {
      throw e;
    } catch (Exception e) {
      //exception when call HealthCheck from cicd
      //throw new BusinessRuntimeException(ErrorCode.USER_DONT_HAVE_PERMISSIONS);
    }

  }

  private void checkPermissionByAcl(List<AclPermissionModel> aclPermissionModels,
                                    boolean isMappingAll)  {
    boolean isPermission;
    try {
      isPermission = commonAclPermissionService.isAnyPermission(aclPermissionModels, isMappingAll);
    } catch (Exception e) {
      logger.error("isAnyPermission error runtime ", e);
      isPermission = false;
    }
    if (!isPermission) {
      throw new BusinessRuntimeException(ErrorCode.USER_DONT_HAVE_PERMISSIONS);
    }
  }

}
