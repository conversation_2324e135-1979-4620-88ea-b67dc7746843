package vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls;

import vn.com.mbbank.kanban.mbmonitor.common.configs.KanbanPropertyConfigUtils;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.BaseUrl;

/**
 * External execution URL.
 */
public class ExternalExecutionUrl extends BaseUrl {
  public static final String VERSION = "/v1";
  public static final String EXECUTE_SCRIPT = VERSION + "/execute_scripts";

  /**
   * Demo for ExternalExecutionUrl.
   *
   * @param path Path
   */
  public ExternalExecutionUrl(String path) {
    super(path);
  }

  /**
   * Load url from properties.
   *
   * @param baseApi baseApi
   * @return url full
   */
  public static String getUrl(String baseApi) {
    return KanbanPropertyConfigUtils.getProperty("monitor.external.execution.url",
        getBaseUrl() + "api/external-execution") + baseApi;
  }

}
