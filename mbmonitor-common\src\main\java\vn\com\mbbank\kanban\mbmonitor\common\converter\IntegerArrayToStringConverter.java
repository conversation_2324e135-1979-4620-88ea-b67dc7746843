package vn.com.mbbank.kanban.mbmonitor.common.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.util.Arrays;
import java.util.stream.Collectors;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;

/**
 * Information of Author.
 * Author: quandh2.os
 */
@Converter
public class IntegerArrayToStringConverter implements AttributeConverter<Integer[], String> {
  
  /**
   * Converts an Integer array to a comma-separated String for storing in the database.
   *
   * @param attribute the Integer array to convert
   * @return a comma-separated String, or an empty String if the input is null or empty
   */
  @Override
  public String convertToDatabaseColumn(Integer[] attribute) {
    if (KanbanCommonUtil.isEmpty(attribute)) {
      return CommonConstants.DEFAULT_WILDCARD;
    }
    return Arrays.stream(attribute)
      .map(String::valueOf)
      .collect(Collectors.joining(CommonConstants.DEFAULT_DELIMITER));
  }
  
  /**
   * Converts a comma-separated String from the database to an Integer array.
   *
   * @param dbData the String value from the database
   * @return an Integer array, or an empty array if the input is null or blank
   */
  @Override
  public Integer[] convertToEntityAttribute(String dbData) {
    if (KanbanCommonUtil.isEmpty(dbData) || CommonConstants.DEFAULT_WILDCARD.equals(dbData)) {
      return new Integer[0];
    }
    return Arrays.stream(dbData.split(CommonConstants.DEFAULT_DELIMITER))
      .map(String::trim)
      .map(Integer::valueOf)
      .toArray(Integer[]::new);
  }
}
