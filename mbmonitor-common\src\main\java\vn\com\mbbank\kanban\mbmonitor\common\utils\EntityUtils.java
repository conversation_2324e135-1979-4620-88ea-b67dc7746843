package vn.com.mbbank.kanban.mbmonitor.common.utils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/11/2024
 */
public class EntityUtils {
  /**
   * Retrieve and sort names of entities based on the order of provided IDs.
   *
   * @param ids           List of IDs (can be of any type, e.g., String, Long) to search for
   * @param entities      List of entities that contain both ID and name
   * @param orderField    Function to extract the order field from an entity (can return any type)
   * @param retrieveField Function to extract the retrieve field from an entity
   * @param <T>           Type of the entity
   * @param <I>           The type of the ID or ordering field (e.g., String, Long)
   * @param <R>           The type of the field to retrieve (any type)
   * @return List of field sorted by the provided IDs, or an empty list if no matches are found
   */
  public static <T, I, R> List<R> getFieldsByOrder(List<I> ids, List<T> entities,
                                                   Function<T, I> orderField,
                                                   Function<T, R> retrieveField) {
    if (!CollectionUtils.isEmpty(ids) && !CollectionUtils.isEmpty(entities)) {
      Map<String, R> entityMap = entities.stream()
          .collect(Collectors.toMap(e -> orderField.apply(e).toString(), retrieveField));

      return ids.stream()
          .map(id -> entityMap.get(id.toString()))
          .collect(Collectors.toList());
    }
    return Collections.emptyList();
  }
}