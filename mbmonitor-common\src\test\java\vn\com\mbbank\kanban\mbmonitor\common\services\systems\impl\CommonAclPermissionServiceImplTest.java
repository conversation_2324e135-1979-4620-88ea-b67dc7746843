package vn.com.mbbank.kanban.mbmonitor.common.services.systems.impl;

import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.acl.AclPermissionModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.SysRoleWithPermissionsModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SysUserResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysPermissionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysUserEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.systems.CommonUserService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 1/15/2025
 */
class CommonAclPermissionServiceImplTest {
  @Mock
  CommonUserService sysUserService;
  @InjectMocks
  CommonAclPermissionServiceImpl commonAclPermissionServiceImpl;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void isPermission_match_any_success() throws BusinessException {
    SysRoleWithPermissionsModel sysRoleWithPermissionsModel = new SysRoleWithPermissionsModel();
    SysPermissionEntity entity = new SysPermissionEntity();
    entity.setId(1L);
    entity.setAction(PermissionActionEnum.VIEW);
    entity.setModule(PermissionModuleEnum.DATABASE_COLLECT);
    sysRoleWithPermissionsModel.setPermissions(List.of(entity));
    sysRoleWithPermissionsModel.setActive(true);
    SysUserResponse response = new SysUserResponse();
    response.setRoles(List.of(sysRoleWithPermissionsModel));
    response.setIsActive(true);
    response.setIsAdmin(false);
    when(sysUserService.findOrCreateNew(anyString())).thenReturn(response);

    boolean result = commonAclPermissionServiceImpl.isAnyPermission(
        List.of(
            new AclPermissionModel(PermissionModuleEnum.DATABASE_COLLECT, PermissionActionEnum.VIEW,
                PermissionTypeEnum.MODULE, "1", "2")), true);
    Assertions.assertEquals(true, result);
  }

  @Test
  void isAnyPermission_success() throws BusinessException {
    SysRoleWithPermissionsModel sysRoleWithPermissionsModel = new SysRoleWithPermissionsModel();
    SysPermissionEntity entity = new SysPermissionEntity();
    entity.setId(1L);
    entity.setAction(PermissionActionEnum.VIEW);
    entity.setModule(PermissionModuleEnum.DATABASE_COLLECT);
    sysRoleWithPermissionsModel.setPermissions(List.of(entity));
    sysRoleWithPermissionsModel.setActive(true);
    SysUserResponse response = new SysUserResponse();
    response.setRoles(List.of(sysRoleWithPermissionsModel));
    response.setIsActive(true);
    response.setIsAdmin(false);
    when(sysUserService.findOrCreateNew(anyString())).thenReturn(response);

    boolean result = commonAclPermissionServiceImpl.isAnyPermission(
        List.of(
            new AclPermissionModel(PermissionModuleEnum.DATABASE_COLLECT, PermissionActionEnum.VIEW,
                PermissionTypeEnum.MODULE, "1", "1")));
    Assertions.assertEquals(true, result);
  }

  @Test
  void isAnyPermission_branch_permission_empty() throws BusinessException {
    SysRoleWithPermissionsModel sysRoleWithPermissionsModel = new SysRoleWithPermissionsModel();
    SysPermissionEntity entity = new SysPermissionEntity();
    entity.setId(1L);
    entity.setAction(PermissionActionEnum.VIEW);
    entity.setModule(PermissionModuleEnum.DATABASE_COLLECT);
    sysRoleWithPermissionsModel.setActive(true);
    SysUserResponse response = new SysUserResponse();
    response.setRoles(List.of(sysRoleWithPermissionsModel));
    response.setIsActive(true);
    response.setIsAdmin(false);
    when(sysUserService.findOrCreateNew(anyString())).thenReturn(response);

    boolean result = commonAclPermissionServiceImpl.isAnyPermission(
        List.of(
            new AclPermissionModel(PermissionModuleEnum.DATABASE_COLLECT, PermissionActionEnum.VIEW,
                PermissionTypeEnum.MODULE, "1", "1")));
    Assertions.assertEquals(false, result);
  }

  @Test
  void isAnyPermission_branch_userInfo_empty() throws BusinessException {
    when(sysUserService.findOrCreateNew(anyString())).thenReturn(null);

    boolean result = commonAclPermissionServiceImpl.isAnyPermission(
        List.of(
            new AclPermissionModel(PermissionModuleEnum.DATABASE_COLLECT, PermissionActionEnum.VIEW,
                PermissionTypeEnum.MODULE,"1", "2")));
    Assertions.assertEquals(false, result);
  }

  @Test
  void isPermission_role_empty() throws BusinessException {
    SysRoleWithPermissionsModel sysRoleWithPermissionsModel = new SysRoleWithPermissionsModel();
    SysPermissionEntity entity = new SysPermissionEntity();
    entity.setId(1L);
    entity.setAction(PermissionActionEnum.VIEW);
    entity.setModule(PermissionModuleEnum.DATABASE_COLLECT);
    sysRoleWithPermissionsModel.setPermissions(List.of(entity));
    sysRoleWithPermissionsModel.setActive(true);
    SysUserResponse response = new SysUserResponse();
    response.setIsActive(true);
    response.setIsAdmin(false);
    response.setRoles(new ArrayList<>());
    when(sysUserService.findOrCreateNew(anyString())).thenReturn(response);

    boolean result = commonAclPermissionServiceImpl.isAnyPermission(
        List.of(
            new AclPermissionModel(PermissionModuleEnum.DATABASE_COLLECT, PermissionActionEnum.VIEW,
                PermissionTypeEnum.MODULE, "1", "2")), true);
    Assertions.assertEquals(false, result);
  }

  @Test
  void isAnyPermission_user_inactive() throws BusinessException {
    SysUserResponse response = new SysUserResponse();
    response.setIsActive(false);
    response.setIsAdmin(false);
    when(sysUserService.findOrCreateNew(anyString())).thenReturn(response);

//    boolean result = commonAclPermissionServiceImpl.isAnyPermission(
//        List.of(
//            new AclPermissionModel(PermissionModuleEnum.DATABASE_COLLECT, PermissionActionEnum.VIEW,
//                PermissionTypeEnum.MODULE, "1", "1"), true);
//    Assertions.assertEquals(false, result);
  }

  @Test
  void isAnyPermission_user_admin() throws BusinessException {
    SysUserResponse response = new SysUserResponse();
    response.setIsActive(true);
    response.setIsAdmin(true);
    when(sysUserService.findOrCreateNew(anyString())).thenReturn(response);

    boolean result = commonAclPermissionServiceImpl.isAnyPermission(
        List.of(
            new AclPermissionModel(PermissionModuleEnum.DATABASE_COLLECT, PermissionActionEnum.VIEW,
                PermissionTypeEnum.MODULE, "1", "2")), true);
    Assertions.assertEquals(true, result);
  }

  @Test
  void isAnyPermission_sub_module_success() throws BusinessException {
    SysRoleWithPermissionsModel sysRoleWithPermissionsModel = new SysRoleWithPermissionsModel();
    SysPermissionEntity entity = new SysPermissionEntity();
    entity.setId(1L);
//    entity.setAction(PermissionActionEnum.EXECUTE_SQL);
//    entity.setModule(PermissionModuleEnum.MULTI_SQL_QUERY);
    entity.setType(PermissionTypeEnum.SUB_MODULE);
//    entity.setModuleId(1L);
//    entity.setModuleParentId(1L);
    sysRoleWithPermissionsModel.setPermissions(List.of(entity));
    sysRoleWithPermissionsModel.setActive(true);
    SysUserResponse response = new SysUserResponse();
    response.setRoles(List.of(sysRoleWithPermissionsModel));
    response.setIsActive(true);
    response.setIsAdmin(false);
    when(sysUserService.findOrCreateNew(anyString())).thenReturn(response);

//    boolean result = commonAclPermissionServiceImpl.isAnyPermission(
//        List.of(
//            new AclPermissionModel(PermissionModuleEnum.MULTI_SQL_QUERY,
//                PermissionActionEnum.EXECUTE_SQL,
//                PermissionTypeEnum.SUB_MODULE, Long.valueOf(1), Long.valueOf(1))), false);
//    Assertions.assertEquals(true, result);
  }

  @Test
  void isAnyPermission_sub_module_parent_has_permission() throws BusinessException {
    SysRoleWithPermissionsModel sysRoleWithPermissionsModel = new SysRoleWithPermissionsModel();
    SysPermissionEntity entity = new SysPermissionEntity();
    entity.setId(1L);
    entity.setType(PermissionTypeEnum.SUB_MODULE);
    sysRoleWithPermissionsModel.setPermissions(List.of(entity));
    sysRoleWithPermissionsModel.setActive(true);
    SysUserResponse response = new SysUserResponse();
    response.setRoles(List.of(sysRoleWithPermissionsModel));
    response.setIsActive(true);
    response.setIsAdmin(false);
    when(sysUserService.findOrCreateNew(anyString())).thenReturn(response);
  }

  @Test
  void isSupperAdmin_success() throws BusinessException {
    SysUserEntity userInfo = new SysUserEntity();
    userInfo.setIsActive(true);
    userInfo.setIsAdmin(true);
    when(sysUserService.findByUserName(anyString())).thenReturn(Optional.of(userInfo));
    boolean result = commonAclPermissionServiceImpl.isSupperAdmin();
    Assertions.assertEquals(true, result);
  }

  @Test
  void isSupperAdmin_user_inactive() throws BusinessException {
    SysUserEntity userInfo = new SysUserEntity();
    userInfo.setIsActive(false);
    userInfo.setIsAdmin(true);
    when(sysUserService.findByUserName(anyString())).thenReturn(Optional.of(userInfo));
    boolean result = commonAclPermissionServiceImpl.isSupperAdmin();
    Assertions.assertEquals(false, result);
  }

  @Test
  void isSupperAdmin_user_not_admin() throws BusinessException {
    SysUserEntity userInfo = new SysUserEntity();
    userInfo.setIsActive(true);
    userInfo.setIsAdmin(false);
    when(sysUserService.findByUserName(anyString())).thenReturn(Optional.of(userInfo));
    boolean result = commonAclPermissionServiceImpl.isSupperAdmin();
    Assertions.assertEquals(false, result);
  }

  @Test
  void isSupperAdmin_user_not_found() throws BusinessException {
    when(sysUserService.findByUserName(anyString())).thenReturn(Optional.empty());
    Assertions.assertThrows(BusinessException.class, () -> {
      commonAclPermissionServiceImpl.isSupperAdmin();
    });
  }

  @Test
  void isMatchPermission_SubModule_ParentIdNotEmpty_ParentIdNoMatch() {
    SysPermissionEntity entity = new SysPermissionEntity();
    entity.setType(PermissionTypeEnum.SUB_MODULE);
//    entity.setModuleParentId(1L);
    entity.setModule(PermissionModuleEnum.DATABASE_COLLECT);
    entity.setAction(PermissionActionEnum.VIEW);
//    entity.setModuleId(1L);

    AclPermissionModel aclModel = new AclPermissionModel();
    aclModel.setModule(PermissionModuleEnum.DATABASE_COLLECT);
    aclModel.setAction(PermissionActionEnum.VIEW);
    aclModel.setId("1");
    aclModel.setParentId("2"); // ParentId không khớp

    Assertions.assertFalse(commonAclPermissionServiceImpl.isMatchPermission(entity, aclModel));
  }

  @Test
  void isMatchPermission_SubModule_ActionNoMatch() {
    SysPermissionEntity entity = new SysPermissionEntity();
    entity.setType(PermissionTypeEnum.SUB_MODULE);
//    entity.setModuleParentId(1L);
    entity.setModule(PermissionModuleEnum.DATABASE_COLLECT);
    entity.setAction(PermissionActionEnum.VIEW);
//    entity.setModuleId(1L);

    AclPermissionModel aclModel = new AclPermissionModel();
    aclModel.setModule(PermissionModuleEnum.DATABASE_COLLECT);
    aclModel.setAction(PermissionActionEnum.EDIT); // Action không khớp
    aclModel.setId("1");
    aclModel.setParentId("2");

    Assertions.assertFalse(commonAclPermissionServiceImpl.isMatchPermission(entity, aclModel));
  }

  @Test
  void isMatchPermission_SubModule_ModuleNoMatch() {
    SysPermissionEntity entity = new SysPermissionEntity();
    entity.setType(PermissionTypeEnum.SUB_MODULE);
//    entity.setModuleParentId(1L);
    entity.setModule(PermissionModuleEnum.DATABASE_COLLECT);
    entity.setAction(PermissionActionEnum.VIEW);
//    entity.setModuleId(1L);

    AclPermissionModel aclModel = new AclPermissionModel();
    aclModel.setModule(PermissionModuleEnum.DATABASE_CONNECTION); // Module không khớp
    aclModel.setAction(PermissionActionEnum.VIEW);
    aclModel.setId("1");
    aclModel.setParentId("2");

    Assertions.assertFalse(commonAclPermissionServiceImpl.isMatchPermission(entity, aclModel));
  }

  @Test
  void isMatchPermission_NotSubModule_ActionNoMatch() {
    SysPermissionEntity entity = new SysPermissionEntity();
    entity.setType(PermissionTypeEnum.MODULE);
    entity.setModule(PermissionModuleEnum.DATABASE_COLLECT);
    entity.setAction(PermissionActionEnum.VIEW);

    AclPermissionModel aclModel = new AclPermissionModel();
    aclModel.setModule(PermissionModuleEnum.DATABASE_COLLECT);
    aclModel.setAction(PermissionActionEnum.EDIT); // Action không khớp

    Assertions.assertFalse(commonAclPermissionServiceImpl.isMatchPermission(entity, aclModel));
  }

  @Test
  void isMatchPermission_NotSubModule_ModuleNoMatch() {
    SysPermissionEntity entity = new SysPermissionEntity();
    entity.setType(PermissionTypeEnum.MODULE);
    entity.setModule(PermissionModuleEnum.DATABASE_COLLECT);
    entity.setAction(PermissionActionEnum.VIEW);

    AclPermissionModel aclModel = new AclPermissionModel();
    aclModel.setModule(PermissionModuleEnum.DATABASE_CONNECTION); // Module không khớp
    aclModel.setAction(PermissionActionEnum.VIEW);

    Assertions.assertFalse(commonAclPermissionServiceImpl.isMatchPermission(entity, aclModel));
  }

  @Test
  void isMatchPermission_SubModule_ParentIdEmpty_ParentIdNoMatch() {
    SysPermissionEntity entity = new SysPermissionEntity();
    entity.setType(PermissionTypeEnum.SUB_MODULE);
    entity.setModuleParentId(null); // ParentId rỗng (empty)
    entity.setModule(PermissionModuleEnum.DATABASE_COLLECT);
    entity.setAction(PermissionActionEnum.VIEW);
//    entity.setModuleId(1L); // ModuleId là 1

    AclPermissionModel aclModel = new AclPermissionModel();
    aclModel.setModule(PermissionModuleEnum.DATABASE_COLLECT); // Module khớp
    aclModel.setAction(PermissionActionEnum.VIEW); // Action khớp
    aclModel.setParentId("2"); // ParentId không khớp với ModuleId

    Assertions.assertFalse(commonAclPermissionServiceImpl.isMatchPermission(entity, aclModel));
  }

  @Test
  void isMatchPermission_SubModule_ParentIdEmpty_ModuleNoMatch() {
    SysPermissionEntity entity = new SysPermissionEntity();
    entity.setType(PermissionTypeEnum.SUB_MODULE);
    entity.setModuleParentId(null); // ParentId rỗng
    entity.setModule(PermissionModuleEnum.DATABASE_CONNECTION); // Module khác nhau
    entity.setAction(PermissionActionEnum.VIEW);
//    entity.setModuleId(1L);

    AclPermissionModel aclModel = new AclPermissionModel();
    aclModel.setModule(PermissionModuleEnum.DATABASE_COLLECT); // Module không khớp
    aclModel.setAction(PermissionActionEnum.VIEW); // Action khớp
    aclModel.setParentId("1"); // ParentId khớp với ModuleId

    Assertions.assertFalse(commonAclPermissionServiceImpl.isMatchPermission(entity, aclModel));
  }

  @Test
  void isMatchPermission_SubModule_ParentIdEmpty_ActionNoMatch() {
    SysPermissionEntity entity = new SysPermissionEntity();
    entity.setType(PermissionTypeEnum.SUB_MODULE);
    entity.setModuleParentId(null); // ParentId rỗng
    entity.setModule(PermissionModuleEnum.DATABASE_COLLECT);
    entity.setAction(PermissionActionEnum.EDIT); // Action khác nhau
//    entity.setModuleId(1L);

    AclPermissionModel aclModel = new AclPermissionModel();
    aclModel.setModule(PermissionModuleEnum.DATABASE_COLLECT); // Module khớp
    aclModel.setAction(PermissionActionEnum.VIEW); // Action không khớp
    aclModel.setParentId("1"); // ParentId khớp với ModuleId

    Assertions.assertFalse(commonAclPermissionServiceImpl.isMatchPermission(entity, aclModel));
  }

  @Test
  void isMatchPermission_SubModule_ModuleIdNoMatch() {
    SysPermissionEntity entity = new SysPermissionEntity();
    entity.setType(PermissionTypeEnum.SUB_MODULE);
//    entity.setModuleParentId(1L); // ParentId khớp
    entity.setModule(PermissionModuleEnum.DATABASE_COLLECT); // Module khớp
    entity.setAction(PermissionActionEnum.VIEW); // Action khớp
//    entity.setModuleId(1L); // ModuleId không khớp

    AclPermissionModel aclModel = new AclPermissionModel();
    aclModel.setModule(PermissionModuleEnum.DATABASE_COLLECT); // Module khớp
    aclModel.setAction(PermissionActionEnum.VIEW); // Action khớp
    aclModel.setId("2"); // ModuleId không khớp
    aclModel.setParentId("1"); // ParentId khớp

    Assertions.assertFalse(commonAclPermissionServiceImpl.isMatchPermission(entity, aclModel));
  }


}
