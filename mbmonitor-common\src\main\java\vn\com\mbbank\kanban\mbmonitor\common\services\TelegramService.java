package vn.com.mbbank.kanban.mbmonitor.common.services;

import java.util.List;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.telegram.TelegramUserInGroupModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.telegram.TelegramUserInfoModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.telegram.TelegramMessageTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/20/2025
 */
@Service
public interface TelegramService {
  /**
   * Send message to telegram with type html.
   *
   * @param botToken botToken
   * @param groupId  groupId
   * @param message  message
   * @return response data
   */
  public ResponseEntity<String> sendMessage(String botToken, String groupId, String message);

  /**
   * Send message to telegram.
   *
   * @param botToken botToken
   * @param groupId  Group chat id.
   * @param message  message
   * @param type     type
   * @return response data
   */
  public ResponseEntity<String> sendMessage(String botToken, String groupId, String message,
                                            TelegramMessageTypeEnum type);

  /**
   * Get bot info.
   *
   * @param botToken botToken
   * @return TelegramUserInfoModel
   */
  public TelegramUserInfoModel getMe(String botToken);

  /**
   * get user info in group.
   *
   * @param botToken botToken
   * @param groupId  group chat Id
   * @param userId   userId
   * @return TelegramUserInGroupModel
   */
  public TelegramUserInGroupModel getChatMember(String botToken, String groupId, String userId);

  /**
   * Push kafka send message to telegram.
   *
   * @param alerts alerts
   */
  void sendAlertKafka(List<AlertEntity> alerts);

}
