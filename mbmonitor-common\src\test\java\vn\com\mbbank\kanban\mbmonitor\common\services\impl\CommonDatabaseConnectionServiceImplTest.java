package vn.com.mbbank.kanban.mbmonitor.common.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.zaxxer.hikari.HikariConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.configs.QueryHikariDataSourceConfig;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.DatabaseConnectionRequest;
import vn.com.mbbank.kanban.mbmonitor.common.enums.OracleDatabaseConnectType;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/21/2025
 */
class CommonDatabaseConnectionServiceImplTest {
//  @Mock
//  QueryHikariDataSourceConfig queryHikariDataSourceConfig;
//  @InjectMocks
//  CommonDatabaseConnectionServiceImpl commonDatabaseConnectionServiceImpl;
//
//  private DatabaseConnectionRequest mockRequest;
//  private HikariConfig mockConfig;
//
//  @BeforeEach
//  void setUp() {
//    MockitoAnnotations.openMocks(this);
//    mockRequest = new DatabaseConnectionRequest();
//    mockConfig = new HikariConfig();
//    mockConfig.setJdbcUrl("jdbc:h2:mem:testdb");
//  }

//  @Test
//  void testConnection_success() throws BusinessException {
//    when(queryHikariDataSourceConfig.getDataSource(mockConfig.getJdbcUrl())).thenReturn(
//        null);
//
//    boolean result = commonDatabaseConnectionServiceImpl.testConnection(mockRequest);
//
//    assertTrue(result);
//  }
//
//  @Test
//  void testConnection_false() throws BusinessException {
//    when(queryHikariDataSourceConfig.getDataSource(any())).thenThrow(
//        new RuntimeException());
//    boolean result = commonDatabaseConnectionServiceImpl.testConnection(mockRequest);
//    assertFalse(result);
//  }
//
//  @Test
//  void createHikariConfig_WithSID() {
//    // Arrange
//    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
//    request.setUserName("testUser");
//    request.setPassword("testPass");
//    request.setHost("localhost");
//    request.setPort(1521);
//    request.setSid("ORCL");
//    request.setOracleConnectType(OracleDatabaseConnectType.SID);
//    var config = commonDatabaseConnectionServiceImpl.createHikariConfig(request);
//
//
//    // Assert
//    assertNotNull(config);
//    assertEquals("******************************************************", config.getJdbcUrl());
//    assertEquals("testUser", config.getUsername());
//    assertEquals("testPass", config.getPassword());
//    assertEquals("oracle.jdbc.OracleDriver", config.getDriverClassName());
//  }
//
//  @Test
//  void createHikariConfig_WithServiceName() {
//    // Arrange
//    DatabaseConnectionRequest request = new DatabaseConnectionRequest();
//    request.setUserName("testUser");
//    request.setPassword("testPass");
//    request.setHost("localhost");
//    request.setPort(1521);
//    request.setServiceName("XE");
//    request.setOracleConnectType(OracleDatabaseConnectType.SERVICE_NAME);
//
//    var config = commonDatabaseConnectionServiceImpl.createHikariConfig(request);
//    // Assert
//    assertNotNull(config);
//    assertEquals("******************************************************", config.getJdbcUrl());
//    assertEquals("testUser", config.getUsername());
//    assertEquals("testPass", config.getPassword());
//    assertEquals("oracle.jdbc.OracleDriver", config.getDriverClassName());
//  }

}
