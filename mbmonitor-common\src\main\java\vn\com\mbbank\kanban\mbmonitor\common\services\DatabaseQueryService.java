package vn.com.mbbank.kanban.mbmonitor.common.services;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SqlExecutionResponse;

/**
 * Services for Superiors.
 */
public interface DatabaseQueryService {

  /**
   * Execute query by Connection and query.
   *
   * @param conn  conn
   * @param query query
   * @return SqlExecutionResponse
   * @throws BusinessException ex
   * @throws SQLException      ex
   */
  SqlExecutionResponse executeQuery(Connection conn, String query)
      throws BusinessException, SQLException;

  /**
   * execute sql.
   *
   * @param conn     conn
   * @param query    query
   * @param isHikari isHikari
   * @return SqlExecutionResponse
   * @throws BusinessException ex
   * @throws SQLException      ex
   */
  SqlExecutionResponse executeQuery(Connection conn, String query, Boolean isHikari)
      throws BusinessException, SQLException;

  /**
   * execute sql.
   *
   * @param conn     conn
   * @param query    query
   * @param isHikari isHikari
   * @param maxRow   maxRow
   * @return SqlExecutionResponse
   * @throws BusinessException ex
   * @throws SQLException      ex
   */
  SqlExecutionResponse executeQuery(Connection conn, String query, Boolean isHikari, Integer maxRow)
      throws BusinessException, SQLException;

  /**
   * execute sql.
   *
   * @param conn     conn
   * @param query    query
   * @param isHikari query
   * @param maxRow   maxRow
   * @param map      map
   * @return SqlExecutionResponse
   * @throws BusinessException ex
   * @throws SQLException      ex
   */
  SqlExecutionResponse executeQuery(Connection conn, String query, Boolean isHikari, Integer maxRow,
                                    Map<String, ?> map)
      throws BusinessException, SQLException;

}
