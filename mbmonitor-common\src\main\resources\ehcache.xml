<?xml version="1.0" encoding="UTF-8"?>
<ehcache xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="http://www.ehcache.org/ehcache.xsd" updateCheck="true" monitoring="autodetect"
         dynamicConfig="true">
    <cache name="users" maxEntriesLocalHeap="500" eternal="false" timeToIdleSeconds="300" timeToLiveSeconds="600"
           diskExpiryThreadIntervalSeconds="1" copyOnRead="true" copyOnWrite="true">
        <copyStrategy class="net.sf.ehcache.store.compound.ReadWriteSerializationCopyStrategy"/>
    </cache>
</ehcache>