package vn.com.mbbank.kanban.mbmonitor.common.entities;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/16/2024
 */
@Entity
@Data
@Table(name = TableName.EMAIL_PARTNER_ADDRESS)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class EmailPartnerAddressEntity extends BaseEntity<Long> {
  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "EMAIL_PARTNER_ADDRESS_SEQ", sequenceName = "EMAIL_PARTNER_ADDRESS_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "EMAIL_PARTNER_ADDRESS_SEQ")
  private Long id;

  @Column(name = "ADDRESS")
  private String address;

  @Column(name = "EMAIL_PARTNER_ID")
  private Long emailPartnerId;

}
