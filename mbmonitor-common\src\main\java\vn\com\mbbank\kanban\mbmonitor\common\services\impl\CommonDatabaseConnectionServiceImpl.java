package vn.com.mbbank.kanban.mbmonitor.common.services.impl;

import com.zaxxer.hikari.HikariConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.configs.QueryHikariDataSourceConfig;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.DatabaseConnectionRequest;
import vn.com.mbbank.kanban.mbmonitor.common.enums.OracleDatabaseConnectType;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonDatabaseConnectionService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/20/2025
 */
@Service
public class CommonDatabaseConnectionServiceImpl implements CommonDatabaseConnectionService {
  @Autowired
  QueryHikariDataSourceConfig queryHikariDataSourceConfig;

  @Override
  public boolean testConnection(DatabaseConnectionRequest connectInfo) throws BusinessException {
    HikariConfig dbConfig = createHikariConfig(connectInfo);
    dbConfig.setInitializationFailTimeout(1000);
    try {
      queryHikariDataSourceConfig.getDataSource(dbConfig.getJdbcUrl());
      return true;
    } catch (RuntimeException ex) {
      return false;
    }
  }

  /**
   * create Hikari info.
   *
   * @param connectInfo connectInfo
   * @return HikariConfig
   */
  @Override
  public HikariConfig createHikariConfig(DatabaseConnectionRequest connectInfo) {
    String url;
    String driverClass = switch (connectInfo.getType()) {
      case ORACLE -> {
        if (OracleDatabaseConnectType.SID.equals(connectInfo.getOracleConnectType())) {
          url = String.format("jdbc:oracle:thin:**/**@**:**:**",
              connectInfo.getUserName(),
              connectInfo.getPassword(),
              connectInfo.getHost(),
              connectInfo.getPort(),
              connectInfo.getSid());
        } else {
          url = String.format("jdbc:oracle:thin:**/**@//**:**/**",
              connectInfo.getUserName(),
              connectInfo.getPassword(),
              connectInfo.getHost(),
              connectInfo.getPort(),
              connectInfo.getServiceName());
        }
        yield "oracle.jdbc.OracleDriver";
      }
      case Microsoft_SQL -> {
        url = String.format(
            "jdbc:sqlserver://**:**;databaseName=**;user=**;password=**;encrypt=true;trustServerCertificate=true",
            connectInfo.getHost(),
            connectInfo.getPort(),
            connectInfo.getDatabaseName(),
            connectInfo.getUserName(),
            connectInfo.getPassword()
        );
        yield "com.microsoft.sqlserver.jdbc.SQLServerDriver";
      }
    };

    HikariConfig config = new HikariConfig();
    config.setJdbcUrl(url);
    config.setUsername(connectInfo.getUserName());
    config.setPassword(connectInfo.getPassword());
    config.setDriverClassName(driverClass);
    return config;
  }
}
