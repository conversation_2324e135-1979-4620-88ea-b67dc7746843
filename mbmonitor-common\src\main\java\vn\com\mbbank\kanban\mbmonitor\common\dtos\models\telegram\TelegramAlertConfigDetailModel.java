package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.telegram;

import lombok.Data;
import org.springframework.data.domain.Page;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TelegramAlertConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TelegramConfigEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/6/2025
 */
@Data
public class TelegramAlertConfigDetailModel {
  private TelegramConfigEntity config;
  private Page<TelegramAlertConfigEntity> alertsConfig;
}
