package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import vn.com.mbbank.kanban.core.entities.core.BaseSoftEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;

@Data
@Entity
@Table(name = TableName.APPLICATION)
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationEntity extends BaseSoftEntity<String> {

  @Id
  @Column(name = "ID")
  private String id;

  @Column(name = "NAME", nullable = false)
  private String name;

  @Column(name = "SERVICE_ID", nullable = false)
  private String serviceId;

  @Column(name = "DESCRIPTION")
  private String description;

  @Override
  public String getId() {
    return id;
  }
}
