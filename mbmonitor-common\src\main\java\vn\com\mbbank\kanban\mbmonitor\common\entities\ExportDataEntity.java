package vn.com.mbbank.kanban.mbmonitor.common.entities;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExportFileStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExportFileTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;

@Data
@Entity
@Table(name = "EXPORT_DATA")
@EqualsAndHashCode(callSuper = true)
public class ExportDataEntity extends BaseEntity<String> {

  @Id
  @Column(name = "ID")
  private String id = GeneratorUtil.generateId();

  @Column(name = "FILE_NAME", nullable = false)
  private String fileName;

  @Column(name = "STATUS")
  @Enumerated(EnumType.STRING)
  private ExportFileStatusEnum status;

  @Column(name = "EXTENSION", nullable = false)
  @Enumerated(EnumType.STRING)
  private ExportFileTypeEnum extension;

  @Column(name = "FILE_STORAGE_ID")
  private Long fileStorageId;

  @Column(name = "EXPORTED_BY")
  private String exportedBy;


  @Override
  public String getId() {
    return id;
  }
}