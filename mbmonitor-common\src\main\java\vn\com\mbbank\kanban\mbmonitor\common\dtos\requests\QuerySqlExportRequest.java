package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;


import jakarta.validation.Valid;
import java.util.Map;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportDataModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExportFileTypeEnum;

/**
 * Request view pagination and name search.
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class QuerySqlExportRequest {
  Long id;
  Map<String, Object> params;
  int numberOfResults = CommonConstants.MAX_QUERY_SQL_EXPORT_ROWS_LIMIT;
  ExportFileTypeEnum type;
  @Valid
  private ExportDataModel exportDataModel;
}
