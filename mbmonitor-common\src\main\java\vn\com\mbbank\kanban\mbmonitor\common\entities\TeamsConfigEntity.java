package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.KanbanAutoGenerateUlId;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TeamsConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TeamsIntervalTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/16/2025
 */
@KanbanAutoGenerateUlId
@Entity
@Table(name = "TEAMS_CONFIG")
@Data
public class TeamsConfigEntity extends BaseEntity<String> {

  @Id
  @Column(name = "ID")
  private String id = GeneratorUtil.generateId();

  @Column(name = "TENANT_ID")
  private String tenantId;

  @Column(name = "CLIENT_ID")
  private String clientId;

  @Column(name = "CLIENT_SECRET")
  private String clientSecret;

  @Column(name = "EMAIL")
  private String email;

  @Column(name = "PASSWORD")
  private String password;

  @Enumerated(EnumType.STRING)
  @Column(name = "TYPE")
  private TeamsConfigTypeEnum type;

  @Column(name = "MESSAGE_TEMPLATE")
  private String messageTemplate;

  @Column(name = "INTERVAL")
  private String interval;

  @Enumerated(EnumType.STRING)
  @Column(name = "INTERVAL_TYPE")
  private TeamsIntervalTypeEnum intervalType;

  @Column(name = "DESCRIPTION")
  private String description;
}