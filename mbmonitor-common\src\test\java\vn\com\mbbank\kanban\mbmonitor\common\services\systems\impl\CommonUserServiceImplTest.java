package vn.com.mbbank.kanban.mbmonitor.common.services.systems.impl;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SysUserResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseCollectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.ServerIntegration;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonRedisService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 1/15/2025
 */
class CommonUserServiceImplTest {
  @Mock
  ServerIntegration serverIntegration;
  @Mock
  CommonRedisService commonRedisService;

  @InjectMocks
  CommonUserServiceImpl commonUserServiceImpl;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void findOrCreateNew_redis_exists() {
    SysUserResponse response = new SysUserResponse();
    response.setId(1L);
    when(commonRedisService.isExistsByKey(any())).thenReturn(true);
    when(commonRedisService.get(any(), any(), any())).thenReturn(response);
    SysUserResponse result = commonUserServiceImpl.findOrCreateNew("userName");
    Assertions.assertEquals(1L,
        result.getId());
  }

  @Test
  void findOrCreateNew_redis_exists_branch_not_user_entity() {
    SysUserResponse responseEntity = new SysUserResponse();
    responseEntity.setId(1L);
    ResponseData responseData = new ResponseData();
    responseData.setData(responseEntity);
    when(serverIntegration.getMe()).thenReturn(responseData);

    DatabaseCollectEntity response = new DatabaseCollectEntity();
    response.setId(1L);
    when(commonRedisService.isExistsByKey(any())).thenReturn(true);
    when(commonRedisService.get(any(), any(), any())).thenReturn(null);
    SysUserResponse result = commonUserServiceImpl.findOrCreateNew("userName");
    Assertions.assertEquals(1L,
        result.getId());
  }

  @Test
  void findOrCreateNew_call_api() {
    SysUserResponse response = new SysUserResponse();
    response.setId(1L);
    when(commonRedisService.isExistsByKey(any())).thenReturn(false);
    ResponseData responseData = new ResponseData();
    responseData.setData(response);
    when(serverIntegration.getMe()).thenReturn(responseData);
    var result = commonUserServiceImpl.findByUserName("userName");
    Assertions.assertEquals(1L,
        result.get().getId());
  }


}
