package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ConfigCollectMapTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DatabaseCollectLogModel {

  /**
   * Information of Author.
   *
   * <AUTHOR>
   * @created_date 3/3/2025
   */
  @Getter
  @Setter
  @AllArgsConstructor
  @NoArgsConstructor
  @Builder
  public static class CustomOutput {
    private ConfigCollectMapTypeEnum serviceNameType;
    private String customService;
    private String fromSourceService;
    private ConfigCollectMapTypeEnum applicationNameType;
    private String customApplication;
    private String fromSourceApplication;
    private String fromSourceContent;
    private ConfigCollectMapTypeEnum priorityType;
    private String customPriority;
    private String fromSourcePriority;
    private ConfigCollectMapTypeEnum contactType;
    private String fromSourceContact;
    private String customContact;
  }

  /**
   * Information of Author.
   *
   * <AUTHOR>
   * @created_date 3/3/2025
   */
  @Getter
  @Setter
  @AllArgsConstructor
  @NoArgsConstructor
  @Builder
  public static class Connection {
    private String connection;
    private String sqlCommand;
    private String createdDateField;
    private String alertIdField;
    private Long interval;
  }

  private String name;
  private String description;
  private Connection connect;
  private CustomOutput output;
  private boolean active;
}
