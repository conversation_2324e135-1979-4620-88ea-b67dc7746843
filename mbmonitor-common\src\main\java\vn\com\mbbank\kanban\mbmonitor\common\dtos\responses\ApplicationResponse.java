package vn.com.mbbank.kanban.mbmonitor.common.dtos.responses;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * Model view application response.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ApplicationResponse {
  String id;
  String serviceName;
  String description;
  String serviceId;
  String name;
  String createdDate;
  boolean deleted;
}

