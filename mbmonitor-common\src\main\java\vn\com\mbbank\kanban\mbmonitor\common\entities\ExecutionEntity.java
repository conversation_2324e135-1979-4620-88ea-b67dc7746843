package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;

@Entity
@Data
@Table(name = TableName.EXECUTION)
@EqualsAndHashCode(callSuper = true)
public class ExecutionEntity extends BaseEntity<String> {

  @Id
  @Column(name = "ID")
  private String id = GeneratorUtil.generateId();

  @Column(name = "NAME")
  private String name;

  @Column(name = "DESCRIPTION")
  private String description;

  @Column(name = "TYPE")
  @Enumerated(EnumType.STRING)
  private ExecutionTypeEnum type;

  @Column(name = "DATABASE_CONNECTION_ID")
  private Long databaseConnectionId;

  @Column(name = "EXECUTION_GROUP_ID")
  private String executionGroupId;

  @Column(name = "SCRIPT")
  private String script;

  @Override
  public String getId() {
    return id;
  }
}
