package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationTypeEnum;

/**
 * NotificationRequest.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SearchNotificationRequest {
  String title;
  String content;
  NotificationTypeEnum type;
  String userName;
  String sourceId;
  NotificationSourceTypeEnum sourceType;
}
