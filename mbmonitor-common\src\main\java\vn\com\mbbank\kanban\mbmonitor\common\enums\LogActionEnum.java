package vn.com.mbbank.kanban.mbmonitor.common.enums;

import com.alibaba.fastjson2.JSON;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.SysLogMessageModel;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 25/02/2024
 */
@Getter
@AllArgsConstructor
public enum LogActionEnum {
  //  CUSTOM_OBJECT
  CREATE_CUSTOM_OBJECT(LogFunctionEnum.CUSTOM_OBJECT, "Create Custom Object name {0} successful with info: {1}"),
  EDIT_CUSTOM_OBJECT(LogFunctionEnum.CUSTOM_OBJECT, "Edit Custom Object name {0} with info: {1}"),
  DELETE_CUSTOM_OBJECT(LogFunctionEnum.CUSTOM_OBJECT, "Delete Custom Object name {0} successful"),

  // USER_MANAGEMENT
  CREATE_USER(LogFunctionEnum.USER_MANAGEMENT, "Create Username {0} successful with info: {1}"),
  INACTIVE_USER(LogFunctionEnum.USER_MANAGEMENT, "Inactive Username {0} successful"),
  ACTIVE_USER(LogFunctionEnum.USER_MANAGEMENT, "Active Username {0} successful"),
  SET_ADMIN(LogFunctionEnum.USER_MANAGEMENT, "Set admin for Username {0} successful"),
  UNSET_ADMIN(LogFunctionEnum.USER_MANAGEMENT, "Unset admin for Username {0} successful"),
  EDIT_USER(LogFunctionEnum.USER_MANAGEMENT, "Edit Username {0} with info: {1}"),
  DELETE_USER(LogFunctionEnum.USER_MANAGEMENT, "Delete Username {0} successful"),
  UPDATE_USER_ROLE(LogFunctionEnum.USER_MANAGEMENT, "Update user name {0} successfully {1}"),

  //ROLE_MANAGEMENT
  CREATE_ROLE(LogFunctionEnum.ROLE_MANAGEMENT, "Create Role {0} successful with info: {1}"),
  EDIT_ROLE(LogFunctionEnum.ROLE_MANAGEMENT, "Edit Role {0} successful with info: {1}"),
  INACTIVE_ROLE(LogFunctionEnum.ROLE_MANAGEMENT, "Inactive Role {0} successful"),
  ACTIVE_ROLE(LogFunctionEnum.ROLE_MANAGEMENT, "Active Role {0} successful"),
  DELETE_ROLE(LogFunctionEnum.ROLE_MANAGEMENT, "Delete Role {0} successful"),

  // INPUT_DATA
  CREATE_WEBHOOK_COLLECT(LogFunctionEnum.INPUT_DATA,
      "Create config Webhook Collect name {0} successful with info: {1}"),
  EDIT_WEBHOOK_COLLECT(LogFunctionEnum.INPUT_DATA, "Edit config Webhook Collect name {0} successful with info: {1}"),
  DELETE_WEBHOOK_COLLECT(LogFunctionEnum.INPUT_DATA, "Delete config Webhook Collect name {0} successful"),
  CREATE_DATABASE_COLLECT(LogFunctionEnum.INPUT_DATA,
      "Create config Database Collect name {0} successful with info: {1}"),
  EDIT_DATABASE_COLLECT(LogFunctionEnum.INPUT_DATA, "Edit config Database Collect name {0} successful with info: {1}"),
  DELETE_DATABASE_COLLECT(LogFunctionEnum.INPUT_DATA, "Delete config Database Collect name {0} successful"),
  ACTIVE_DATABASE_COLLECT(LogFunctionEnum.INPUT_DATA, "Active config Database Collect name {0} successful"),
  INACTIVE_DATABASE_COLLECT(LogFunctionEnum.INPUT_DATA, "Inactive config Database Collect name {0} successful"),
  CREATE_EMAIL_COLLECT(LogFunctionEnum.INPUT_DATA, "Create config Email Collect name {0} successful with info: {1}"),
  EDIT_EMAIL_COLLECT(LogFunctionEnum.INPUT_DATA, "Edit config Email Collect name {0} successful with info: {1}"),
  DELETE_EMAIL_COLLECT(LogFunctionEnum.INPUT_DATA, "Delete config Email Collect name {0} successful"),
  ACTIVE_EMAIL_COLLECT(LogFunctionEnum.INPUT_DATA, "Active config Email Collect name {0} successful"),
  INACTIVE_EMAIL_COLLECT(LogFunctionEnum.INPUT_DATA, "Inactive config Email Collect name {0} successful"),

  // SERVICE_MANAGER
  CREATE_SERVICE(LogFunctionEnum.SERVICE_MANAGEMENT, "Create Service name {0} successful with info: {1}"),
  DELETE_SERVICE(LogFunctionEnum.SERVICE_MANAGEMENT, "Delete service name {0} successful"),
  EDIT_SERVICE(LogFunctionEnum.SERVICE_MANAGEMENT, "Edit Service name {0} successful with info: {1}"),

  // APPLICATION_MANAGER
  CREATE_APPLICATION(LogFunctionEnum.APPLICATION_MANAGEMENT, "Create Application name {0} successful with info: {1}"),
  DELETE_APPLICATION(LogFunctionEnum.APPLICATION_MANAGEMENT, "Delete Application name {0} successful"),
  EDIT_APPLICATION(LogFunctionEnum.APPLICATION_MANAGEMENT, "Edit Application name {0} successful with info: {1}"),

  // PRIORITY_CONFIG
  CREATE_PRIORITY_CONFIG(LogFunctionEnum.PRIORITY_CONFIG, "Create priority name {0} successful with info: {1}"),
  EDIT_PRIORITY_CONFIG(LogFunctionEnum.PRIORITY_CONFIG, "Edit priority name {1} successful with info: {1}"),
  DELETE_PRIORITY_CONFIG(LogFunctionEnum.PRIORITY_CONFIG, "Delete priority name {0} successful"),
  CHANGE_ORDER_OF_PRIORITY_CONFIG(LogFunctionEnum.PRIORITY_CONFIG,
      "Change order priority name {0} successful from index {1} to index {2}"),

  // ALERT_GROUP_CONFIG
  CREATE_ALERT_GROUP_CONFIG(LogFunctionEnum.GROUP_ALERT_CONFIG,
      "Create alert group config name {0} successful with info: {1}"),
  EDIT_ALERT_GROUP_CONFIG(LogFunctionEnum.GROUP_ALERT_CONFIG,
      "Edit alert group config name {0} successful with info: {1}"),
  DELETE_ALERT_GROUP_CONFIG(LogFunctionEnum.GROUP_ALERT_CONFIG, "Delete alert group config name {0} successful"),
  CHANGE_ORDER_OF_ALERT_GROUP_CONFIG(LogFunctionEnum.GROUP_ALERT_CONFIG,
      "Change order alert group config name {0} successful from index {1} to index {2}"),
  ACTIVE_ALERT_GROUP_CONFIG(LogFunctionEnum.GROUP_ALERT_CONFIG, "Active alert group config name {0} successful"),
  INACTIVE_ALERT_GROUP_CONFIG(LogFunctionEnum.GROUP_ALERT_CONFIG, "Inactive alert group config name {0} successful"),

  // MAINTENANCE_TIME_CONFIG
  CREATE_MAINTENANCE_TIME_CONFIG(LogFunctionEnum.MAINTENANCE_TIME_CONFIG,
      "Create Maintenance Time name {0} successful with info: {1}"),
  EDIT_MAINTENANCE_TIME_CONFIG(LogFunctionEnum.MAINTENANCE_TIME_CONFIG,
      "Edit Maintenance Time name {0} successful with info: {1}"),
  DELETE_MAINTENANCE_TIME_CONFIG(LogFunctionEnum.MAINTENANCE_TIME_CONFIG,
      "Delete Maintenance Time name {0}"),
  ACTIVE_MAINTENANCE_TIME_CONFIG(LogFunctionEnum.MAINTENANCE_TIME_CONFIG,
      "Active Maintenance Time name {0}"),
  INACTIVE_MAINTENANCE_TIME_CONFIG(LogFunctionEnum.MAINTENANCE_TIME_CONFIG,
      "Inactive Maintenance Time name {0}"),

  // FILTER_CONFIG
  CREATE_FILTER_CONFIG(LogFunctionEnum.FILTER_ALERT,
      "Create Filter Alert Config name {0} with info: {1}"),
  EDIT_FILTER_CONFIG(LogFunctionEnum.FILTER_ALERT,
      "Edit Filter Alert Config name {0} with info: {1}"),
  DELETE_FILTER_CONFIG(LogFunctionEnum.FILTER_ALERT,
      "Delete Filter Alert Config name {0} successful"),
  ACTIVE_FILTER_CONFIG(LogFunctionEnum.FILTER_ALERT,
      "Active Filter Alert Config name {0} successful"),
  INACTIVE_FILTER_CONFIG(LogFunctionEnum.FILTER_ALERT,
      "Inactive Filter Alert Config name {0} successful"),

  // EMAIL_CONFIG
  CREATE_PARTNER(LogFunctionEnum.EMAIL_CONFIG, "Create partner name {0} with info: {1}"),
  EDIT_PARTNER(LogFunctionEnum.EMAIL_CONFIG, "Edit partner name {0} with info: {1}"),
  DELETE_PARTNER(LogFunctionEnum.EMAIL_CONFIG, "Delete partner name {0} successful"),
  CREATE_TEMPLATE(LogFunctionEnum.EMAIL_CONFIG, "Create email template name {0} successful"),
  EDIT_TEMPLATE(LogFunctionEnum.EMAIL_CONFIG, "Edit email template name {0} successful with info"),
  DELETE_TEMPLATE(LogFunctionEnum.EMAIL_CONFIG, "Delete email template name {0} successful"),
  CREATE_EMAIL_CONNECTION(LogFunctionEnum.EMAIL_CONFIG, "Create email connection {0} successful with info: {0}"),
  EDIT_EMAIL_CONNECTION(LogFunctionEnum.EMAIL_CONFIG, "Edit email connection {0} successful with info"),
  ACTIVE_EMAIL_CONNECTION(LogFunctionEnum.EMAIL_CONFIG, "Active email connection {0} successful"),
  INACTIVE_EMAIL_CONNECTION(LogFunctionEnum.EMAIL_CONFIG, "Inactive email connection {0} successful"),
  DELETE_EMAIL_CONNECTION(LogFunctionEnum.EMAIL_CONFIG, "Delete email connection {0} successful"),

  // DATABASE_CONFIG
  CREATE_DATABASE_CONNECT(LogFunctionEnum.DATABASE_CONFIG,
      "Create Database Connection with host {0} successful with info: {1}"),
  EDIT_DATABASE_CONNECT(LogFunctionEnum.DATABASE_CONFIG,
      "Edit Database Connection with host {0} successful with info: {1}"),
  ACTIVE_DATABASE_CONNECT(LogFunctionEnum.DATABASE_CONFIG,
      "Active Database Connection with host {0} successful"),
  INACTIVE_DATABASE_CONNECT(LogFunctionEnum.DATABASE_CONFIG,
      "Inactive Database Connection with host {0} successful"),
  DELETE_DATABASE_CONNECT(LogFunctionEnum.DATABASE_CONFIG,
      "Delete Database Connection with host {0} successful"),
  CREATE_GROUP_QUERY(LogFunctionEnum.DATABASE_CONFIG,
      "Create group query name {0} successful with info: {1}"),
  EDIT_GROUP_QUERY(LogFunctionEnum.DATABASE_CONFIG,
      "Edit group query name {0} successful with info: {1}"),
  DELETE_GROUP_QUERY(LogFunctionEnum.DATABASE_CONFIG,
      "Delete group query name {0} successful"),
  CREATE_QUERY(LogFunctionEnum.DATABASE_CONFIG,
      "Create query name {0} successful with info: {1}"),
  EDIT_QUERY(LogFunctionEnum.DATABASE_CONFIG,
      "Edit query name {0} successful with info: {1}"),
  DELETE_QUERY(LogFunctionEnum.DATABASE_CONFIG,
      "Delete query name {0} successful"),
  CREATE_DATABASE_THRESHOLD(LogFunctionEnum.DATABASE_CONFIG, "Create config database threshold name {0}"
    +  " successful with info: {1}"),
  EDIT_DATABASE_THRESHOLD(LogFunctionEnum.DATABASE_CONFIG, "Edit config database threshold name {0}"
    +  " successful with info: {1}"),
  DELETE_DATABASE_THRESHOLD(LogFunctionEnum.DATABASE_CONFIG, "Delete config database threshold name {0} successful"),
  ACTIVE_DATABASE_THRESHOLD(LogFunctionEnum.DATABASE_CONFIG, "Active config database threshold name {0} successful"),
  INACTIVE_DATABASE_THRESHOLD(LogFunctionEnum.DATABASE_CONFIG,
      "Inactive config database threshold name {0} successful"),
  
  // MONITOR_WEB_CONFIG
  CREATE_MONITOR_WEB_CONFIG(LogFunctionEnum.MONITOR_WEB_CONFIG,
    "Create config monitor web name {0} successful with info: {1}"),
  EDIT_MONITOR_WEB_CONFIG(LogFunctionEnum.MONITOR_WEB_CONFIG,
    "Edit config monitor web name {0} successful with info: {1}"),
  DELETE_MONITOR_WEB_CONFIG(LogFunctionEnum.MONITOR_WEB_CONFIG, "Delete config monitor web name {0} successful"),
  ACTIVE_MONITOR_WEB_CONFIG(LogFunctionEnum.MONITOR_WEB_CONFIG, "Active config monitor web name {0} successful"),
  INACTIVE_MONITOR_WEB_CONFIG(LogFunctionEnum.MONITOR_WEB_CONFIG, "Inactive config monitor web name {0} successful"),
  SUCCESS_MONITOR_WEB_JOB(LogFunctionEnum.MONITOR_WEB_JOB, "All actions of {0} completed successfully."),
  LAST_ERROR_MONITOR_WEB_JOB(LogFunctionEnum.MONITOR_WEB_JOB,
    "RPA config '{0}' permanently failed at action: {1}. Last error: {2}"),
  ATTEMPT_ERROR_MONITOR_WEB_JOB(LogFunctionEnum.MONITOR_WEB_JOB,
    "RPA config '{0}' failed at action: {1}. Retry attempt #{2} failed. Error: {3}"),
  FIRST_ERROR_MONITOR_WEB_JOB(LogFunctionEnum.MONITOR_WEB_JOB,
    "RPA config '{0}' failed at action: {1}. This is the first error encountered. Error: {2}"),

  // SYSTEM
  DENY_ALERT_BY_WRONG_INFORMATION(LogFunctionEnum.SYSTEM, "Alert with info: {0} denied due to: {1}"),
  DENY_ALERT_BY_FILTER_ALERT(LogFunctionEnum.SYSTEM, "Alert with info: {0} denied due to filter alert config name {0}"),
  MODIFY_ALERT(LogFunctionEnum.SYSTEM, "Alert modified from {0} to {1} by config name {2}"),
  SEND_TELEGRAM_FAILED(LogFunctionEnum.SYSTEM, "Send Telegram failed to groupChatId {0} with message {1} due to: {2}"),
  DATABASE_THRESHOLD_ERROR(LogFunctionEnum.SYSTEM, "Database threshold name {0} get error {1}"),

  //TRIGGER
  DENY_TRIGGER(LogFunctionEnum.TRIGGER,
          "The auto-trigger action configuration {0}"
                  + " failed due to missing input parameter in execution name: {1}"),
  ERROR_TRIGGER(LogFunctionEnum.TRIGGER,
          "Configure auto trigger action {0} error when running execution {1}: {2}"),
  CREATE_AUTO_TRIGGER_EXECUTION(LogFunctionEnum.TRIGGER,
          "Create auto trigger execution name {0} successful with info: {1}"),
  EDIT_AUTO_TRIGGER_EXECUTION(LogFunctionEnum.TRIGGER,
          "Edit auto trigger execution name {0} successful with info: {1}"),
  INACTIVE_AUTO_TRIGGER_EXECUTION(LogFunctionEnum.TRIGGER, "Inactive auto trigger execution name {0} successful"),
  ACTIVE_AUTO_TRIGGER_EXECUTION(LogFunctionEnum.TRIGGER, "Active auto trigger execution name {0} successful"),
  DELETE_AUTO_TRIGGER_EXECUTION(LogFunctionEnum.TRIGGER, "Delete auto trigger execution name {0} successful");

  /**
   * format message template to string with parameter.
   *
   * @param messageParams messageParam
   * @return message
   */
  public String formatMessage(Object... messageParams) {
    return JSON.toJSONString(new SysLogMessageModel(messageTemplate,
        List.of(messageParams)));
  }

  private final LogFunctionEnum function;
  private final String messageTemplate;
}