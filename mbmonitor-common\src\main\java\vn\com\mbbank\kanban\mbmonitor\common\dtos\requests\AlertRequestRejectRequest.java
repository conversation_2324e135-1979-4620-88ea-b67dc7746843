package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * Information of Author.
 *
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlertRequestRejectRequest {
  @NotNull
  String id;
  @Size(min = 1, max = 1000)
  String rejectedReason;
}
