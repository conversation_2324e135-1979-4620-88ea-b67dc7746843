package vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import vn.com.mbbank.kanban.mbmonitor.common.configs.RestTemplateConfig;

/**
 * Base Integrate for integration to other services.
 */
@Component
public abstract class BaseIntegration {
  @Autowired
  @Qualifier(RestTemplateConfig.REST_TEMPLATE_INTERNAL)
  protected RestTemplate restTemplate;
}
