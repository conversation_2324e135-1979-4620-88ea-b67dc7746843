package vn.com.mbbank.kanban.mbmonitor.external.execution.controllers;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecuteScriptParamModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecutionScriptRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionScriptResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.services.systems.CommonAclPermissionService;
import vn.com.mbbank.kanban.mbmonitor.external.execution.services.ExecutionService;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ExecutionController.
 */
@ExtendWith(MockitoExtension.class)
class ExecutionControllerTest {

    @Mock
    private ExecutionService executionService;

    @Mock
    private CommonAclPermissionService commonAclPermissionService;

    @InjectMocks
    private ExecutionController executionController;

    @Test
    void execute_success_withValidRequest() {
        // Arrange
        ExecutionScriptRequest request = createTestRequest();
        ExecutionScriptResponse expectedResponse = new ExecutionScriptResponse();
        expectedResponse.setStatus(ExecutionStatusEnum.COMPLETED);
        expectedResponse.setResult("Test result");

        when(executionService.execute(request)).thenReturn(expectedResponse);

        // Act
        ResponseData<ExecutionScriptResponse> responseData = executionController.execute(request);

        // Assert
        assertNotNull(responseData);
        assertEquals(expectedResponse, responseData.getData());
        verify(executionService).execute(request);
    }

    @Test
    void executeAsync_success_withValidRequest() {
        // Arrange
        ExecutionScriptRequest request = createTestRequest();
        doNothing().when(executionService).executeAsync(request);

        // Act
        ResponseData<String> responseData = executionController.executeAsync(request);

        // Assert
        assertNotNull(responseData);
        assertEquals("OK", responseData.getData());
        verify(executionService).executeAsync(request);
    }

    /**
     * Creates a test request for use in tests.
     */
    private ExecutionScriptRequest createTestRequest() {
        ExecutionScriptRequest request = new ExecutionScriptRequest();
        request.setName("Test Script");
        request.setDescription("Test Description");
        request.setExecutionBy("Test User");
        request.setScript("print('Hello, World!')");

        List<ExecuteScriptParamModel> params = new ArrayList<>();
        ExecuteScriptParamModel param = new ExecuteScriptParamModel();
        param.setName("TEST_PARAM");
        param.setValue("test_value");
        params.add(param);

        request.setParams(params);
        return request;
    }
}