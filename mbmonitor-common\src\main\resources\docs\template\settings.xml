<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
    <proxies>
        <proxy>
            <id>proxy</id>
            <active>true</active>
            <protocol>http</protocol>
            <host>proxy.mbbank.com.vn</host>
            <port>8080</port>
            <username>thangnv9</username>
            <password>Abc123</password>
            <nonProxyHosts>www.google.com|*.example.com|10.*</nonProxyHosts>
        </proxy>
    </proxies>


    <servers>
        <server>
            <id>kanban_dev</id>
            <username>kanban_dev</username>
            <password>Mb<PERSON><PERSON>_dev123</password>
        </server>
        <server>
            <id>nexus-releases</id>
            <username>kanban_dev</username>
            <password>Mb<PERSON>ban_dev123</password>
        </server>
        <server>
            <id>nexus-releases</id>
            <username>kanban_dev</username>
            <password>Mbkanban_dev123</password>
        </server>
        <server>
            <id>nexus-central</id>
            <username>kanban_dev</username>
            <password>Mbkanban_dev123</password>
        </server>
        <server>
            <id>nexus-snapshots</id>
            <username>kanban_dev</username>
            <password>Mbkanban_dev123</password>
        </server>
        <server>
            <id>nexus</id>
            <username>kanban_dev</username>
            <password>Mbkanban_dev123</password>
        </server>

    </servers>

    <mirrors>
        <mirror>
            <id>kanban_dev</id>
            <name>Local Mirror Repository</name>
            <url>http://10.1.12.211:8081/repository/maven-kanban-group/</url>
            <mirrorOf>central</mirrorOf>
            <blocked>false</blocked>
        </mirror>

        <mirror>
            <id>kanban_dev</id>
            <url>http://10.1.12.211:8081/repository/maven-kanban-group/</url>
            <mirrorOf>dummy</mirrorOf>
            <blocked>false</blocked>
        </mirror>
    </mirrors>
    <profiles>
    </profiles>
    <usePluginRegistry>false</usePluginRegistry>
</settings>
