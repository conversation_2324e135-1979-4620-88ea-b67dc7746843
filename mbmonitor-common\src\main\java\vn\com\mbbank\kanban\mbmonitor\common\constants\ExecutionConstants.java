package vn.com.mbbank.kanban.mbmonitor.common.constants;

import java.util.Set;

/**
 * ExecutionConstants.
 *
 * <AUTHOR>
 * @created_date 8/23/2024
 */
public class ExecutionConstants {

  public static final String HIDDEN_VARIABLE_PLACEHOLDER = "*****";
  public static final Set<String> WHITE_LISTED_LIBS =
      Set.of(
          "requests", "bs4",
          "cx_Oracle", "imaptools", "exchangelib",
          "openpyxl", "pandas", "selenium",
          "paramiko", "pyotp", "lunarcalendar", "os",
          "time", "json", "math", "datetime", "re",
          "random", "collections", "statistics", "http", "urllib", "argparse");

  public static final Set<String> WHITE_LISTED_FUNCTION =
      Set.of("print", "len", "type", "str", "int", "float",
          "list", "dict", "set", "tuple", "range", "abs",
          "all", "any", "bool", "bytes", "chr", "divmod",
          "enumerate", "filter", "format", "frozenset", "hash",
          "hex", "id", "isinstance", "issubclass", "iter", "map", "max",
          "min", "next", "oct", "ord", "pow", "repr", "reversed", "round",
          "slice", "sorted", "sum", "zip", "os.environ.get", "os.getenv", "os.environ");
}
