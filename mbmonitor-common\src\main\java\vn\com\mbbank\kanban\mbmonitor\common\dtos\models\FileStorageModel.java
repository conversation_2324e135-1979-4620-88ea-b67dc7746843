package vn.com.mbbank.kanban.mbmonitor.common.dtos.models;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * EmailPartnerModel.
 */
@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FileStorageModel {
  Long id;
  String name;
  Long size;
}
