package vn.com.mbbank.kanban.mbmonitor.common.mapper.log;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.GroupQueryLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.GroupQuerySqlEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GroupQueryLogModelMapper extends
    KanbanBaseMapper<GroupQueryLogModel, GroupQuerySqlEntity> {
  GroupQueryLogModelMapper INSTANCE = Mappers.getMapper(GroupQueryLogModelMapper.class);
}
