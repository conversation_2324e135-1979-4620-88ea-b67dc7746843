package vn.com.mbbank.kanban.mbmonitor.common.utils;

import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;

public class AlertPriorityConfigUtilsTest {

  @Test
  void getHighestPositionPriorityConfig_success() {
    var config1 = new AlertPriorityConfigEntity();
    config1.setId(1L);
    config1.setPosition(20);
    config1.setName("1");
    var config2 = new AlertPriorityConfigEntity();
    config2.setId(2L);
    config2.setPosition(2);
    config2.setName("2");
    var config3 = new AlertPriorityConfigEntity();
    config3.setId(3L);
    config3.setPosition(3);
    config3.setName("3");
    var config4 = new AlertPriorityConfigEntity();
    config4.setId(-1L);
    config4.setPosition(10);
    config4.setName("DEFAULT");
    var highestConfig = AlertPriorityConfigUtils.getHighestPositionPriorityConfig(
        List.of(config1, config2, config3, config4));

    Assertions.assertEquals(2, highestConfig.get().getPosition());
  }

  @Test
  void getHighestPositionPriorityConfig_success_firstItemIsDefault() {
    var config1 = new AlertPriorityConfigEntity();
    config1.setId(-1L);
    config1.setPosition(20);
    config1.setName("1");
    var config2 = new AlertPriorityConfigEntity();
    config2.setId(2L);
    config2.setPosition(2);
    config2.setName("2");
    var config3 = new AlertPriorityConfigEntity();
    config3.setId(3L);
    config3.setPosition(3);
    config3.setName("3");
    var config4 = new AlertPriorityConfigEntity();
    config4.setId(1L);
    config4.setPosition(10);
    config4.setName("DEFAULT");
    var highestConfig = AlertPriorityConfigUtils.getHighestPositionPriorityConfig(
        List.of(config1, config2, config3, config4));

    Assertions.assertEquals(2, highestConfig.get().getPosition());
  }
}
