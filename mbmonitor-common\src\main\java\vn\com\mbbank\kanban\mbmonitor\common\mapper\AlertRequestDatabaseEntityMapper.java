package vn.com.mbbank.kanban.mbmonitor.common.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.AlertRequestDatabaseRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertRequestDatabaseEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 06/11/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AlertRequestDatabaseEntityMapper extends
    KanbanBaseMapper<AlertRequestDatabaseEntity, AlertRequestDatabaseRequest> {
  AlertRequestDatabaseEntityMapper INSTANCE =
      Mappers.getMapper(AlertRequestDatabaseEntityMapper.class);
}