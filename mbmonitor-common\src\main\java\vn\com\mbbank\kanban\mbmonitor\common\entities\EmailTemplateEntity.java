package vn.com.mbbank.kanban.mbmonitor.common.entities;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/27/2024
 */
@Entity
@Data
@Table(name = TableName.EMAIL_TEMPLATE)
@EqualsAndHashCode(callSuper = true)
public class EmailTemplateEntity extends BaseEntity<Long> {

  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "EMAIL_TEMPLATE_SEQ", sequenceName = "EMAIL_TEMPLATE_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "EMAIL_TEMPLATE_SEQ")
  private Long id;

  @Column(name = "NAME")
  private String name;

  @Column(name = "DESCRIPTION")
  private String description;

  @Column(name = "CONTENT")
  private String content;

  @Column(name = "SUBJECT")
  private String subject;

  @Override
  public Long getId() {
    return id;
  }
}
