package vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CollectEmailAlertContentTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CollectEmailConfigLogModel {
  private String name;
  private String description;
  private Long intervalTime;
  private String email;
  private boolean active;
  private RuleGroupType ruleGroup;
  private String service;
  private String application;
  private String priorityConfig;
  private String recipient;
  private CollectEmailAlertContentTypeEnum contentType;
  private String content;
  private String contentValue;
  private String condition;
}
