package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CollectEmailAlertContentTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CollectEmailConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupConverter;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;

@Data
@Entity
@Table(name = "COLLECT_EMAIL_CONFIG")
@EqualsAndHashCode(callSuper = true)
@Getter
public class CollectEmailConfigEntity extends BaseEntity<Long> {

  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "COLLECT_EMAIL_CONFIG_SEQ", sequenceName = "COLLECT_EMAIL_CONFIG_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "COLLECT_EMAIL_CONFIG_SEQ")
  private Long id;

  @Column(name = "NAME", nullable = false)
  private String name;

  @Column(name = "DESCRIPTION")
  private String description;

  @Column(name = "INTERVAL_TIME", nullable = false)
  private Long intervalTime;

  @Column(name = "EMAIL_CONFIG_ID")
  private Long emailConfigId;

  @Column(name = "ACTIVE")
  private boolean active;

  @Column(name = "RULE_GROUP", columnDefinition = "NCLOB")
  @Convert(converter = RuleGroupConverter.class)
  private RuleGroupType ruleGroup;

  @Column(name = "SERVICE_ID")
  private String serviceId;

  @Column(name = "APPLICATION_ID")
  private String applicationId;

  @Column(name = "PRIORITY_CONFIG_ID")
  private Long priorityConfigId;

  @Column(name = "RECIPIENT")
  private String recipient;

  @Column(name = "CONTENT_TYPE")
  @Enumerated(EnumType.STRING)
  private CollectEmailAlertContentTypeEnum contentType;

  @Column(name = "CONTENT")
  private String content;

  @Column(name = "CONTENT_VALUE")
  private String contentValue;

  @Column(name = "TYPE")
  @Enumerated(EnumType.STRING)
  private CollectEmailConfigTypeEnum type;

  @Column(name = "LAST_RECEIVED_EMAIL_DATE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date lastReceivedEmailDate;

  @Column(name = "ABSENCE_INTERVAL")
  private Long absenceInterval;

  @Column(name = "ALERT_REPEAT_INTERVAL")
  private Long alertRepeatInterval;

  @Override
  public Long getId() {
    return id;
  }
}