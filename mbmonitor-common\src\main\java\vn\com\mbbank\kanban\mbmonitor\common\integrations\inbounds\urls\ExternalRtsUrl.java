package vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls;

import vn.com.mbbank.kanban.mbmonitor.common.configs.KanbanPropertyConfigUtils;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.BaseUrl;

/**
 * External notification URL.
 */
public class ExternalRtsUrl extends BaseUrl {
  public static final String VERSION = "/v1";
  public static final String NOTIFICATION = VERSION + "/notifications";

  /**
   * Demo for ExternalNotificationUrl.
   *
   * @param path Path
   */
  public ExternalRtsUrl(String path) {
    super(path);
  }

  /**
   * Load url from properties.
   *
   * @param baseApi baseApi
   * @return url full
   */
  public static String getUrl(String baseApi) {
    return KanbanPropertyConfigUtils.getProperty("monitor.rts.url",
        getBaseUrl() + "api/rts") + baseApi;
  }

}
