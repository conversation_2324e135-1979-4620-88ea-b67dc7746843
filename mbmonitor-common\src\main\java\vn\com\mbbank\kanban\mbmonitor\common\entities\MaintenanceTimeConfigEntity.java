package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeUnitEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupConverter;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;

@Entity
@Data
@Table(name = TableName.MAINTENANCE_TIME_CONFIG)
@EqualsAndHashCode(callSuper = true)
public class MaintenanceTimeConfigEntity extends BaseEntity<Long> {

  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "MAINTENANCE_TIME_CONFIG_SEQ",
      sequenceName = "MAINTENANCE_TIME_CONFIG_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "MAINTENANCE_TIME_CONFIG_SEQ")
  private Long id;

  @Column(name = "NAME", nullable = false)
  private String name;

  @Column(name = "DESCRIPTION")
  private String description;

  @Column(name = "ACTIVE")
  private Boolean active;

  @Column(name = "RULE_GROUP")
  @Convert(converter = RuleGroupConverter.class)
  private RuleGroupType ruleGroup;

  @Column(name = "TYPE", nullable = false)
  @Enumerated(EnumType.STRING)
  private MaintenanceTimeConfigTypeEnum type;

  @Column(name = "NEXT_TIME")
  private Integer nextTime;

  @Column(name = "UNIT", nullable = false)
  @Enumerated(EnumType.STRING)
  private MaintenanceTimeUnitEnum unit;

  @Column(name = "CRON_EXPRESSION")
  private String cronExpression;

  @Column(name = "START_TIME")
  @Temporal(TemporalType.TIMESTAMP)
  private Date startTime;

  @Column(name = "END_TIME")
  @Temporal(TemporalType.TIMESTAMP)
  private Date endTime;

  @Override
  public Long getId() {
    return id;
  }

}
