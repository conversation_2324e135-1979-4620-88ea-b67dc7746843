package vn.com.mbbank.kanban.mbmonitor.common.configs;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/20/2025
 */
@Component
public class ApplicationContextProvider implements ApplicationContextAware {
  private static ApplicationContext context;

  @Override
  public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
    context = applicationContext;
  }

  /**
   * get application context.
   *
   * @return ApplicationContext
   */
  public static ApplicationContext getApplicationContext() {
    return context;
  }

  /**
   * find a bean spring.
   *
   * @param beanClass beanClass
   * @param <T>       class bean
   * @return bean
   */
  public static <T> T getBean(Class<T> beanClass) {
    return !KanbanCommonUtil.isEmpty(context) ? context.getBean(beanClass) : null;
  }
}
