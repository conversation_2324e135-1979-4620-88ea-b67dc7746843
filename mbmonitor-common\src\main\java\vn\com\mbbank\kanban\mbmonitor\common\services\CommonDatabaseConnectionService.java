package vn.com.mbbank.kanban.mbmonitor.common.services;

import com.zaxxer.hikari.HikariConfig;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.DatabaseConnectionRequest;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/20/2025
 */
public interface CommonDatabaseConnectionService {
  /**
   * Test connection.
   *
   * @param connectInfo connectInfo
   * @return true/false
   * @throws BusinessException ex
   */
  boolean testConnection(DatabaseConnectionRequest connectInfo) throws BusinessException;

  /**
   * create hikari config.
   *
   * @param connectInfo connectInfo
   * @return HikariConfig
   */
  HikariConfig createHikariConfig(DatabaseConnectionRequest connectInfo);

}
