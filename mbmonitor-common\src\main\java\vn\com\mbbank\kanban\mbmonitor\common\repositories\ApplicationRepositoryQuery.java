package vn.com.mbbank.kanban.mbmonitor.common.repositories;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import vn.com.mbbank.kanban.core.utils.KanbanEntityUtils;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.ApplicationPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;

/**
 * ApplicationRepositoryQuery.
 */
@Component
public class ApplicationRepositoryQuery {

  /**
   * Constructs a query to retrieve applications by their IDs.
   *
   * @param ids the list of application IDs
   * @return a prepared query for fetching applications with the given IDs
   */
  public PrepareQuery findAllByIdIn(List<String> ids) {
    return new PrepareQuery("""
        SELECT application.ID AS id, application.NAME AS name, application.DESCRIPTION AS description,
        application.SERVICE_ID AS serviceId, service.NAME AS serviceName
        FROM APPLICATION application
        INNER JOIN SERVICE service ON application.SERVICE_ID = service.ID
        """)
        .append(" WHERE application.ID IN (:ids)", "ids", ids);
  }

  /**
   * Constructs a query to retrieve applications with priority information based on the alert group status.
   *
   * @param alertGroupStatus the status of the alert group
   * @return a prepared query for fetching applications with priority details
   */
  public PrepareQuery findApplicationWithPriorityByAlertGroupStatus(AlertGroupStatusEnum alertGroupStatus) {
    return new PrepareQuery("""
        SELECT application.ID                   AS id,
               application.NAME                 AS name,
               application.SERVICE_ID           AS serviceId,
               alert.ALERT_PRIORITY_CONFIG_ID   AS alertPriorityConfigId,
               COUNT(alertGroup.PRIMARY_ALERT_ID) AS alertAmount
        FROM ALERT_GROUP alertGroup
        JOIN ALERT alert
             on alertGroup.PRIMARY_ALERT_ID = alert.ID
        LEFT JOIN APPLICATION application
             ON alert.APPLICATION_ID = application.ID
         WHERE 1 = 1
        """)
        .append(buildQueryStatusEq(alertGroupStatus))
        .append("""
            GROUP BY application.ID, application.NAME, application.SERVICE_ID, alert.ALERT_PRIORITY_CONFIG_ID
            ORDER BY application.NAME
            """);
  }

  /**
   * Constructs a query to retrieve an application by its ID.
   *
   * @param id the application ID
   * @return a prepared query for fetching the application with the given ID
   */
  public PrepareQuery findApplicationById(String id) {
    return new PrepareQuery("""
        SELECT application.ID AS id, application.NAME AS name, application.DESCRIPTION AS description,
        application.SERVICE_ID AS serviceId, service.NAME AS serviceName
        FROM APPLICATION application
        INNER JOIN SERVICE service ON application.SERVICE_ID=service.ID
        """)
        .append(" WHERE application.ID= :id", "id", id);
  }

  /**
   * Constructs a query to retrieve applications based on pagination and filter criteria.
   *
   * @param applicationPaginationRequest the pagination and filtering request
   * @return a prepared query for fetching applications that match the criteria
   */
  public PrepareQuery findAll(ApplicationPaginationRequest applicationPaginationRequest) {
    var query = new PrepareQuery("""
        SELECT application.ID AS id, application.NAME AS name, application.SERVICE_ID AS serviceId,
        application.DESCRIPTION AS description, service.NAME AS serviceName,
        application.CREATED_DATE AS createdDate
        FROM APPLICATION application
        INNER JOIN SERVICE service ON application.SERVICE_ID=service.ID
        WHERE 1=1
        """
    )
        .append(buildQueryServiceIdIn(applicationPaginationRequest.getServiceIds()))
        .append(buildQueryNameLike(applicationPaginationRequest.getName()), LikeMatcher.CONTAINING)
        .append(buildQuerySearchLike(applicationPaginationRequest.getSearch()))
        .append(buildQueryDeletedEq(Boolean.TRUE.equals(applicationPaginationRequest.getWithDeleted())));

    String sortColumn = KanbanEntityUtils.getColumnName(applicationPaginationRequest.getSortBy(),
        ApplicationEntity.class);
    if (!StringUtils.isNullOrEmpty(sortColumn)) {
      query.append(" ORDER BY APPLICATION.").append(sortColumn);
      if (Objects.nonNull(applicationPaginationRequest.getSortOrder())) {
        query.append(" ").append(applicationPaginationRequest.getSortOrder().name());
      }
    }
    return query;
  }

  /**
   * Builds a query fragment for filtering by alert group status.
   *
   * @param alertGroupStatus the alert group status to filter by
   * @return a prepared query fragment for alert group status, or null if the status is null
   */
  private PrepareQuery buildQueryStatusEq(AlertGroupStatusEnum alertGroupStatus) {
    if (Objects.isNull(alertGroupStatus)) {
      return null;
    }
    return new PrepareQuery(" AND alertGroup.STATUS = :status ", "status", alertGroupStatus.name());
  }

  /**
   * Builds a query fragment for filtering deleted applications.
   *
   * @param withDeleted flag indicating whether to include deleted applications
   * @return a prepared query fragment to filter out deleted applications if necessary
   */
  PrepareQuery buildQueryDeletedEq(boolean withDeleted) {
    return withDeleted ? new PrepareQuery() : new PrepareQuery(" AND application.DELETED = 0");
  }

  /**
   * Builds a query fragment for filtering by a list of service IDs.
   *
   * @param serviceIds the list of service IDs
   * @return a prepared query fragment filtering by the given service IDs, or null if the list is empty
   */
  PrepareQuery buildQueryServiceIdIn(List<String> serviceIds) {
    if (CollectionUtils.isEmpty(serviceIds)) {
      return null;
    }
    return new PrepareQuery(" AND application.SERVICE_ID IN (:serviceIds)", "serviceIds", serviceIds);
  }

  /**
   * Builds a query fragment for filtering by application name using a LIKE match.
   *
   * @param name the name filter
   * @return a prepared query fragment using a case-insensitive match on the application name, or null
   */
  PrepareQuery buildQueryNameLike(String name) {
    if (StringUtils.isBlank(name)) {
      return null;
    }
    return new PrepareQuery(" AND LOWER(application.NAME) LIKE :name",
        Map.of("name", name.toLowerCase()));
  }

  /**
   * Builds a query fragment for a comprehensive search across name, description, ID, and service ID.
   *
   * @param search the search term
   * @return a prepared query fragment for the search criteria, or null if the search term is blank
   */
  PrepareQuery buildQuerySearchLike(String search) {
    if (StringUtils.isBlank(search)) {
      return null;
    }
    PrepareQuery prepareQuery = new PrepareQuery();
    prepareQuery.append(new PrepareQuery("AND LOWER(application.NAME) LIKE :search"), LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(application.DESCRIPTION) LIKE :search"), LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(application.ID) LIKE :search"), LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(application.SERVICE_ID) LIKE :search",
        Map.of("search", search.toLowerCase())), LikeMatcher.CONTAINING);
    return prepareQuery;
  }
}
