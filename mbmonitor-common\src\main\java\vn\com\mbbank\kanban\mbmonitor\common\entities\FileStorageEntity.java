package vn.com.mbbank.kanban.mbmonitor.common.entities;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseSoftEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/27/2024
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = true, exclude = {"id"})
@Table(name = TableName.FILE_STORAGE)
public class FileStorageEntity extends BaseSoftEntity<Long> {

  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "FILE_STORAGE_SEQ", sequenceName = "FILE_STORAGE_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "FILE_STORAGE_SEQ")
  private Long id;

  @Column(name = "PATH")
  private String path;

  @Column(name = "\"SIZE\"")
  private Long size;

  @Column(name = "DEPENDENCY_ID")
  private String dependencyId;

  @Column(name = "DEPENDENCY_NAME")
  private String dependencyName;

  @Override
  public Long getId() {
    return id;
  }
}
