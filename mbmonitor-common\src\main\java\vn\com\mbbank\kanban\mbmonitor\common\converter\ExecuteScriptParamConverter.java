package vn.com.mbbank.kanban.mbmonitor.common.converter;

import com.alibaba.fastjson2.JSON;
import jakarta.persistence.AttributeConverter;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.execution.ExecuteScriptParamModel;

/**
 * ExecuteScriptParamConverter.
 */
public class ExecuteScriptParamConverter implements AttributeConverter<List<ExecuteScriptParamModel>, String> {
  private static final Logger logger = LoggerFactory.getLogger(ExecuteScriptParamConverter.class);

  @Override
  public String convertToDatabaseColumn(List<ExecuteScriptParamModel> model) {
    return JSON.toJSONString(model);
  }

  @Override
  public List<ExecuteScriptParamModel> convertToEntityAttribute(String dbData) {
    try {
      return JSON.parseArray(dbData, ExecuteScriptParamModel.class);
    } catch (Exception e) {
      logger.warn("Convert to ExecuteScriptParamModel error fallback to empty array");
      return new ArrayList<>();
    }
  }

}