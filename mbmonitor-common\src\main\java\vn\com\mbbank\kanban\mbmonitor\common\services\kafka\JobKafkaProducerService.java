package vn.com.mbbank.kanban.mbmonitor.common.services.kafka;


import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.KafkaJobModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;

/**
 * interface KafkaProducerService.
 */
public interface JobKafkaProducerService {

  /**
   * notify service job to update job mail.
   *
   * @param type type
   * @param data data
   */
  void notifyJobUpdate(KafkaTypeEnum type, KafkaJobModel data) throws BusinessException;
}
