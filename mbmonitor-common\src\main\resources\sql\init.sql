CREATE SEQUENCE MBMONITOR.ALERT_GROUP_CONFIG_CONDITION_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.ALERT_GROUP_CONFIG_POSITION_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.ALERT_GROUP_CONFIG_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/


CREATE SEQUENCE MBMONITOR.ALERT_GROUP_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.ALERT_GROUP_CONFIG_DEPENDENCY_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.ALERT_PRIORITY_CONFIG_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.ALERT_PRIORITY_CONFIG_POSITION_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.ALERT_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.APPLICATION_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.COLLECT_EMAIL_CONFIG_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.CUSTOM_OBJECT_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.DATABASE_COLLECT_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.DATABASE_COLLECT_TEMP_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.DATABASE_CONNECTION_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.EMAIL_CONFIG_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.EMAIL_PARTNER_ADDRESS_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.EMAIL_PARTNER_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.EMAIL_TEMPLATE_RECEIVER_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.EMAIL_TEMPLATE_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.FILE_STORAGE_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.NOTE_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.RAW_VALUE_CONFIG_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.SERVICE_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.SYS_PERMISSION_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.SYS_ROLE_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.SYS_USER_ROLE_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.SYS_USER_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/

CREATE SEQUENCE MBMONITOR.WEBHOOK_SEQ
    START WITH 1
    INCREMENT BY 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
    CACHE 50
/
CREATE SEQUENCE MBMONITOR.JOB_HISTORY_DETAIL_SEQ INCREMENT BY 1 MINVALUE 1 MAXVALUE 99999999999999999999999999999999999999 NOCYCLE CACHE 50
/
CREATE SEQUENCE MBMONITOR.JOB_HISTORY_SEQ INCREMENT BY 1 MINVALUE 1 MAXVALUE 9999999999999999999999999999999999999999 NOCYCLE CACHE 50
/
CREATE TABLE MBMONITOR.NOTE
(
    ALERT_GROUP_ID NUMBER(19),
    CREATED_DATE   TIMESTAMP(6),
    ID             NUMBER(19) DEFAULT MBMONITOR.NOTE_SEQ.NEXTVAL NOT NULL
        CONSTRAINT NOTE_pk
            PRIMARY KEY,
    MODIFIED_DATE  TIMESTAMP(6),
    CONTENT        VARCHAR2(300 CHAR),
    CREATED_BY     VARCHAR2(255 CHAR),
    MODIFIED_BY    VARCHAR2(255 CHAR)
)
/

CREATE INDEX MBMONITOR.NOTE_ALERT_GROUP_ID_INDEX
    ON MBMONITOR.NOTE (ALERT_GROUP_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.SYS_USER
(
    IS_ACTIVE     NUMBER(1)
        CHECK (IS_ACTIVE IN (0, 1)),
    CREATED_DATE  TIMESTAMP(6),
    ID            NUMBER(19) DEFAULT MBMONITOR.SYS_USER_SEQ.NEXTVAL NOT NULL
        PRIMARY KEY,
    MODIFIED_DATE TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255 CHAR),
    EMAIL         VARCHAR2(255 CHAR),
    MODIFIED_BY   VARCHAR2(255 CHAR),
    USERNAME      VARCHAR2(255 CHAR),
    IS_ADMIN      NUMBER(1)  DEFAULT 0
        CHECK (IS_ADMIN = 1 OR IS_ADMIN = 0)
)
/

CREATE INDEX MBMONITOR.SYS_USER_USERNAME_INDEX
    ON MBMONITOR.SYS_USER (USERNAME) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.ALERT_PRIORITY_CONFIG
(
    ID            NUMBER(19) DEFAULT MBMONITOR.ALERT_PRIORITY_CONFIG_SEQ.NEXTVAL          NOT NULL
        CONSTRAINT ALERT_PRIORITY_CONFIG_pk
            PRIMARY KEY,
    NAME          VARCHAR2(50)                                                            NOT NULL,
    COLOR         VARCHAR2(20)                                                            NOT NULL,
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255),
    DELETED       NUMBER(1),
    DELETED_BY    VARCHAR2(255),
    DELETED_DATE  TIMESTAMP(6),
    POSITION      NUMBER     DEFAULT MBMONITOR.ALERT_PRIORITY_CONFIG_POSITION_SEQ.NEXTVAL NOT NULL
)
/

CREATE INDEX MBMONITOR.ALERT_PRIORITY_CONFIG_POSITION_INDEX
    ON MBMONITOR.ALERT_PRIORITY_CONFIG (POSITION) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.PRIORITY_RAW_VALUE_CONFIG
(
    ID                       NUMBER(19) DEFAULT MBMONITOR.RAW_VALUE_CONFIG_SEQ.NEXTVAL NOT NULL
        CONSTRAINT PRIORITY_RAW_VALUE_CONFIG_pk
            PRIMARY KEY,
    ALERT_PRIORITY_CONFIG_ID NUMBER(19)                                                NOT NULL,
    VALUE                    VARCHAR2(300)                                             NOT NULL,
    TYPE                     VARCHAR2(255)                                             NOT NULL
        CONSTRAINT TYPE_CHECK
            CHECK (TYPE IN ('PRIORITY', 'ALERT_CONTENT')),
    CREATED_DATE             TIMESTAMP(6),
    CREATED_BY               VARCHAR2(255),
    MODIFIED_DATE            TIMESTAMP(6),
    MODIFIED_BY              VARCHAR2(255)
)
/

CREATE INDEX MBMONITOR.PRIORITY_RAW_VALUE_CONFIG_ALERT_PRIORITY_CONFIG_ID_INDEX
    ON MBMONITOR.PRIORITY_RAW_VALUE_CONFIG (ALERT_PRIORITY_CONFIG_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.SYS_ROLE_PERMISSION
(
    ID            NUMBER DEFAULT MBMONITOR.SYS_PERMISSION_SEQ.NEXTVAL NOT NULL
        PRIMARY KEY,
    MODULE_NAME   VARCHAR2(250),
    ACTION        VARCHAR2(50),
    ROLE_ID       NUMBER,
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255)
)
/

CREATE INDEX MBMONITOR.SYS_ROLE_PERMISSION_ROLE_ID_INDEX
    ON MBMONITOR.SYS_ROLE_PERMISSION (ROLE_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.SYS_ROLE
(
    ID            NUMBER    DEFAULT MBMONITOR.SYS_ROLE_SEQ.NEXTVAL NOT NULL
        PRIMARY KEY,
    NAME          VARCHAR2(250),
    DESCRIPTION   VARCHAR2(2000),
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255),
    ACTIVE        NUMBER(1) DEFAULT 1
        CHECK (active IN (0, 1))
)
/

CREATE TABLE MBMONITOR.SYS_USER_ROLE
(
    ID            NUMBER DEFAULT MBMONITOR.SYS_USER_ROLE_SEQ.NEXTVAL NOT NULL
        PRIMARY KEY,
    USER_ID       NUMBER,
    ROLE_ID       NUMBER,
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255)
)
/

CREATE INDEX MBMONITOR.SYS_USER_ROLE_USER_ID_INDEX
    ON MBMONITOR.SYS_USER_ROLE (USER_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.SYS_USER_ROLE_ROLE_ID_INDEX
    ON MBMONITOR.SYS_USER_ROLE (ROLE_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.CUSTOM_OBJECT
(
    ID            NUMBER DEFAULT MBMONITOR.CUSTOM_OBJECT_SEQ.NEXTVAL NOT NULL
        CONSTRAINT CUSTOM_OBJECT_pk
            PRIMARY KEY,
    TYPE          VARCHAR2(30),
    FROM_INDEX    NUMBER,
    TO_INDEX      NUMBER,
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255),
    DELETED       NUMBER(1),
    DELETED_DATE  TIMESTAMP(6),
    DELETED_BY    VARCHAR2(255),
    NAME          VARCHAR2(50 CHAR),
    DESCRIPTION   VARCHAR2(300 CHAR),
    FROM_KEYWORD  VARCHAR2(20 CHAR),
    TO_KEYWORD    VARCHAR2(20 CHAR),
    REGEX         VARCHAR2(300 CHAR)
)
/

CREATE TABLE MBMONITOR.ALERT
(
    ID                       NUMBER DEFAULT MBMONITOR.ALERT_SEQ.NEXTVAL NOT NULL,
    APPLICATION_ID           VARCHAR2(10),
    SERVICE_ID               VARCHAR2(10),
    CONTENT                  VARCHAR2(3000 CHAR),
    RECIPIENT                VARCHAR2(255 CHAR),
    STATUS                   VARCHAR2(300),
    ALERT_PRIORITY_CONFIG_ID NUMBER,
    CREATED_DATE             TIMESTAMP(6),
    CREATED_BY               VARCHAR2(255),
    MODIFIED_DATE            TIMESTAMP(6),
    MODIFIED_BY              VARCHAR2(255),
    ALERT_GROUP_ID           NUMBER DEFAULT 0
)
    PARTITION BY RANGE (CREATED_DATE) INTERVAL (NUMTOYMINTERVAL(1, 'MONTH'))
(
    PARTITION PARTBASE VALUES LESS THAN (TO_DATE('2024-10-01 00:00:00', 'SYYYY-MM-DD HH24:MI:SS',
                                                 'NLS_CALENDAR=GREGORIAN'))
)
/

ALTER TABLE MBMONITOR.ALERT
    ADD CONSTRAINT ALERT_PK PRIMARY KEY (ID, CREATED_DATE) USING INDEX LOCAL
/

-- search by application and order by id
CREATE INDEX MBMONITOR.ALERT_APPLICATION_ID_ID_INDEX
    ON MBMONITOR.ALERT (APPLICATION_ID, ID) LOCAL TABLESPACE INDEXS
/

-- search by priority and order by id
CREATE INDEX MBMONITOR.ALERT_ALERT_PRIORITY_CONFIG_ID_ID_INDEX
    ON MBMONITOR.ALERT (ALERT_PRIORITY_CONFIG_ID, ID) LOCAL TABLESPACE INDEXS
/

-- search by priority and application and order by id
CREATE INDEX MBMONITOR.ALERT_ALERT_PRIORITY_CONFIG_ID_APPLICATION_ID_ID_INDEX
    ON MBMONITOR.ALERT (ALERT_PRIORITY_CONFIG_ID, APPLICATION_ID, ID) LOCAL TABLESPACE INDEXS
/

-- search by service,  application and order by id
CREATE INDEX MBMONITOR.ALERT_SERVICE_ID_APPLICATION_ID_ID_INDEX
    ON MBMONITOR.ALERT (SERVICE_ID, APPLICATION_ID, ID) LOCAL TABLESPACE INDEXS
/

-- search by service and order by id
CREATE INDEX MBMONITOR.ALERT_SERVICE_ID_ID_INDEX
    ON MBMONITOR.ALERT (SERVICE_ID, ID) LOCAL TABLESPACE INDEXS
/

-- search by priority , service and order by id
CREATE INDEX MBMONITOR.ALERT_ALERT_PRIORITY_CONFIG_ID_SERVICE_ID_ID_INDEX
    ON MBMONITOR.ALERT (ALERT_PRIORITY_CONFIG_ID, SERVICE_ID, ID) LOCAL TABLESPACE INDEXS
/

-- search by priority, service, app and order by id
CREATE INDEX MBMONITOR.ALERT_ALERT_PRIORITY_CONFIG_ID_SERVICE_ID_APPLICATION_ID_ID_INDEX
    ON MBMONITOR.ALERT (ALERT_PRIORITY_CONFIG_ID, SERVICE_ID, APPLICATION_ID, ID) LOCAL TABLESPACE INDEXS
/

-- Filter alert by alertGroupId
CREATE INDEX MBMONITOR.ALERT_ALERT_GROUP_ID_INDEX
    ON MBMONITOR.ALERT (ALERT_GROUP_ID) LOCAL TABLESPACE INDEXS
/

-- search order by recipient
CREATE INDEX MBMONITOR.ALERT_RECIPIENT_ID_INDEX
    ON MBMONITOR.ALERT (RECIPIENT, ID) LOCAL TABLESPACE INDEXS
/

-- search by created time
CREATE INDEX MBMONITOR.ALERT_CREATED_DATE_ID_INDEX
    ON MBMONITOR.ALERT (CREATED_DATE DESC, ID ASC) LOCAL TABLESPACE INDEXS
/

-- search not grouped alert in AlertGroupJob
CREATE INDEX MBMONITOR.ALERT_STATUS_ALERT_GROUP_ID_ID_INDEX
    ON MBMONITOR.ALERT (STATUS, ALERT_GROUP_ID, ID) LOCAL TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.SERVICE
(
    ID            VARCHAR2(10)      NOT NULL
        CONSTRAINT SERVICE_pk
            PRIMARY KEY,
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255),
    DELETED_BY    VARCHAR2(255),
    DELETED       NUMBER(1),
    DELETED_DATE  TIMESTAMP(6),
    NAME          VARCHAR2(50 CHAR) NOT NULL,
    DESCRIPTION   VARCHAR2(300 CHAR)
)
/

CREATE TABLE MBMONITOR.APPLICATION
(
    ID            VARCHAR2(10)      NOT NULL
        CONSTRAINT APPLICATION_pk
            PRIMARY KEY,
    SERVICE_ID    VARCHAR2(10)      NOT NULL,
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255)     NOT NULL,
    DELETED_BY    VARCHAR2(255),
    DELETED       NUMBER(1),
    DELETED_DATE  TIMESTAMP(6),
    NAME          VARCHAR2(50 CHAR) NOT NULL,
    DESCRIPTION   VARCHAR2(300 CHAR)
)
/

CREATE INDEX MBMONITOR.APPLICATION_SERVICE_ID_INDEX
    ON MBMONITOR.APPLICATION (SERVICE_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.WEBHOOK
(
    ID                         NUMBER DEFAULT MBMONITOR.WEBHOOK_SEQ.NEXTVAL NOT NULL
        CONSTRAINT WEBHOOK_pk
            PRIMARY KEY,
    NAME                       VARCHAR2(100),
    DATA_TYPE                  VARCHAR2(50),
    TOKEN                      VARCHAR2(100),
    SERVICE_TYPE               VARCHAR2(50),
    SERVICE_ID                 VARCHAR2(10),
    APPLICATION_TYPE           VARCHAR2(50),
    APPLICATION_ID             VARCHAR2(10),
    APPLICATION_MAP_VALUE      VARCHAR2(50),
    ALERT_CONTENT_TYPE         VARCHAR2(50),
    ALERT_CONTENT_CUSTOM_VALUE VARCHAR2(3000 CHAR),
    ALERT_CONTENT_MAP_VALUE    VARCHAR2(250),
    CONTACT_TYPE               VARCHAR2(50),
    CONTACT_CUSTOM_VALUE       VARCHAR2(255 CHAR),
    CONTACT_MAP_VALUE          VARCHAR2(250),
    PRIORITY_TYPE              VARCHAR2(50),
    PRIORITY_CUSTOM_VALUE      VARCHAR2(250),
    PRIORITY_MAP_VALUE         VARCHAR2(250),
    CREATED_DATE               TIMESTAMP(6),
    CREATED_BY                 VARCHAR2(255),
    MODIFIED_DATE              TIMESTAMP(6),
    MODIFIED_BY                VARCHAR2(255),
    SERVICE_MAP_VALUE          VARCHAR2(250),
    ALERT_PRIORITY_CONFIG_ID   NUMBER
)
/

CREATE INDEX MBMONITOR.WEBHOOK_TOKEN_INDEX
    ON MBMONITOR.WEBHOOK (TOKEN) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.WEBHOOK_APPLICATION_ID_INDEX
    ON MBMONITOR.WEBHOOK (APPLICATION_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.WEBHOOK_SERVICE_ID_INDEX
    ON MBMONITOR.WEBHOOK (SERVICE_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.WEBHOOK_ALERT_PRIORITY_CONFIG_ID_INDEX
    ON MBMONITOR.WEBHOOK (ALERT_PRIORITY_CONFIG_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.EMAIL_PARTNER
(

    ID            NUMBER(19) DEFAULT MBMONITOR.EMAIL_PARTNER_SEQ.NEXTVAL NOT NULL
        PRIMARY KEY,
    NAME          VARCHAR2(50 CHAR)                                      NOT NULL,
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255)
)
/

CREATE TABLE MBMONITOR.EMAIL_PARTNER_ADDRESS
(
    ID               NUMBER(19) DEFAULT MBMONITOR.EMAIL_PARTNER_ADDRESS_SEQ.NEXTVAL NOT NULL
        PRIMARY KEY,
    ADDRESS          VARCHAR2(256)                                                  NOT NULL,
    EMAIL_PARTNER_ID NUMBER(19),
    CREATED_DATE     TIMESTAMP(6),
    CREATED_BY       VARCHAR2(255),
    MODIFIED_DATE    TIMESTAMP(6),
    MODIFIED_BY      VARCHAR2(255)
)
/

CREATE INDEX MBMONITOR.EMAIL_PARTNER_ADDRESS_EMAIL_PARTNER_ID_INDEX
    ON MBMONITOR.EMAIL_PARTNER_ADDRESS (EMAIL_PARTNER_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.ALERT_GROUP_CONFIG
(
    ID                        NUMBER    DEFAULT MBMONITOR.ALERT_GROUP_CONFIG_SEQ.NEXTVAL NOT NULL
        CONSTRAINT ALERT_GROUP_CONFIG_pk
            PRIMARY KEY,
    NAME                      VARCHAR2(50 CHAR)                                          NOT NULL,
    DESCRIPTION               VARCHAR2(200 CHAR),
    TYPE                      VARCHAR2(30)                                               NOT NULL,
    DELETED                   NUMBER,
    CREATED_DATE              TIMESTAMP(6),
    CREATED_BY                VARCHAR2(255),
    MODIFIED_DATE             TIMESTAMP(6),
    MODIFIED_BY               VARCHAR2(255),
    DELETED_BY                VARCHAR2(255),
    POSITION                  NUMBER    DEFAULT MBMONITOR.ALERT_GROUP_CONFIG_POSITION_SEQ.NEXTVAL,
    ALERT_OUTPUT              VARCHAR2(30),
    DELETED_DATE              TIMESTAMP(6),
    ACTIVE                    NUMBER(1) DEFAULT 1,
    CUSTOM_SERVICE_ID         VARCHAR2(10),
    CUSTOM_APPLICATION_ID     VARCHAR2(10),
    CUSTOM_RECIPIENT          VARCHAR2(255 CHAR),
    CUSTOM_CONTENT            VARCHAR2(3000 CHAR),
    CUSTOM_PRIORITY_CONFIG_ID NUMBER
)
/

CREATE TABLE MBMONITOR.ALERT_GROUP
(
    ID                    NUMBER DEFAULT MBMONITOR.ALERT_GROUP_SEQ.NEXTVAL NOT NULL
        CONSTRAINT ALERT_GROUP_pk
            PRIMARY KEY,
    PRIMARY_ALERT_ID      NUMBER                                           NOT NULL,
    MATCH_VALUE           VARCHAR2(1000),
    ALERT_GROUP_CONFIG_ID NUMBER DEFAULT 0,
    CREATED_DATE          TIMESTAMP(6),
    CREATED_BY            VARCHAR2(255),
    MODIFIED_DATE         TIMESTAMP(6),
    MODIFIED_BY           VARCHAR2(255),
    STATUS                VARCHAR2(30),
    SERVICE_ID            VARCHAR2(10)                                     NOT NULL,
    APPLICATION_ID        VARCHAR2(10)                                     NOT NULL
)
/

CREATE INDEX MBMONITOR.ALERT_GROUP_ALERT_GROUP_CONFIG_ID_MATCH_VALUE_STATUS_INDEX
    ON MBMONITOR.ALERT_GROUP (ALERT_GROUP_CONFIG_ID, MATCH_VALUE, STATUS) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.ALERT_GROUP_ALERT_GROUP_CONFIG_ID_STATUS_INDEX
    ON MBMONITOR.ALERT_GROUP (ALERT_GROUP_CONFIG_ID, STATUS) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.ALERT_GROUP_PRIMARY_ALERT_ID_INDEX
    ON MBMONITOR.ALERT_GROUP (PRIMARY_ALERT_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.ALERT_GROUP_ALERT_GROUP_CONFIG_ID_INDEX
    ON MBMONITOR.ALERT_GROUP (ALERT_GROUP_CONFIG_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.ALERT_GROUP_STATUS_PRIMARY_ALERT_ID_INDEX
    ON MBMONITOR.ALERT_GROUP (STATUS, PRIMARY_ALERT_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.ALERT_GROUP_SERVICE_ID_APPLICATION_ID_ALERT_GROUP_CONFIG_ID_CREATED_DATE_INDEX
    ON MBMONITOR.ALERT_GROUP (SERVICE_ID ASC, APPLICATION_ID ASC, ALERT_GROUP_CONFIG_ID ASC, CREATED_DATE
                              DESC) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.EMAIL_CONFIG
(
    ID            NUMBER DEFAULT MBMONITOR.EMAIL_CONFIG_SEQ.NEXTVAL NOT NULL
        CONSTRAINT EMAIL_CONFIG_pk
            PRIMARY KEY,
    HOST          VARCHAR2(100),
    PORT          NUMBER,
    USERNAME      VARCHAR2(100),
    PASSWORD      VARCHAR2(100),
    PROTOCOL_TYPE VARCHAR2(10),
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255),
    SECURITY_TYPE VARCHAR2(10),
    EMAIL         VARCHAR2(253),
    INTERVAL_TIME VARCHAR2(20),
    EXECUTED      NUMBER,
    ACTIVE        NUMBER,
    DESCRIPTION   VARCHAR2(300 CHAR)
)
/

CREATE TABLE MBMONITOR.COLLECT_EMAIL_CONFIG
(
    ID                 NUMBER DEFAULT MBMONITOR.COLLECT_EMAIL_CONFIG_SEQ.NEXTVAL NOT NULL
        CONSTRAINT COLLECT_EMAIL_CONFIG_pk
            PRIMARY KEY,
    NAME               VARCHAR2(100),
    RULE_GROUP         NCLOB,
    EMAIL_CONFIG_ID    NUMBER,
    INTERVAL_TIME      VARCHAR2(20),
    SERVICE_ID         VARCHAR2(10),
    APPLICATION_ID     VARCHAR2(10),
    RECIPIENT          VARCHAR2(255 CHAR),
    CONTENT            VARCHAR2(3000 CHAR),
    PRIORITY_CONFIG_ID NUMBER,
    CREATED_DATE       TIMESTAMP(6),
    CREATED_BY         VARCHAR2(255),
    MODIFIED_DATE      TIMESTAMP(6),
    MODIFIED_BY        VARCHAR2(255),
    ACTIVE             NUMBER,
    CONTENT_VALUE      VARCHAR2(3000 CHAR),
    DESCRIPTION        VARCHAR2(300 CHAR),
    CONTENT_TYPE       VARCHAR2(20 CHAR)
)
/

CREATE INDEX MBMONITOR.COLLECT_EMAIL_CONFIG_SERVICE_ID_INDEX
    ON MBMONITOR.COLLECT_EMAIL_CONFIG (SERVICE_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.COLLECT_EMAIL_CONFIG_APPLICATION_ID_INDEX
    ON MBMONITOR.COLLECT_EMAIL_CONFIG (APPLICATION_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.COLLECT_EMAIL_CONFIG_PRIORITY_CONFIG_ID_INDEX
    ON MBMONITOR.COLLECT_EMAIL_CONFIG (PRIORITY_CONFIG_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.COLLECT_EMAIL_CONFIG_EMAIL_CONFIG_ID_INDEX
    ON MBMONITOR.COLLECT_EMAIL_CONFIG (EMAIL_CONFIG_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.FILE_STORAGE
(
    ID              NUMBER(19) DEFAULT MBMONITOR.FILE_STORAGE_SEQ.NEXTVAL NOT NULL
        CONSTRAINT FILE_STORAGE_pk
            PRIMARY KEY,
    PATH            VARCHAR2(500),
    DEPENDENCY_ID   VARCHAR2(50),
    DEPENDENCY_NAME VARCHAR2(50),
    CREATED_DATE    TIMESTAMP(6),
    DELETED_BY      VARCHAR2(255),
    DELETED         NUMBER(1),
    DELETED_DATE    TIMESTAMP(6),
    CREATED_BY      VARCHAR2(255),
    MODIFIED_DATE   TIMESTAMP(6),
    MODIFIED_BY     VARCHAR2(255),
    SIZE            NUMBER(7)
)
/

CREATE INDEX MBMONITOR.FILE_STORAGE_DEPENDENCY_NAME_DEPENDENCY_ID_INDEX
    ON MBMONITOR.FILE_STORAGE (DEPENDENCY_NAME, DEPENDENCY_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.EMAIL_TEMPLATE
(
    ID            NUMBER(19) DEFAULT MBMONITOR.EMAIL_TEMPLATE_SEQ.NEXTVAL NOT NULL
        CONSTRAINT EMAIL_TEMPLATE_pk
            PRIMARY KEY,
    DESCRIPTION   VARCHAR2(300 CHAR),
    SUBJECT       VARCHAR2(150 CHAR),
    NAME          VARCHAR2(50 CHAR),
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255),
    CONTENT       CLOB
)
/

CREATE TABLE MBMONITOR.EMAIL_TEMPLATE_RECEIVER
(
    ID                NUMBER(19) DEFAULT MBMONITOR.EMAIL_TEMPLATE_RECEIVER_SEQ.NEXTVAL NOT NULL
        CONSTRAINT EMAIL_TEMPLATE_RECEIVER_pk
            PRIMARY KEY,
    EMAIL_TEMPLATE_ID NUMBER(20),
    PARTNER_ID        NUMBER(20),
    ADDRESS           VARCHAR2(255),
    TYPE              VARCHAR2(10),
    CREATED_DATE      TIMESTAMP(6),
    CREATED_BY        VARCHAR2(255),
    MODIFIED_DATE     TIMESTAMP(6),
    MODIFIED_BY       VARCHAR2(255)
)
/

CREATE INDEX MBMONITOR.EMAIL_TEMPLATE_RECEIVER_EMAIL_TEMPLATE_ID_INDEX
    ON MBMONITOR.EMAIL_TEMPLATE_RECEIVER (EMAIL_TEMPLATE_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.EMAIL_TEMPLATE_RECEIVER_PARTNER_ID_INDEX
    ON MBMONITOR.EMAIL_TEMPLATE_RECEIVER (PARTNER_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.ALERT_GROUP_CONFIG_DEPENDENCY
(
    ID                    NUMBER DEFAULT MBMONITOR.ALERT_GROUP_CONFIG_DEPENDENCY_SEQ.NEXTVAL NOT NULL
        CONSTRAINT ALERT_GROUP_CONFIG_SERVICE_pk
            PRIMARY KEY,
    DEPENDENCE_ID         VARCHAR2(10)                                                       NOT NULL,
    CREATED_DATE          TIMESTAMP(6),
    CREATED_BY            VARCHAR2(255),
    MODIFIED_DATE         TIMESTAMP(6),
    MODIFIED_BY           VARCHAR2(255),
    ALERT_GROUP_CONFIG_ID NUMBER                                                             NOT NULL,
    TYPE                  VARCHAR2(20)
)
/

CREATE INDEX MBMONITOR.ALERT_GROUP_CONFIG_DEPENDENCY_ALERT_GROUP_CONFIG_ID_TYPE_DEPENDENCE_ID_INDEX
    ON MBMONITOR.ALERT_GROUP_CONFIG_DEPENDENCY (ALERT_GROUP_CONFIG_ID, TYPE) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.ALERT_GROUP_CONFIG_DEPENDENCY_ALERT_GROUP_CONFIG_ID_INDEX
    ON MBMONITOR.ALERT_GROUP_CONFIG_DEPENDENCY (ALERT_GROUP_CONFIG_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.ALERT_GROUP_CONFIG_CONDITION
(
    ID                    NUMBER DEFAULT MBMONITOR.ALERT_GROUP_CONFIG_CONDITION_SEQ.NEXTVAL NOT NULL
        CONSTRAINT ALERT_GROUP_CONFIG_CONDITION_pk
            PRIMARY KEY,
    ALERT_GROUP_CONFIG_ID NUMBER                                                            NOT NULL,
    RULE_GROUP            CLOB,
    CUSTOM_OBJECT_ID      NUMBER,
    CREATED_DATE          TIMESTAMP(6),
    CREATED_BY            VARCHAR2(255),
    MODIFIED_DATE         TIMESTAMP(6),
    MODIFIED_BY           VARCHAR2(255)
)
/

CREATE INDEX MBMONITOR.ALERT_GROUP_CONFIG_CONDITION_ALERT_GROUP_CONFIG_ID_INDEX
    ON MBMONITOR.ALERT_GROUP_CONFIG_CONDITION (ALERT_GROUP_CONFIG_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.ALERT_GROUP_CONFIG_CONDITION_CUSTOM_OBJECT_ID_INDEX
    ON MBMONITOR.ALERT_GROUP_CONFIG_CONDITION (CUSTOM_OBJECT_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.DATABASE_CONNECTION
(
    ID                     NUMBER(19) DEFAULT MBMONITOR.DATABASE_CONNECTION_SEQ.NEXTVAL NOT NULL
        PRIMARY KEY,
    NAME                   VARCHAR2(100 CHAR),
    TYPE                   VARCHAR2(50),
    HOST                   VARCHAR2(254),
    PORT                   NUMBER,
    SID                    VARCHAR2(50),
    SERVICE_NAME           VARCHAR2(50),
    DESCRIPTION            VARCHAR2(1000),
    IS_ACTIVE              NUMBER(1)  DEFAULT 1                                         NOT NULL
        CHECK (IS_ACTIVE IN (0, 1)),
    CREATED_DATE           TIMESTAMP(6),
    MODIFIED_DATE          TIMESTAMP(6),
    CREATED_BY             VARCHAR2(255),
    MODIFIED_BY            VARCHAR2(255),
    ORACLE_CONNECTION_TYPE VARCHAR2(100),
    USER_NAME              VARCHAR2(100),
    PASSWORD               VARCHAR2(100)
)
/

CREATE TABLE MBMONITOR.DATABASE_COLLECT
(
    ID                    NUMBER    DEFAULT MBMONITOR.DATABASE_COLLECT_SEQ.NEXTVAL NOT NULL
        PRIMARY KEY,
    NAME                  VARCHAR2(50),
    DESCRIPTION           VARCHAR2(1000),
    CONNECTION_ID         NUMBER                                                   NOT NULL,
    SQL_COMMAND           CLOB,
    CREATED_DATE_FIELD    VARCHAR2(30),
    ALERT_ID_FIELD        VARCHAR2(30),
    INTERVAL              NUMBER,
    SERVICE_NAME_TYPE     VARCHAR2(50),
    SERVICE_ID            VARCHAR2(50),
    SERVICE_MAP_VALUE     VARCHAR2(50),
    APPLICATION_TYPE      VARCHAR2(50),
    APPLICATION_ID        VARCHAR2(50),
    APPLICATION_MAP_VALUE VARCHAR2(50),
    PRIORITY_TYPE         VARCHAR2(50),
    PRIORITY_ID           NUMBER,
    PRIORITY_MAP_VALUE    VARCHAR2(50),
    CONTACT_TYPE          VARCHAR2(50),
    CONTACT_MAP_VALUE     VARCHAR2(50),
    CONTACT_CUSTOM_VALUE  VARCHAR2(255 CHAR),
    IS_ACTIVE             NUMBER(1) DEFAULT 1                                      NOT NULL
        CHECK (IS_ACTIVE IN (0, 1)),
    CREATED_DATE          TIMESTAMP(6),
    CREATED_BY            VARCHAR2(255),
    MODIFIED_DATE         TIMESTAMP(6),
    MODIFIED_BY           VARCHAR2(255),
    ALERT_MAP_VALUE       VARCHAR2(50)
)
/

CREATE INDEX MBMONITOR.DATABASE_COLLECT_CONNECTION_ID_INDEX
    ON MBMONITOR.DATABASE_COLLECT (CONNECTION_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.DATABASE_COLLECT_APPLICATION_ID_INDEX
    ON MBMONITOR.DATABASE_COLLECT (APPLICATION_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.DATABASE_COLLECT_PRIORITY_ID_INDEX
    ON DATABASE_COLLECT (PRIORITY_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.DATABASE_COLLECT_SERVICE_ID_INDEX
    ON DATABASE_COLLECT (SERVICE_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.DATABASE_COLLECT_TEMP
(
    ID                  NUMBER DEFAULT MBMONITOR.DATABASE_COLLECT_TEMP_SEQ.NEXTVAL NOT NULL
        CONSTRAINT DATABASE_COLLECT_TEMP_pk
            PRIMARY KEY,
    ALERT_COLLECT_ID    VARCHAR2(2000),
    DATABASE_COLLECT_ID NUMBER,
    ALERT_COLLECT_DATE  TIMESTAMP(6),
    CREATED_DATE        TIMESTAMP(6),
    MODIFIED_DATE       TIMESTAMP(6),
    CREATED_BY          VARCHAR2(255),
    MODIFIED_BY         VARCHAR2(255)
)
/

CREATE INDEX MBMONITOR.DATABASE_COLLECT_TEMP_DATABASE_COLLECT_ID_INDEX
    ON MBMONITOR.DATABASE_COLLECT_TEMP (DATABASE_COLLECT_ID) TABLESPACE INDEXS
/

CREATE INDEX MBMONITOR.DATABASE_COLLECT_TEMP_ALERT_COLLECT_ID_INDEX
    ON MBMONITOR.DATABASE_COLLECT_TEMP (ALERT_COLLECT_ID) TABLESPACE INDEXS
/

CREATE TABLE MBMONITOR.JOB_HISTORY
(
    ID            NUMBER(22,
                      0) DEFAULT MBMONITOR.JOB_HISTORY_SEQ.NEXTVAL NOT NULL ENABLE PRIMARY KEY,
    JOB_TYPE      VARCHAR(50),
    JOB_ID        NUMBER(22,
                      0),
    TOTAL         NUMBER(22,
                      0),
    SUCCESS       NUMBER(22,
                      0),
    START_DATE    TIMESTAMP(6),
    END_DATE      TIMESTAMP(6),
    DESCRIPTION   CLOB,
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255)
)


/
CREATE INDEX MBMONITOR.JOB_HISTORY_JOB_ID_INDEX ON MBMONITOR.JOB_HISTORY (JOB_ID) TABLESPACE INDEXS

/
CREATE TABLE MBMONITOR.JOB_HISTORY_DETAIL
(
    ID            NUMBER(22,
                      0) DEFAULT MBMONITOR.JOB_HISTORY_DETAIL_SEQ.NEXTVAL NOT NULL ENABLE PRIMARY KEY,
    HISTORY_ID    number(22, 0)                                           NOT NULL,
    ALERT_ID      number(22, 0),
    STATUS        VARCHAR(50),
    DESCRIPTION   CLOB,
    CREATED_DATE  TIMESTAMP(6),
    CREATED_BY    VARCHAR2(255),
    MODIFIED_DATE TIMESTAMP(6),
    MODIFIED_BY   VARCHAR2(255)
)

/
CREATE INDEX MBMONITOR.JOB_HISTORY_DETAIL_HISTORY_ID_INDEX ON MBMONITOR.JOB_HISTORY_DETAIL (HISTORY_ID) TABLESPACE INDEXS
/
CREATE INDEX MBMONITOR.JOB_HISTORY_DETAIL_ALERT_ID_INDEX ON MBMONITOR.JOB_HISTORY_DETAIL (ALERT_ID) TABLESPACE INDEXS

/
INSERT INTO MBMONITOR.SYS_USER(IS_ACTIVE, EMAIL, USERNAME, IS_ADMIN)
VALUES (1, '<EMAIL>', 'caunv1', 1);
