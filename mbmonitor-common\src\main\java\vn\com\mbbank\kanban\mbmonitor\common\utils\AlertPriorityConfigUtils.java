package vn.com.mbbank.kanban.mbmonitor.common.utils;

import java.util.List;
import java.util.Optional;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import vn.com.mbbank.kanban.mbmonitor.common.constants.AlertPriorityConfigConstants;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;

/**
 * AlertPriorityConfigUtils.
 */
public class AlertPriorityConfigUtils {

  private AlertPriorityConfigUtils() {
  }

  /**
   * find PriorityConfig with highest priority.
   *
   * @param priorityConfigs list of priorityConfigs.
   * @return AlertPriorityConfigEntity.
   */
  public static Optional<AlertPriorityConfigEntity> getHighestPositionPriorityConfig(
      List<AlertPriorityConfigEntity> priorityConfigs) {
    if (CollectionUtils.isEmpty(priorityConfigs)) {
      return Optional.empty();
    }
    return Optional.of(
        priorityConfigs.stream().reduce(priorityConfigs.get(0), (highestConfig, currentConfig) -> {
          if (AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_ID.equals(
              currentConfig.getId())) {
            return highestConfig;
          }
          if (AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_ID.equals(
              highestConfig.getId())) {
            return currentConfig;
          }
          return NumberUtils.compare(highestConfig.getPosition(), currentConfig.getPosition()) < 0
              ?
              highestConfig :
              currentConfig;
        }));
  }
}
