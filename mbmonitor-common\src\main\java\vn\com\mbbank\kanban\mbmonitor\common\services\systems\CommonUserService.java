package vn.com.mbbank.kanban.mbmonitor.common.services.systems;

import java.util.Optional;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SysUserResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysUserEntity;

/**
 * User service for common.
 */
public interface CommonUserService {
  /**
   * Find user by userName or create new user if it's not exists.
   *
   * @param userName userName of user
   * @return user details
   */
  SysUserResponse findOrCreateNew(String userName);

  /**
   * find by username.
   *
   * @param userName roleIds
   * @return user.
   */
  Optional<SysUserEntity> findByUserName(String userName);
}
