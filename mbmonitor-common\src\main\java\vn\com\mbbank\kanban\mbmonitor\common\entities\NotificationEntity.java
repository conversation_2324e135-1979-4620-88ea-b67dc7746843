package vn.com.mbbank.kanban.mbmonitor.common.entities;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationTypeEnum;

@Data
@Entity
@Table(name = TableName.NOTIFICATION)
@EqualsAndHashCode(callSuper = true)
public class NotificationEntity extends BaseEntity<String> {

  @Id
  @Column(name = "ID")
  private String id;

  @Column(name = "TITLE")
  private String title;

  @Column(name = "CONTENT")
  private String content;

  @Column(name = "TYPE")
  @Enumerated(EnumType.STRING)
  private NotificationTypeEnum type;

  @Column(name = "USER_NAME")
  private String userName;

  @Column(name = "SOURCE_ID")
  private String sourceId;

  @Column(name = "SOURCE_TYPE")
  @Enumerated(EnumType.STRING)
  private NotificationSourceTypeEnum sourceType;

  @Column(name = "IS_READ")
  private Boolean isRead;

  @Column(name = "READ_DATE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date readDate;

  @Override
  public String getId() {
    return id;
  }
}