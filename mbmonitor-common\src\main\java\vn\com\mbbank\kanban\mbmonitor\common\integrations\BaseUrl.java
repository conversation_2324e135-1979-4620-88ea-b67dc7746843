package vn.com.mbbank.kanban.mbmonitor.common.integrations;

import org.springframework.web.util.UriComponentsBuilder;
import vn.com.mbbank.kanban.mbmonitor.common.configs.KanbanPropertyConfigUtils;

/**
 * Base URL.
 */
public abstract class BaseUrl {
  public final String path;

  /**
   * Constuctor.
   *
   * @param path path
   */
  public BaseUrl(String path) {
    this.path = path;

  }

  /**
   * get Base url.
   *
   * @return base monitor url
   */
  public static String getBaseUrl() {
    return KanbanPropertyConfigUtils.getProperty("mbmonitor.internal.url",
        KanbanPropertyConfigUtils.getProperty("mbmonitor.url", ""));

  }

  @Override
  public String toString() {
    return UriComponentsBuilder.newInstance().pathSegment(path).toUriString();
  }

}
