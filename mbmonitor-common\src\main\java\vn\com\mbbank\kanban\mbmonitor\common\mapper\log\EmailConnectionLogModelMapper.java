package vn.com.mbbank.kanban.mbmonitor.common.mapper.log;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.EmailConnectionLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailConfigEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface EmailConnectionLogModelMapper extends
    KanbanBaseMapper<EmailConnectionLogModel, EmailConfigEntity> {
  EmailConnectionLogModelMapper INSTANCE = Mappers.getMapper(EmailConnectionLogModelMapper.class);
}
