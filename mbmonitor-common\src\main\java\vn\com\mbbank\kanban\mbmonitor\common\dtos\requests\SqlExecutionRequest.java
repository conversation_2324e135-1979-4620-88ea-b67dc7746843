package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;

import jakarta.validation.constraints.NotNull;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;


/**
 * Model use for SqlExecutionRequest.
 */
@Getter
@Setter
@Builder
public class SqlExecutionRequest {
  @NotNull
  @Builder.Default
  private String password = "";
  @NotNull
  @Builder.Default
  private String sqlQuery = "";

  /**
   * Get decoded SQL query.

   * @return sql after decode
   */
  public String getSqlDecoded() {
    byte[] decoded = Base64.getDecoder().decode(getSqlQuery());
    String decodedStr = new String(decoded, StandardCharsets.UTF_8);
    return decodedStr;
  }
}
