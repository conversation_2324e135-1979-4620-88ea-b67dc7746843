package vn.com.mbbank.kanban.mbmonitor.common.entities;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolSecurityTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailProtocolTypeEnum;

@Data
@Entity
@Table(name = "EMAIL_CONFIG")
@EqualsAndHashCode(callSuper = true)
public class EmailConfigEntity extends BaseEntity<Long> {

  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "EMAIL_CONFIG_SEQ", sequenceName = "EMAIL_CONFIG_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "EMAIL_CONFIG_SEQ")
  private Long id;

  @Column(name = "HOST", nullable = false)
  private String host;

  @Column(name = "DESCRIPTION")
  private String description;

  @Column(name = "PROTOCOL_TYPE", nullable = false)
  @Enumerated(EnumType.STRING)
  private EmailProtocolTypeEnum protocolType;

  @Column(name = "SECURITY_TYPE", nullable = false)
  @Enumerated(EnumType.STRING)
  private EmailProtocolSecurityTypeEnum securityType;

  @Column(name = "ACTIVE")
  private boolean active;

  @Column(name = "EXECUTED")
  private boolean executed;

  @Column(name = "INTERVAL_TIME", nullable = false)
  private Long intervalTime;

  @Column(name = "PORT")
  private Long port;

  @Column(name = "USERNAME")
  private String username;

  @Column(name = "EMAIL")
  private String email;

  @Column(name = "PASSWORD")
  private String password;

  @Override
  public Long getId() {
    return id;
  }
}