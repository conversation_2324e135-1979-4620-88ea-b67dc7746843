package vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls;

import vn.com.mbbank.kanban.mbmonitor.common.configs.KanbanPropertyConfigUtils;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.BaseUrl;

/**
 * External database URL.
 */
public class ExternalDatabaseUrl extends BaseUrl {
  /**
   * Demo for ExternalDatabaseUrl.
   *
   * @param path Path
   */
  public ExternalDatabaseUrl(String path) {
    super(path);
  }

  /**
   * Load url from properties.
   *
   * @param baseApi baseApi
   * @return url full
   */
  public static String getUrl(String baseApi) {
    return KanbanPropertyConfigUtils.getProperty("monitor.external.database.url",
        getBaseUrl() + "api/external-database" + baseApi);
  }

  public static final String VERSION = "/v1";
  public static final String DATABASE_CONNECTION = VERSION + "/admin/database-connections";

  public static final String DATABASE_COLLECT = VERSION + "/admin/database-collects";

  public static final String GROUP_QUERY_SQL_URL = VERSION + "/admin/group-query-sqls";
  public static final String QUERY_SQL_URL = VERSION + "/admin/query-sqls";


}
