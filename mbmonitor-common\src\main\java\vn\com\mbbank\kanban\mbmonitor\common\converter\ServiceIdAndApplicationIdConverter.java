package vn.com.mbbank.kanban.mbmonitor.common.converter;

import jakarta.persistence.AttributeConverter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.springframework.util.CollectionUtils;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;

/**
 * ServiceIdAndApplicationIdConverter.
 */
public class ServiceIdAndApplicationIdConverter
    implements AttributeConverter<List<String>, String> {

  /**
   * convert attributes to string.
   *
   * @param attributes list of attribute
   * @return string.
   */
  @Override
  public String convertToDatabaseColumn(List<String> attributes) {
    if (CollectionUtils.isEmpty(attributes)) {
      return "";
    }
    return String.join(CommonConstants.DEFAULT_DELIMITER, attributes);
  }

  /**
   * convert string to list attribute.
   *
   * @param dbData the string mapping field
   * @return list of attribute.
   */
  @Override
  public List<String> convertToEntityAttribute(String dbData) {
    if (StringUtils.isBlank(dbData)) {
      return new ArrayList<>();
    }
    return Arrays.stream(dbData.split(CommonConstants.DEFAULT_DELIMITER)).toList();
  }
}
