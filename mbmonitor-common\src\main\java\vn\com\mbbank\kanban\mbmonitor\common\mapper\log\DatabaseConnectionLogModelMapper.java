package vn.com.mbbank.kanban.mbmonitor.common.mapper.log;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.DatabaseConnectionLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseConnectionEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DatabaseConnectionLogModelMapper extends
    KanbanBaseMapper<DatabaseConnectionLogModel, DatabaseConnectionEntity> {
  DatabaseConnectionLogModelMapper INSTANCE = Mappers.getMapper(DatabaseConnectionLogModelMapper.class);
}
