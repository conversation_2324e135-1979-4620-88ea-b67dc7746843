package vn.com.mbbank.kanban.mbmonitor.common.dtos.models;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CustomObjectTypeEnum;

/**
 * EmailModel for CustomObjectModel config.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomObjectModel {


  private Long id;

  private String name;

  private String description;

  private CustomObjectTypeEnum type;

  private String regex;

  private Integer fromIndex;

  private Integer toIndex;

  private String fromKeyword;

  private String toKeyword;
}