package vn.com.mbbank.kanban.mbmonitor.common.dtos.requests;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;

/**
 * Model request service to create or update service.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class QuerySqlRequest {
  Long id;
  @Size(max = CommonConstants.COMMON_QUERY_SQL_NAME_MAX_LENGTH)
  @Size(min = 1)
  String name;
  @Size(max = CommonConstants.COMMON_DESCRIPTION_MAX_LENGTH)
  String description;
  @NotNull
  Long databaseConnectionId;
  @NotNull
  Long groupQuerySqlId;
  @NotNull
  String command;
}
