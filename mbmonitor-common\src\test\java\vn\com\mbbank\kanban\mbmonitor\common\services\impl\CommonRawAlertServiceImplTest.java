package vn.com.mbbank.kanban.mbmonitor.common.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonApplicationService;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonServiceService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/20/2025
 */
class CommonRawAlertServiceImplTest {
  @Mock
  CommonServiceService commonServiceService;
  @Mock
  CommonApplicationService commonApplicationService;
  @Mock
  CommonAlertPriorityConfigService commonAlertPriorityConfigService;
  @InjectMocks
  CommonRawAlertServiceImpl commonRawAlertServiceImpl;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void createRawData_case_success() {
    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("id");
    serviceEntity.setName("service");
    when(commonServiceService.findAllByNameIgnoreCaseInOrServiceIdIn(any(), any())).thenReturn(List.of(serviceEntity));

    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setServiceId("id");
    applicationEntity.setId("id");
    applicationEntity.setName("application");

    when(commonApplicationService.findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(any(), any(), any())).thenReturn(List.of(applicationEntity));
    // mock priority
    AlertPriorityConfigEntity alertPriorityConfigEntity = new AlertPriorityConfigEntity();
    alertPriorityConfigEntity.setId(1L);
    alertPriorityConfigEntity.setName("name");
    when(commonAlertPriorityConfigService.findAllById(any())).thenReturn(List.of(alertPriorityConfigEntity));

    AlertBaseModel alertBaseModel = new AlertBaseModel();
    alertBaseModel.setServiceId("id");
    alertBaseModel.setApplicationId("id");
    alertBaseModel.setApplicationNameRaw("application");
    alertBaseModel.setServiceNameRaw("service");
    alertBaseModel.setRecipientRaw("abc");
    alertBaseModel.setPriorityRaw("name");
    alertBaseModel.setAlertPriorityConfigId(1L);
    alertBaseModel.setContentRaw("abnc");
    alertBaseModel.setContent("abc");
    var result =
        commonRawAlertServiceImpl.createRawData(List.of(alertBaseModel),
            AlertSourceTypeEnum.WEBHOOK);
    Assertions.assertEquals("id", result.get(0).getServiceId());

  }

  @Test
  void createRawData_case_service_is_not_null() {
    AlertBaseModel alertBaseModel = new AlertBaseModel();
    alertBaseModel.setServiceId("id");
    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("id");
    when(commonServiceService.findAllByNameIgnoreCaseInOrServiceIdIn(any(), any())).thenReturn(List.of(serviceEntity));
    var result =
        commonRawAlertServiceImpl.createRawData(List.of(alertBaseModel),
            AlertSourceTypeEnum.WEBHOOK);
    Assertions.assertEquals("id", result.get(0).getServiceId());

  }

  @Test
  void createRawData_case_service_is_not_null_and_not_exist_database() {
    AlertBaseModel alertBaseModel = new AlertBaseModel();
    alertBaseModel.setServiceId("Id");

    when(commonServiceService.findAllByNameIgnoreCaseInOrServiceIdIn(any(), any())).thenReturn(new ArrayList<>());
    var result =
        commonRawAlertServiceImpl.createRawData(List.of(alertBaseModel),
            AlertSourceTypeEnum.WEBHOOK);
    Assertions.assertEquals(ErrorCode.SERVICE_NOT_FOUND.getMessage(),
        result.get(0).getDescription());
  }

  @Test
  void createRawData_case_service_is_null_and_service_raw_exists() {
    ServiceEntity service = new ServiceEntity();
    service.setId("id");
    service.setName("name");
    when(commonServiceService.findAllByNameIgnoreCaseInOrServiceIdIn(any(), any())).thenReturn(
        List.of(service));
    AlertBaseModel alertBaseModel = new AlertBaseModel();
    alertBaseModel.setServiceNameRaw("name");
    var result =
        commonRawAlertServiceImpl.createRawData(List.of(alertBaseModel),
            AlertSourceTypeEnum.WEBHOOK);
    Assertions.assertEquals("id", result.get(0).getServiceId());
  }

  @Test
  void createRawData_case_service_is_null_and_service_raw_not_exists() {
    AlertBaseModel alertBaseModel = new AlertBaseModel();
    when(commonServiceService.findAllByNameIgnoreCaseInOrServiceIdIn(any(), any())).thenReturn(
        new ArrayList<>());
    var result =
        commonRawAlertServiceImpl.createRawData(List.of(alertBaseModel),
            AlertSourceTypeEnum.WEBHOOK);
    Assertions.assertEquals(ErrorCode.SERVICE_NOT_FOUND.getMessage(),
        result.get(0).getDescription());
  }


  @Test
  void createRawData_case_application_is_not_null() {
    AlertBaseModel alertBaseModel = new AlertBaseModel();
    alertBaseModel.setServiceId("id");
    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("id");
    when(commonServiceService.findAllByNameIgnoreCaseInOrServiceIdIn(any(), any())).thenReturn(List.of(serviceEntity));
    alertBaseModel.setApplicationId("id");
    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setId("id");
    when(commonApplicationService.findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(any(), any(), any())).thenReturn(List.of(applicationEntity));
    var result =
        commonRawAlertServiceImpl.createRawData(List.of(alertBaseModel),
            AlertSourceTypeEnum.WEBHOOK);
    Assertions.assertEquals("id", result.get(0).getApplicationId());

  }

  @Test
  void createRawData_case_application_is_not_null_and_not_exist_database() {
    AlertBaseModel alertBaseModel = new AlertBaseModel();
    alertBaseModel.setServiceId("id");
    alertBaseModel.setApplicationId("id");
    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("id");
    when(commonServiceService.findAllByNameIgnoreCaseInOrServiceIdIn(any(), any())).thenReturn(
        List.of(serviceEntity));
    when(
        commonApplicationService.findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(
            any(), any(), any())).thenReturn(new ArrayList<>());
    var result =
        commonRawAlertServiceImpl.createRawData(List.of(alertBaseModel),
            AlertSourceTypeEnum.WEBHOOK);
    Assertions.assertEquals(ErrorCode.APPLICATION_NOT_FOUND.getMessage(),
        result.get(0).getDescription());
  }

  @Test
  void createRawData_case_application_is_null_and_application_raw_exists() {
    AlertBaseModel alertBaseModel = new AlertBaseModel();
    ApplicationEntity application = new ApplicationEntity();
    application.setId("id");
    application.setName("abc");
    application.setServiceId("id");
    alertBaseModel.setServiceId("id");
    alertBaseModel.setApplicationNameRaw("abc");
    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("id");
    serviceEntity.setName("abc");
    when(commonServiceService.findAllByNameIgnoreCaseInOrServiceIdIn(any(), any())).thenReturn(
        List.of(serviceEntity));
    when(
        commonApplicationService.findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(
            any(), any(), any())).thenReturn(
        List.of(application));
    var result =
        commonRawAlertServiceImpl.createRawData(List.of(alertBaseModel),
            AlertSourceTypeEnum.WEBHOOK);
    Assertions.assertEquals("id", result.get(0).getApplicationId());
  }

  @Test
  void createRawData_case_application_is_null_and_application_raw_not_exists() {
    AlertBaseModel alertBaseModel = new AlertBaseModel();
    alertBaseModel.setServiceId("id");
    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("id");
    when(commonServiceService.findAllByNameIgnoreCaseInOrServiceIdIn(any(), any())).thenReturn(
        List.of(serviceEntity));
    alertBaseModel.setApplicationId("id");

    when(
        commonApplicationService.findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(
            any(), any(), any())).thenReturn(
        new ArrayList<>());
    var result =
        commonRawAlertServiceImpl.createRawData(List.of(alertBaseModel),
            AlertSourceTypeEnum.WEBHOOK);
    Assertions.assertEquals(ErrorCode.APPLICATION_NOT_FOUND.getMessage(),
        result.get(0).getDescription());
  }

  @Test
  void createRawData_case_priority_is_not_null() {
    AlertBaseModel alertBaseModel = new AlertBaseModel();
    alertBaseModel.setServiceId("id");
    alertBaseModel.setAlertPriorityConfigId(1L);
    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("id");
    when(commonServiceService.findAllByNameIgnoreCaseInOrServiceIdIn(any(), any())).thenReturn(List.of(serviceEntity));
    alertBaseModel.setApplicationId("id");
    when(commonApplicationService.findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(any(), any(), any())).thenReturn(List.of(new ApplicationEntity()));
    when(commonAlertPriorityConfigService.findById(any())).thenReturn(
        new AlertPriorityConfigEntity());
    var result =
        commonRawAlertServiceImpl.createRawData(List.of(alertBaseModel),
            AlertSourceTypeEnum.WEBHOOK);
    Assertions.assertEquals("id", result.get(0).getApplicationId());

  }

  @Test
  void createRawData_case_priority_is_not_null_and_not_exist_database() {
    AlertBaseModel alertBaseModel = new AlertBaseModel();
    alertBaseModel.setServiceId("id");
    alertBaseModel.setAlertPriorityConfigId(1L);
    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("id");
    when(commonServiceService.findAllByNameIgnoreCaseInOrServiceIdIn(any(), any())).thenReturn(
        List.of(serviceEntity));
    alertBaseModel.setApplicationId("id");
    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setId("id");
    applicationEntity.setServiceId("id");
    alertBaseModel.setApplicationId("id");
    when(
        commonApplicationService.findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(
            any(), any(), any())).thenReturn(List.of(applicationEntity));
    when(commonAlertPriorityConfigService.findAllById(any())).thenReturn(null);
    var result =
        commonRawAlertServiceImpl.createRawData(List.of(alertBaseModel),
            AlertSourceTypeEnum.WEBHOOK);
    Assertions.assertEquals(ErrorCode.ALERT_PRIORITY_CONFIG_NOT_FOUND.getMessage(),
        result.get(0).getDescription());

  }

  @Test
  void createRawData_case_alert_is_not_null() {
    AlertBaseModel alertBaseModel = new AlertBaseModel();
    alertBaseModel.setContentRaw("id");
    alertBaseModel.setServiceId("id");
    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("id");
    when(commonServiceService.findAllByNameIgnoreCaseInOrServiceIdIn(any(), any())).thenReturn(List.of(serviceEntity));
    alertBaseModel.setApplicationId("id");
    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setId("id");
    alertBaseModel.setApplicationId("id");
    when(commonApplicationService.findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(any(), any(), any())).thenReturn(List.of(applicationEntity));
    AlertPriorityConfigEntity alertPriorityConfigEntity = new AlertPriorityConfigEntity();
    alertPriorityConfigEntity.setId(1L);
    alertBaseModel.setAlertPriorityConfigId(1L);
    when(commonAlertPriorityConfigService.findById(any())).thenReturn(alertPriorityConfigEntity);
    var result =
        commonRawAlertServiceImpl.createRawData(List.of(alertBaseModel),
            AlertSourceTypeEnum.WEBHOOK);
    Assertions.assertEquals("id", result.get(0).getApplicationId());

  }


  @Test
  void createRawData_case_alert_is_not_null_branch_raw_null() {
    AlertBaseModel alertBaseModel = new AlertBaseModel();
    alertBaseModel.setContent("Id");
    alertBaseModel.setServiceId("id");
    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("id");
    when(commonServiceService.findAllByNameIgnoreCaseInOrServiceIdIn(any(), any())).thenReturn(List.of(serviceEntity));
    alertBaseModel.setApplicationId("id");
    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setId("id");
    alertBaseModel.setApplicationId("id");
    when(commonApplicationService.findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(any(), any(), any())).thenReturn(List.of(applicationEntity));
    AlertPriorityConfigEntity alertPriorityConfigEntity = new AlertPriorityConfigEntity();
    alertPriorityConfigEntity.setId(1L);
    alertBaseModel.setAlertPriorityConfigId(1L);
    when(commonAlertPriorityConfigService.findAllById(any())).thenReturn(
        List.of(alertPriorityConfigEntity));
    var result =
        commonRawAlertServiceImpl.createRawData(List.of(alertBaseModel),
            AlertSourceTypeEnum.WEBHOOK);
    Assertions.assertEquals("id", result.get(0).getApplicationId());

  }


  @Test
  void createRawData_case_alert_is_null() {
    AlertBaseModel alertBaseModel = new AlertBaseModel();
    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("id");
    alertBaseModel.setServiceId("id");
    when(commonServiceService.findAllByNameIgnoreCaseInOrServiceIdIn(any(), any())).thenReturn(
        List.of(serviceEntity));
    alertBaseModel.setApplicationId("id");
    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setId("id");
    applicationEntity.setServiceId("id");
    alertBaseModel.setApplicationId("id");
    when(
        commonApplicationService.findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(
            any(), any(), any())).thenReturn(List.of(applicationEntity));
    AlertPriorityConfigEntity alertPriorityConfigEntity = new AlertPriorityConfigEntity();
    alertPriorityConfigEntity.setId(1L);
    alertBaseModel.setAlertPriorityConfigId(1L);
    when(commonAlertPriorityConfigService.findAllById(any())).thenReturn(
        List.of(alertPriorityConfigEntity));
    var result =
        commonRawAlertServiceImpl.createRawData(List.of(alertBaseModel),
            AlertSourceTypeEnum.WEBHOOK);
    Assertions.assertEquals(ErrorCode.ALERT_CONTENT_EMPTY.getMessage(),
        result.get(0).getDescription());

  }

  @Test
  void createRawData_case_contact_is_not_null() {
    AlertBaseModel alertBaseModel = new AlertBaseModel();
    alertBaseModel.setContentRaw("id");
    alertBaseModel.setRecipientRaw("ac");
    alertBaseModel.setServiceId("id");
    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("id");
    when(commonServiceService.findAllByNameIgnoreCaseInOrServiceIdIn(any(), any())).thenReturn(List.of(serviceEntity));
    alertBaseModel.setApplicationId("id");
    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setId("id");
    alertBaseModel.setApplicationId("id");
    when(commonApplicationService.findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(any(), any(), any())).thenReturn(List.of(applicationEntity));
    AlertPriorityConfigEntity alertPriorityConfigEntity = new AlertPriorityConfigEntity();
    alertPriorityConfigEntity.setId(1L);
    alertBaseModel.setAlertPriorityConfigId(1L);
    when(commonAlertPriorityConfigService.findAllById(any())).thenReturn(
        List.of(alertPriorityConfigEntity));
    var result =
        commonRawAlertServiceImpl.createRawData(List.of(alertBaseModel),
            AlertSourceTypeEnum.WEBHOOK);
    Assertions.assertEquals("id", result.get(0).getApplicationId());

  }

  @Test
  void createRawData_case_contact_is_not_null_branch_raw_null() {
    AlertBaseModel alertBaseModel = new AlertBaseModel();
    alertBaseModel.setContentRaw("id");
    alertBaseModel.setServiceId("id");
    alertBaseModel.setRecipient("abc");
    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("id");
    when(commonServiceService.findAllByNameIgnoreCaseInOrServiceIdIn(any(), any())).thenReturn(List.of(serviceEntity));
    alertBaseModel.setApplicationId("id");
    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setId("id");
    alertBaseModel.setApplicationId("id");
    when(commonApplicationService.findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(any(), any(), any())).thenReturn(List.of(applicationEntity));
    AlertPriorityConfigEntity alertPriorityConfigEntity = new AlertPriorityConfigEntity();
    alertPriorityConfigEntity.setId(1L);
    alertBaseModel.setAlertPriorityConfigId(1L);
    when(commonAlertPriorityConfigService.findAllById(any())).thenReturn(
        List.of(alertPriorityConfigEntity));
    var result =
        commonRawAlertServiceImpl.createRawData(List.of(alertBaseModel),
            AlertSourceTypeEnum.WEBHOOK);
    Assertions.assertEquals("id", result.get(0).getApplicationId());

  }

  @Test
  void createRawData_case_contact_is_not_null_branch_raw_null_branch_not_equals_serviceId() {
    AlertBaseModel alertBaseModel = new AlertBaseModel();
    alertBaseModel.setContentRaw("id");
    alertBaseModel.setRecipient("abc");
    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("id");
    serviceEntity.setName("abc");
    when(commonServiceService.findAllByNameIgnoreCaseInOrServiceIdIn(any(), any())).thenReturn(List.of(serviceEntity));
    alertBaseModel.setApplicationId("id");
    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setId("id");
    alertBaseModel.setApplicationId("id");
    applicationEntity.setName("anbc");
    applicationEntity.setServiceId("id");
    when(commonApplicationService.findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(any(), any(), any())).thenReturn(List.of(applicationEntity));
    AlertPriorityConfigEntity alertPriorityConfigEntity = new AlertPriorityConfigEntity();
    alertPriorityConfigEntity.setId(1L);
    alertBaseModel.setAlertPriorityConfigId(1L);
    when(commonAlertPriorityConfigService.findAllById(any())).thenReturn(
        List.of(alertPriorityConfigEntity));
    var result =
        commonRawAlertServiceImpl.createRawData(List.of(alertBaseModel),
            AlertSourceTypeEnum.WEBHOOK);
    Assertions.assertEquals(false, result.get(0).getIsValid());

  }

  @Test
  void createRawData_case_contact_is_null() {
    AlertBaseModel alertBaseModel = new AlertBaseModel();
    alertBaseModel.setContentRaw("id");
    alertBaseModel.setServiceId("id");
    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("id");
    when(commonServiceService.findAllByNameIgnoreCaseInOrServiceIdIn(any(), any())).thenReturn(List.of(serviceEntity));
    alertBaseModel.setApplicationId("id");
    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setId("id");
    alertBaseModel.setApplicationId("id");
    when(commonApplicationService.findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(any(), any(), any())).thenReturn(List.of(applicationEntity));
    AlertPriorityConfigEntity alertPriorityConfigEntity = new AlertPriorityConfigEntity();
    alertPriorityConfigEntity.setId(1L);
    alertBaseModel.setAlertPriorityConfigId(1L);
    when(commonAlertPriorityConfigService.findAllById(any())).thenReturn(
        List.of(alertPriorityConfigEntity));
    var result =
        commonRawAlertServiceImpl.createRawData(List.of(alertBaseModel),
            AlertSourceTypeEnum.WEBHOOK);
    Assertions.assertEquals("id", result.get(0).getApplicationId());

  }

  @Test
  void convertIdToRawData_Success() {
    // Arrange
    AlertBaseModel alert = new AlertBaseModel();
    alert.setServiceId("1");
    alert.setApplicationId("2");
    alert.setAlertPriorityConfigId(3L);
    alert.setRecipient("<EMAIL>");
    alert.setContent("Test content");

    ServiceEntity serviceEntity = new ServiceEntity();
    serviceEntity.setId("1");
    serviceEntity.setName("Service Name");

    ApplicationEntity applicationEntity = new ApplicationEntity();
    applicationEntity.setId("2");
    applicationEntity.setName("Application Name");

    AlertPriorityConfigEntity priorityEntity = new AlertPriorityConfigEntity();
    priorityEntity.setId(3L);
    priorityEntity.setName("High Priority");

    when(commonServiceService.findAllByNameIgnoreCaseInOrServiceIdIn(any(), any())).thenReturn(List.of(serviceEntity));
    when(commonApplicationService.findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(any(), any(), any())).thenReturn(List.of(applicationEntity));
    when(commonAlertPriorityConfigService.findAllById(List.of(3L))).thenReturn(
        List.of(priorityEntity));

    // Act
    List<AlertBaseModel> result = commonRawAlertServiceImpl.convertIdToRawData(List.of(alert));

    // Assert
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals("High Priority", result.get(0).getPriorityRaw());
    assertEquals("<EMAIL>", result.get(0).getRecipientRaw());
    assertEquals("Test content", result.get(0).getContent());
  }

  @Test
  void convertIdToRawData_alert_empty() {
    // Act
    List<AlertBaseModel> result = commonRawAlertServiceImpl.convertIdToRawData(new ArrayList<>());
    assertEquals(0, result.size());
  }
}
