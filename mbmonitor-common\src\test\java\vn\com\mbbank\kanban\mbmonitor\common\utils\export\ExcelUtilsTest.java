package vn.com.mbbank.kanban.mbmonitor.common.utils.export;

import org.apache.logging.log4j.util.Strings;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AttributeInfoDto;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.ApplicationResponse;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;
public class ExcelUtilsTest {
  static class TestClass {
    private String field = "testValue";
    private Integer intField = null;

    public TestClass() {
    }

    public TestClass(String field, Integer intField) {
      this.field = field;
      this.intField = intField;
    }
  }

  @InjectMocks
  ExcelUtils excelUtils;

  @Test
  void getFieldValue_WithNullFieldValue_ReturnsEmptyString() throws NoSuchFieldException {
    TestClass testInstance = new TestClass(null, null);
    Field matchingField = TestClass.class.getDeclaredField("intField");
    matchingField.setAccessible(true);
    String result = excelUtils.getFieldValue(matchingField, testInstance).toString();
    assertEquals("", result, "Expected an empty string when the field value is null");
  }


  @Test
  void getFieldValue_WithIllegalAccess_ShouldReturnEmptyString() throws NoSuchFieldException {
    TestClass instance = new TestClass();
    Field matchingField = TestClass.class.getDeclaredField("field");
    Field fieldValueIfValueNull = TestClass.class.getDeclaredField("intField");

    matchingField.setAccessible(true);

    assertEquals(Strings.EMPTY, excelUtils.getFieldValue(fieldValueIfValueNull, instance));

    try {
      matchingField.setAccessible(false);
      String result = matchingField.get(instance).toString();
      assertEquals("testValue", result);
    } catch (IllegalAccessException e) {
      assertFalse(true);
    }
  }

  @Test
  public void transformDataExport_matchingField_notPresent() {
    List<AttributeInfoDto> rowData1 = new ArrayList<>();
    List<ApplicationResponse> applicationResponses = new ArrayList<>();

    rowData1.add(new AttributeInfoDto("test", "row1_name1", ""));
    List<List<AttributeInfoDto>> transformed = excelUtils.transformDataExport(applicationResponses, rowData1);

    assertEquals(0, transformed.size());

    applicationResponses.add(ApplicationResponse.builder()
        .id("appId")
        .serviceName("serviceName")
        .description("description")
        .name("appName")
        .build());

    List<List<AttributeInfoDto>> transformedData = new ArrayList<>();
    transformedData.add(rowData1);
    transformed = excelUtils.transformDataExport(applicationResponses, rowData1);

    assertEquals("", transformed.get(0).get(0).getValue());
  }

  @Test
  public void transformDataExport_sucess() {
    List<AttributeInfoDto> rowData1 = new ArrayList<>();
    List<ApplicationResponse> applicationResponses = new ArrayList<>();

    rowData1.add(new AttributeInfoDto("serviceName", "row1_name1", ""));
    List<List<AttributeInfoDto>> transformed = excelUtils.transformDataExport(applicationResponses, rowData1);

    assertEquals(0, transformed.size());

    applicationResponses.add(ApplicationResponse.builder()
        .id("appId")
        .serviceName("serviceName")
        .description("description")
        .name("appName")
        .build());

    List<List<AttributeInfoDto>> transformedData = new ArrayList<>();
    transformedData.add(rowData1);
    transformed = excelUtils.transformDataExport(applicationResponses, rowData1);

    assertEquals("serviceName", transformed.get(0).get(0).getValue());
  }


  @Test
  public void getAllDeclaredFields_EmptyList_success() {
    List<Object> emptyDataInfoList = List.of();
    List<Field> fields = ExcelUtils.getAllDeclaredFields(emptyDataInfoList);
    assertTrue(fields.isEmpty(), "The field list should be empty when dataInfoList is empty");
  }
  @Test
  void getFieldValue_WithNonNullValue() throws IllegalAccessException {
    Field mockField = mock(Field.class);
    Object instance = new Object();
    when(mockField.get(instance)).thenReturn("Test Value");
    String result = ExcelUtils.getFieldValue(mockField, instance);

    assertEquals("Test Value", result);
  }
  @Test
  void transformDataExport_attributeInfoListNull() {
    List<Object> dataInfoList = List.of(new Object());
    List<List<AttributeInfoDto>> result = ExcelUtils.transformDataExport(dataInfoList, null);
    assertNotNull(result);
  }

  @Test
  void transformDataExport_WithEmptyDataInfoList() {
    List<Object> dataInfoList = List.of(new Object());
    List<AttributeInfoDto> attributeInfoList = new ArrayList<>();
    List<List<AttributeInfoDto>> result = ExcelUtils.transformDataExport(dataInfoList, attributeInfoList);
    assertNotNull(result);
  }

  @Test
  void transformDataExport_WithNullAttributeInfoList() {
    List<Object> dataInfoList = new ArrayList<>();
    dataInfoList.add(new Object());
    List<AttributeInfoDto> attributeInfoList = null;

    List<List<AttributeInfoDto>> result = ExcelUtils.transformDataExport(dataInfoList, attributeInfoList);

    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  void getValueFromInstance_WithClassField() {
    TestParent instance = new TestParent();
    instance.parentField = "parentValue";
    String value = ExcelUtils.getValueFromInstance(instance, "parentField");

    assertEquals("parentValue", value);
  }

  @Test
  void getValueFromInstance_WithClassField_NoSuchField() {
    TestParent instance = new TestParent();
    String value = ExcelUtils.getValueFromInstance(instance, "nonExistentField");

    assertNull(value);
  }

  @Test
  void getValueFromInstance_WithClassField_NullValue() {
    TestParent instance = new TestParent();
    instance.parentField = null;
    String value = ExcelUtils.getValueFromInstance(instance, "parentField");

    assertNull(value);
  }
  @Test
  void getFieldValue_WithNullField() throws IllegalAccessException {
    Field mockField = mock(Field.class);
    Object instance = new Object();
    when(mockField.get(instance)).thenReturn(null);
    String result = ExcelUtils.getFieldValue( mockField, instance);

    assertEquals(Strings.EMPTY, result);
  }
  @Test
  void getValueFromInstance_WithMapValueNull() {
    Map<String, String> map = new HashMap<>();
    map.put("key", "value");
    String result = ExcelUtils.getValueFromInstance(map, "nonExistingKey");
    assertNull( result);
  }

}

class TestParent {
  public String parentField;
}

class TestChild extends TestParent {
  public String childField;
}
