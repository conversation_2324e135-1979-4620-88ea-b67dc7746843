package vn.com.mbbank.kanban.mbmonitor.common.entities;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.EmailReceiverEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/27/2024
 */
@Entity
@Data
@Table(name = TableName.EMAIL_TEMPLATE_RECEIVER)
@EqualsAndHashCode(callSuper = true)
public class EmailTemplateReceiverEntity extends BaseEntity<Long> {

  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "EMAIL_TEMPLATE_RECEIVER_SEQ",
      sequenceName = "EMAIL_TEMPLATE_RECEIVER_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "EMAIL_TEMPLATE_RECEIVER_SEQ")
  private Long id;

  @Column(name = "EMAIL_TEMPLATE_ID")
  private Long emailTemplateId;

  @Column(name = "PARTNER_ID")
  private String partnerId;

  @Column(name = "ADDRESS")
  private String address;

  @Column(name = "TYPE", nullable = false)
  @Enumerated(EnumType.STRING)
  private EmailReceiverEnum type;

  @Override
  public Long getId() {
    return id;
  }
}
