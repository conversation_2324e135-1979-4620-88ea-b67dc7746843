package vn.com.mbbank.kanban.mbmonitor.common.utils.telegram;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/25/2025
 */
class TelegramUtilsTest {
  @Mock
  Set<String> TELEGRAM_ALLOWED_TAGS;
  @InjectMocks
  TelegramUtils telegramUtils;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void plainText() {
    String input = "Hello, world!";
    String expected = "Hello, world!";
    assertEquals(expected, TelegramUtils.sanitizeHtml(input));
  }

  @Test
  void allowedTags() {
    String input = "<b>Bold</b> <i>Italic</i> <a href='https://example.com'>Link</a>";
    String expected = "<b>Bold</b> <i>Italic</i> <a href=\"https://example.com\">Link</a>";
    assertEquals(expected, TelegramUtils.sanitizeHtml(input));
  }

  @Test
  void disallowedTags() {
    String input = "<z>CustomTag</z>";
    String expected = "&lt;z&gt; CustomTag";
    assertEquals(expected, TelegramUtils.sanitizeHtml(input));
  }

  @Test
  void nestedAllowedTags() {
    String input = "<b>Bold <i>and Italic</i></b>";
    String expected = "<b>Bold <i>and Italic</i></b>";
    assertEquals(expected, TelegramUtils.sanitizeHtml(input));
  }

  @Test
  void escapedCharacters() {
    String input = "5 > 3 & 2 < 4";
    String expected = "5 &gt; 3 &amp; 2 &lt; 4";
    assertEquals(expected, TelegramUtils.sanitizeHtml(input));
  }

  @Test
  void nestedDisallowedTags() {
    String input = "<div><b>Allowed</b> <script>alert('XSS');</script></div>";
    String expected = "&lt;div&gt; <b>Allowed</b> &lt;script&gt; alert(&apos;XSS&apos;);";
    assertEquals(expected, TelegramUtils.sanitizeHtml(input));
  }

  @Test
  void enscapHtmlDoctypeElementNotAllow() {
    String input = "<!DOCTYPE html><b>Allowed</b> <script>alert('XSS');</script></div>";
    String expected = "&lt;!DOCTYPE html&gt;<b>Allowed</b> &lt;script&gt; alert(&apos;XSS&apos;);";
    assertEquals(expected, TelegramUtils.sanitizeHtml(input));
  }
}
