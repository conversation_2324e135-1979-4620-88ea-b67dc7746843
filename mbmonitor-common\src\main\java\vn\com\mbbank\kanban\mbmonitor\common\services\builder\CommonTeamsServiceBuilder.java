package vn.com.mbbank.kanban.mbmonitor.common.services.builder;

import lombok.RequiredArgsConstructor;
import vn.com.mbbank.kanban.core.utils.KanbanApplicationConfigUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.TeamsConfigModel;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonTeamsService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/14/2025
 */
@RequiredArgsConstructor
public class CommonTeamsServiceBuilder {
  private final TeamsConfigModel configModel;

  /**
   * Get instance team services.
   *
   * @return CommonTeamsService
   */
  public  CommonTeamsService getInstance() {
    var commonTeamsService =  KanbanApplicationConfigUtils.getBean(CommonTeamsService.class);
    commonTeamsService.init(configModel);
    return commonTeamsService;
  }

}
