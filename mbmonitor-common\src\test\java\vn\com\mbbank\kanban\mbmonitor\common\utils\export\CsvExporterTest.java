package vn.com.mbbank.kanban.mbmonitor.common.utils.export;

import org.apache.commons.csv.CSVPrinter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AttributeInfoDto;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.ExportFileDto;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class CsvExporterTest {

  @InjectMocks
  CsvExporter csvExporter;

  @Mock
  private ExportFileDto exportFileDto;

  @Mock
  private CSVPrinter csvPrinter;

  private ByteArrayOutputStream outputStream;
  @Mock
  private OutputStreamWriter writer;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    outputStream = new ByteArrayOutputStream();
  }

  @Test
  void export_success() throws IOException {
    List<String> data = Arrays.asList("test1", "test2");
    List<AttributeInfoDto> attributes = Arrays.asList(new AttributeInfoDto());
    List<List<AttributeInfoDto>> transformedData = Arrays.asList();

    when(exportFileDto.getAttributes()).thenReturn(attributes);

    try (MockedStatic<ExcelUtils> excelUtilsMock = mockStatic(ExcelUtils.class)) {
      excelUtilsMock.when(() -> ExcelUtils.transformDataExport(data, attributes))
          .thenReturn(transformedData);

      StreamingResponseBody response = csvExporter.export(data, exportFileDto);
      response.writeTo(outputStream);

      verify(exportFileDto, atLeastOnce()).getAttributes();
    }
  }

  @Test
  void init_WithTitle_success() throws IOException {
    List<AttributeInfoDto> attributes = Arrays.asList(new AttributeInfoDto("id1", "Name1", null));
    List<String> titles = Arrays.asList("Title1", "Title2");

    when(exportFileDto.getAttributes()).thenReturn(attributes);
    when(exportFileDto.getTitle()).thenReturn(titles);

    csvExporter.init(exportFileDto, outputStream);

    verify(exportFileDto, atLeastOnce()).getAttributes();
    verify(exportFileDto, atLeastOnce()).getTitle();
  }

  @Test
  void init_WithoutTitle_success() throws IOException {
    List<AttributeInfoDto> attributes = Arrays.asList(new AttributeInfoDto("id1", "Name1", null));

    when(exportFileDto.getAttributes()).thenReturn(attributes);
    when(exportFileDto.getTitle()).thenReturn(null);

    csvExporter.init(exportFileDto, outputStream);

    verify(exportFileDto, times(1)).getTitle();
    verify(exportFileDto, atLeastOnce()).getAttributes();
    verify(csvPrinter, never()).printRecord(anyString());
  }

  @Test
  void writeBatch_success() throws IOException {
    List<String> batchData = Arrays.asList("data1", "data2");
    List<AttributeInfoDto> attributes = Arrays.asList(new AttributeInfoDto());
    List<List<AttributeInfoDto>> transformedData = Arrays.asList(
        Arrays.asList(AttributeInfoDto.builder().value("va").attributeId("data1").build()),
        Arrays.asList(AttributeInfoDto.builder().value("va").attributeId("data1").build())
    );

    when(exportFileDto.getAttributes()).thenReturn(attributes);

    try (MockedStatic<ExcelUtils> excelUtilsMock = mockStatic(ExcelUtils.class)) {
      excelUtilsMock.when(() -> ExcelUtils.transformDataExport(batchData, attributes))
          .thenReturn(transformedData);

      csvExporter.writeBatch(batchData, exportFileDto, outputStream);

      excelUtilsMock.verify(() -> ExcelUtils.transformDataExport(batchData, attributes));
      verify(exportFileDto, atLeastOnce()).getAttributes();
    }
  }

  @Test
  void finish_success() throws IOException {
    csvExporter.close(outputStream);

    verify(csvPrinter, times(1)).flush();
  }

  @Test
  void export_EmptyData_success() throws IOException {
    List<String> data = Collections.emptyList();
    List<AttributeInfoDto> attributes = Arrays.asList(new AttributeInfoDto());
    List<List<AttributeInfoDto>> transformedData = Collections.emptyList();

    when(exportFileDto.getAttributes()).thenReturn(attributes);

    try (MockedStatic<ExcelUtils> excelUtilsMock = mockStatic(ExcelUtils.class)) {
      excelUtilsMock.when(() -> ExcelUtils.transformDataExport(data, attributes))
          .thenReturn(transformedData);

      StreamingResponseBody response = csvExporter.export(data, exportFileDto);
      response.writeTo(outputStream);

      verify(exportFileDto, atLeastOnce()).getAttributes();
    }
  }

  @Test
  void printerTitle_success() throws IOException {
    List<String> titles = Arrays.asList("Title1", "Title2");
    CsvExporter.printerTitle(titles, csvPrinter);

    verify(csvPrinter, times(1)).printRecord("Title1");
    verify(csvPrinter, times(1)).printRecord("Title2");
    verify(csvPrinter, times(1)).println();
  }

  @Test
  void printerTitle_Empty_success() throws IOException {
    List<String> titles = Collections.emptyList();
    CsvExporter.printerTitle(titles, csvPrinter);

    verify(csvPrinter, never()).printRecord(anyString());
    verify(csvPrinter, never()).println();
  }

  @Test
  void printerHeader_success() throws IOException {
    List<AttributeInfoDto> attributes = Arrays.asList(
        new AttributeInfoDto("attr1", "Name1", null),
        new AttributeInfoDto("attr2", "Name2", null)
    );

    CsvExporter.printerHeader(attributes, csvPrinter);

    verify(csvPrinter, times(1)).print("Name1");
    verify(csvPrinter, times(1)).print("Name2");
    verify(csvPrinter, times(1)).println();
  }

  // Assuming printerRow exists in the actual class
  @Test
  void printerRowWithValidAndInvalidDates_success() throws IOException {
    List<AttributeInfoDto> rowData = Arrays.asList(
        AttributeInfoDto.builder().value("2024-10-07 09:44:31.000").build(),
        AttributeInfoDto.builder().value("invalid_date").build()
    );
    LocalDateTime expectedDate = LocalDateTime.parse("2024-10-07 09:44:31.000",
        new DateTimeFormatterBuilder()
            .appendPattern("yyyy-MM-dd HH:mm:ss")
            .optionalStart()
            .appendLiteral(".")
            .appendFraction(java.time.temporal.ChronoField.NANO_OF_SECOND, 1, 6, false)
            .optionalEnd()
            .toFormatter());

    // Assuming printerRow is implemented to format dates
    csvExporter.printerRow(rowData, csvPrinter);

    verify(csvPrinter, times(1)).print(expectedDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")));
    verify(csvPrinter, times(1)).print("invalid_date");
    verify(csvPrinter, times(1)).println();
  }

  @Test
  void printerTitle_nonNullNonEmptyList_success() throws IOException {
    List<String> titles = Arrays.asList("Title1", "Title2", "Title3");
    CsvExporter.printerTitle(titles, csvPrinter);

    verify(csvPrinter, times(1)).printRecord("Title1");
    verify(csvPrinter, times(1)).printRecord("Title2");
    verify(csvPrinter, times(1)).printRecord("Title3");
    verify(csvPrinter, times(1)).println();
  }

  @Test
  void printerTitle_nullList_success() throws IOException {
    CsvExporter.printerTitle(null, csvPrinter);

    verify(csvPrinter, never()).printRecord(anyString());
    verify(csvPrinter, never()).println();
  }

  @Test
  void printerTitle_emptyList_success() throws IOException {
    List<String> titles = Collections.emptyList();
    CsvExporter.printerTitle(titles, csvPrinter);

    verify(csvPrinter, never()).printRecord(anyString());
    verify(csvPrinter, never()).println();
  }
}