package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseSoftEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.CustomObjectTypeEnum;

@Data
@Entity
@Table(name = TableName.CUSTOM_OBJECT)
@EqualsAndHashCode(callSuper = true)
public class CustomObjectEntity extends BaseSoftEntity<Long> {
  @Id
  @Column(name = "ID")
  @SequenceGenerator(name = "CUSTOM_OBJECT_SEQ", sequenceName = "CUSTOM_OBJECT_SEQ", allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "CUSTOM_OBJECT_SEQ")
  private Long id;

  @Column(name = "NAME", nullable = false)
  private String name;

  @Column(name = "DESCRIPTION")
  private String description;

  @Column(name = "TYPE", nullable = false)
  @Enumerated(EnumType.STRING)
  private CustomObjectTypeEnum type;

  @Column(name = "REGEX")
  private String regex;

  @Column(name = "FROM_INDEX")
  private Integer fromIndex;

  @Column(name = "TO_INDEX")
  private Integer toIndex;

  @Column(name = "FROM_KEYWORD")
  private String fromKeyword;

  @Column(name = "TO_KEYWORD")
  private String toKeyword;

  @Override
  public Long getId() {
    return id;
  }
}