package vn.com.mbbank.kanban.mbmonitor.common.dtos.responses;


import jakarta.persistence.Transient;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.SysRoleWithPermissionsModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysUserEntity;


/**
 * SysUser response.
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SysUserResponse extends SysUserEntity {
  List<SysRoleWithPermissionsModel> roles;
  @Transient
  private Boolean isUserLocal;

  /**
   * check password.
   *
   * @return userLocal
   */
  public Boolean getIsUserLocal() {
    return getPassword() != null;
  }

}
