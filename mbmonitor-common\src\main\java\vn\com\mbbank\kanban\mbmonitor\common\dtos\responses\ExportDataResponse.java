package vn.com.mbbank.kanban.mbmonitor.common.dtos.responses;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExportFileStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExportFileTypeEnum;

/**
 * Response DTO for representing export data configurations.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ExportDataResponse {
  String id;
  String fileName;
  ExportFileTypeEnum extension;
  ExportFileStatusEnum status;
  String exportedBy;
  String createdDate;
}
