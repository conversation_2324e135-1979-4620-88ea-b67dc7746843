package vn.com.mbbank.kanban.mbmonitor.common.services.impl;

import static vn.com.mbbank.kanban.mbmonitor.common.constants.AlertConstants.NO_CONTACT;
import static vn.com.mbbank.kanban.mbmonitor.common.constants.AlertPriorityConfigConstants.DEFAULT_ALERT_PRIORITY_CONFIG_NAME;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.constants.AlertConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.AlertBaseModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertSourceTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonApplicationService;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonRawAlertService;
import vn.com.mbbank.kanban.mbmonitor.common.services.CommonServiceService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/19/2025
 */
@Service
@Lazy
public class CommonRawAlertServiceImpl implements CommonRawAlertService {
  @Autowired
  private CommonServiceService commonServiceService;

  @Autowired
  private CommonApplicationService commonApplicationService;

  @Autowired
  private CommonAlertPriorityConfigService commonAlertPriorityConfigService;


  @Override
  public List<AlertBaseModel> createRawData(List<AlertBaseModel> alerts,
                                            AlertSourceTypeEnum source) {
    List<String> serviceIds = new ArrayList<>();
    List<String> applicationIds = new ArrayList<>();
    List<Long> priorityIds = new ArrayList<>();
    List<String> listServiceNamesRaw = new ArrayList<>();
    List<String> listApplicationNamesRaw = new ArrayList<>();
    for (var alert : alerts) {
      if (!KanbanCommonUtil.isEmpty(alert.getServiceId())) {
        serviceIds.add(alert.getServiceId());
      }

      if (!KanbanCommonUtil.isEmpty(alert.getApplicationId())) {
        applicationIds.add(alert.getApplicationId());
      }

      if (!KanbanCommonUtil.isEmpty(alert.getAlertPriorityConfigId())) {
        priorityIds.add(alert.getAlertPriorityConfigId());
      }

      if (!KanbanCommonUtil.isEmpty(alert.getServiceNameRaw())) {
        listServiceNamesRaw.add(alert.getServiceNameRaw().toLowerCase());
      }
      if (!KanbanCommonUtil.isEmpty(alert.getApplicationNameRaw())) {
        listApplicationNamesRaw.add(alert.getApplicationNameRaw().toLowerCase());
      }
    }

    var priorities = Optional.ofNullable(commonAlertPriorityConfigService.findAllById(priorityIds))
        .orElse(new ArrayList<>());
    var services = commonServiceService.findAllByNameIgnoreCaseInOrServiceIdIn(listServiceNamesRaw,
        serviceIds);

    var serviceFromDb = services.stream().map(ServiceEntity::getId).toList();
    var applications =
        commonApplicationService.findAllByNameIgnoreCaseInOrApplicationIdAndServiceIdAndDeletedFalse(
            listApplicationNamesRaw,
            applicationIds, serviceFromDb);

    List<AlertBaseModel> newAlerts = new ArrayList<>();
    for (AlertBaseModel alert : alerts) {
      newAlerts.add(
          createRawData(alert, source, services, applications, priorities));
    }
    return newAlerts;
  }

  @Override
  public List<AlertBaseModel> convertIdToRawData(List<AlertBaseModel> alerts) {
    if (KanbanCommonUtil.isEmpty(alerts)) {
      return alerts;
    }
    var priorityIds = alerts.stream().map(AlertBaseModel::getAlertPriorityConfigId).toList();
    var priorities = Optional.ofNullable(commonAlertPriorityConfigService.findAllById(priorityIds))
        .orElse(new ArrayList<>());
    List<AlertBaseModel> newAlerts = new ArrayList<>();
    for (var alert : alerts) {
      var priorityName = priorities.stream()
          .filter(obj -> Objects.equals(obj.getId(), alert.getAlertPriorityConfigId())).findFirst()
          .orElse(new AlertPriorityConfigEntity()).getName();
      alert.setPriorityRaw(priorityName);
      alert.setRecipientRaw(alert.getRecipient());
      alert.setContentRaw(alert.getContent());
      newAlerts.add(alert);
    }
    return newAlerts;
  }

  private AlertBaseModel createRawData(AlertBaseModel alert, AlertSourceTypeEnum source,
                                       List<ServiceEntity> services,
                                       List<ApplicationEntity> applications,
                                       List<AlertPriorityConfigEntity> priorities) {
    alert.setSource(source);
    alert.setStatus(AlertStatusEnum.NEW);
    if (!KanbanCommonUtil.isEmpty(alert.getServiceId())) {
      var service =
          services.stream().filter(obj -> Objects.equals(obj.getId(), alert.getServiceId()))
              .findFirst().orElse(null);
      if (!KanbanCommonUtil.isEmpty(service)) {
        alert.setServiceNameRaw(service.getName());
      } else {
        alert.setIsValid(false);
        alert.setDescription(ErrorCode.SERVICE_NOT_FOUND.getMessage());
        return alert;
      }
    } else {
      var service =
          services.stream()
              .filter(obj -> obj.getName().equalsIgnoreCase(alert.getServiceNameRaw())).findFirst()
              .orElse(null);
      if (!KanbanCommonUtil.isEmpty(service)) {
        alert.setServiceId(service.getId());
      } else {
        alert.setIsValid(false);
        alert.setDescription(ErrorCode.SERVICE_NOT_FOUND.getMessage());
        return alert;
      }

    }
    if (!KanbanCommonUtil.isEmpty(alert.getApplicationId())) {
      var application =
          applications.stream().filter(
                  obj -> Objects.equals(obj.getId(), alert.getApplicationId())
                      &&
                      Objects.equals(obj.getServiceId(), alert.getServiceId()))
              .findFirst().orElse(null);
      if (!KanbanCommonUtil.isEmpty(application)) {
        alert.setApplicationNameRaw(application.getName());
      } else {
        alert.setIsValid(false);
        alert.setDescription(ErrorCode.APPLICATION_NOT_FOUND.getMessage());
        return alert;
      }
    } else {

      var application =
          applications.stream()
              .filter(obj -> Objects.equals(obj.getServiceId(), alert.getServiceId())
                  &&
                  obj.getName().equalsIgnoreCase(alert.getApplicationNameRaw()))
              .findFirst()
              .orElse(null);
      if (!KanbanCommonUtil.isEmpty(application)) {
        alert.setApplicationId(application.getId());
      } else {
        alert.setIsValid(false);
        alert.setDescription(ErrorCode.APPLICATION_NOT_FOUND.getMessage());
        return alert;
      }
    }
    if (!KanbanCommonUtil.isEmpty(alert.getAlertPriorityConfigId())) {
      var priority = priorities.stream()
          .filter(obj -> Objects.equals(obj.getId(), alert.getAlertPriorityConfigId())).findFirst()
          .orElse(null);
      alert.setPriorityRaw(DEFAULT_ALERT_PRIORITY_CONFIG_NAME);
      if (!KanbanCommonUtil.isEmpty(priority)) {
        alert.setPriorityRaw(priority.getName());
      } else {
        alert.setIsValid(false);
        alert.setDescription(ErrorCode.ALERT_PRIORITY_CONFIG_NOT_FOUND.getMessage());
        return alert;
      }
    }

    // build alert content
    if (!KanbanCommonUtil.isEmpty(alert.getContentRaw())) {
      var content = alert.getContentRaw().substring(0,
          Math.min(AlertConstants.CONTENT_MAX_LENGTH, alert.getContentRaw().length()));
      alert.setContent(content);
    } else {
      alert.setContentRaw(alert.getContent());
    }

    if (KanbanCommonUtil.isEmpty(alert.getContentRaw())
        &&
        KanbanCommonUtil.isEmpty(alert.getContent())) {
      alert.setIsValid(false);
      alert.setDescription(ErrorCode.ALERT_CONTENT_EMPTY.getMessage());
      return alert;
    }
    // build contact
    if (!KanbanCommonUtil.isEmpty(alert.getRecipientRaw())) {
      var contact = alert.getRecipientRaw().substring(0,
          Math.min(AlertConstants.CONTACT_MAX_LENGTH, alert.getRecipientRaw().length()));
      alert.setRecipient(contact);
    }
    if (KanbanCommonUtil.isEmpty(alert.getRecipientRaw())
        &&
        KanbanCommonUtil.isEmpty(alert.getRecipient())) {
      alert.setRecipient(NO_CONTACT);
    }
    if (KanbanCommonUtil.isEmpty(alert.getRecipientRaw())) {
      alert.setRecipientRaw(alert.getRecipient());
    }
    return alert;
  }
}
