package vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds;

import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SysUserResponse;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;

/**
 * For Server Integration.
 */
@Component
public class ServerIntegration extends BaseIntegration {
  /**
   * Get user info.
   *
   * @return User info
   */
  public ResponseData<SysUserResponse> getMe() {
    var response =
        restTemplate.exchange(ServerUrl.USERS_URL_SERVICE.path + "/me", HttpMethod.GET, null,
            new ParameterizedTypeReference<ResponseData<SysUserResponse>>() {
            });

    return response.getBody();
  }
}
