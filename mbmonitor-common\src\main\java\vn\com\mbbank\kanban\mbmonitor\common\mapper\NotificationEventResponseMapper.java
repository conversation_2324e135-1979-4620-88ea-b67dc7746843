package vn.com.mbbank.kanban.mbmonitor.common.mapper;

import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.NotificationEventResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NotificationEventEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NotificationEventTargetEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationEventTargetTypeEnum;

/**
 * Mapper for NotificationEventResponse.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface NotificationEventResponseMapper extends
    KanbanBaseMapper<NotificationEventResponse, NotificationEventEntity> {
  NotificationEventResponseMapper INSTANCE = Mappers.getMapper(NotificationEventResponseMapper.class);

  /**
   * Map from NotificationEventEntity to NotificationEventResponse.
   *
   * @param entity  NotificationEventEntity
   * @param targets list of NotificationEventTargetEntity
   * @return NotificationEventResponse
   */
  default NotificationEventResponse map(NotificationEventEntity entity,
                                        List<NotificationEventTargetEntity> targets) {
    var response = this.map(entity);
    for (NotificationEventTargetEntity target : targets) {
      if (NotificationEventTargetTypeEnum.USER.equals(target.getType())) {
        response.getUserNames().add(target.getTarget());
      } else if (NotificationEventTargetTypeEnum.ROLE.equals(target.getType())) {
        response.getRoleIds().add(Long.parseLong(target.getTarget()));
      }
    }
    return response;
  }
}