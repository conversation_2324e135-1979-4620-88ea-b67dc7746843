package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.NotificationEventTargetTypeEnum;

/**
 * Entity for NotificationEventTargetEntity table.
 */
@Data
@Entity
@Table(name = TableName.NOTIFICATION_EVENT_TARGET)
@EqualsAndHashCode(callSuper = true)
public class NotificationEventTargetEntity extends BaseEntity<String> {
  @Id
  @Column(name = "ID")
  private String id;

  @Column(name = "TARGET")
  private String target;

  @Column(name = "TYPE")
  @Enumerated(EnumType.STRING)
  private NotificationEventTargetTypeEnum type;

  @Column(name = "NOTIFICATION_EVENT_ID")
  private String notificationEventId;
}