package vn.com.mbbank.kanban.mbmonitor.common.utils;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.math.NumberUtils;
import vn.com.mbbank.kanban.core.utils.KanbanApplicationConfigUtils;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 5/14/2025
 */
public class BatchUtils {
  /**
   * Process a list of items in batches and apply a function to each batch.
   *
   * @param items          The list of items to process
   * @param batchSize      The maximum size of each batch
   * @param batchOperation The function to apply to each batch
   * @param <T>            The type of the items in the list
   * @param <R>            The return type of the batch operation
   * @return The aggregated result of applying the function to all batches
   */
  public static <T, R extends Number> R processBatchesWithNumberResult(
      List<T> items,
      int batchSize,
      Function<List<T>, R> batchOperation) {
    Number result = 0;

    if (items == null || items.isEmpty()) {
      R typedResult = (R) result;
      return typedResult;
    }

    for (int i = 0; i < items.size(); i += batchSize) {
      List<T> batch = items.subList(i, Math.min(i + batchSize, items.size()));
      Number batchResult = batchOperation.apply(batch);
      result = add(result, batchResult);
    }

    R typedResult = (R) result;
    return typedResult;
  }

  /**
   * Process a list of items in batches and apply a function to each batch.
   *
   * @param items          The list of items to process
   * @param batchOperation The function to apply to each batch
   * @param <T>            The type of the items in the list
   * @param <R>            The return type of the batch operation
   * @return The aggregated result of applying the function to all batches
   */
  public static <T, R extends Number> R processBatchesWithNumberResult(
      List<T> items,
      Function<List<T>, R> batchOperation) {
    String batchSizeConfig =
        KanbanApplicationConfigUtils.getProperty("spring.jpa.properties.hibernate.jdbc.batch_size",
            "500");
    var batchSize = NumberUtils.toInt(batchSizeConfig, 500);
    return processBatchesWithNumberResult(items, batchSize, batchOperation);
  }

  /**
   * Process a list of items in batches and apply a function that returns a collection to each batch.
   *
   * @param items          The list of items to process
   * @param batchOperation The function to apply to each batch
   * @param <T>            The type of the items in the input list
   * @param <R>            The type of the items in the result list
   * @return A consolidated list containing all results from all batches
   */
  public static <T, R> List<R> processBatchesWithListResult(
      List<T> items,
      Function<List<T>, List<R>> batchOperation) {
    String batchSizeConfig =
        KanbanApplicationConfigUtils.getProperty("spring.jpa.properties.hibernate.jdbc.batch_size",
            "500");
    var batchSize = NumberUtils.toInt(batchSizeConfig, 500);
    return processBatchesWithListResult(items, batchSize, batchOperation);
  }

  /**
   * Process a list of items in batches and apply a function that returns a collection to each batch.
   *
   * @param items          The list of items to process
   * @param batchSize      The maximum size of each batch
   * @param batchOperation The function to apply to each batch
   * @param <T>            The type of the items in the input list
   * @param <R>            The type of the items in the result list
   * @return A consolidated list containing all results from all batches
   */
  public static <T, R> List<R> processBatchesWithListResult(
      List<T> items,
      int batchSize,
      Function<List<T>, List<R>> batchOperation) {

    if (items == null || items.isEmpty()) {
      return List.of();
    }

    return items.stream()
        .collect(Collectors.groupingBy(item -> items.indexOf(item) / batchSize))
        .values().stream()
        .flatMap(batch -> batchOperation.apply(batch).stream())
        .collect(Collectors.toList());
  }


  private static Number add(Number a, Number b) {
    if (a instanceof Integer || b instanceof Integer) {
      return a.intValue() + b.intValue();
    } else if (a instanceof Long || b instanceof Long) {
      return a.longValue() + b.longValue();
    } else if (a instanceof Double || b instanceof Double) {
      return a.doubleValue() + b.doubleValue();
    } else if (a instanceof Float || b instanceof Float) {
      return a.floatValue() + b.floatValue();
    } else {
      return a.intValue() + b.intValue(); // Default to int
    }
  }
}
