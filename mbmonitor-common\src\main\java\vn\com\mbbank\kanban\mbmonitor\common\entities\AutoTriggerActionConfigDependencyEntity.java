package vn.com.mbbank.kanban.mbmonitor.common.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.com.mbbank.kanban.core.entities.core.BaseEntity;
import vn.com.mbbank.kanban.mbmonitor.common.constants.TableName;
import vn.com.mbbank.kanban.mbmonitor.common.enums.DependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;

@Entity
@Data
@Table(name = TableName.AUTO_TRIGGER_ACTION_CONFIG_DEPENDENCY)
@EqualsAndHashCode(callSuper = true)
public class AutoTriggerActionConfigDependencyEntity extends BaseEntity<String> {
  @Id
  @Column(name = "ID")
  private String id = GeneratorUtil.generateId();

  @Column(name = "AUTO_TRIGGER_ACTION_CONFIG_ID")
  private String autoTriggerActionConfigId;

  @Column(name = "TYPE")
  @Enumerated(EnumType.STRING)
  private DependencyTypeEnum type;

  @Column(name = "DEPENDENCY_ID")
  private String dependencyId;

  @Override
  public String getId() {
    return id;
  }
}
