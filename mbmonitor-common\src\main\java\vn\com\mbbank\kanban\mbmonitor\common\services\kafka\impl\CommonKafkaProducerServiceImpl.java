package vn.com.mbbank.kanban.mbmonitor.common.services.kafka.impl;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.BaseKafkaModel;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.CommonKafkaProducerService;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/19/2025
 */
@Service
@RequiredArgsConstructor
public class CommonKafkaProducerServiceImpl implements CommonKafkaProducerService {
  private final KafkaTemplate<String, String> kafkaTemplate;
  Logger logger = LoggerFactory.getLogger(this.getClass());

  @Value("${kanban.kafka.topic.jobs}")
  private String topic;

  @Value("${kanban.kafka.producer.timeout}")
  private Long timeout;


  @Override
  public <T> void send(String topic, BaseKafkaModel<T> data) {
    send(topic, data);
  }

  @Override
  public <T> void send(String topic, String key, BaseKafkaModel<T> data) {
    kafkaTemplate.send(topic, key, KanbanCommonUtil.beanToString(data));
  }

  @Override
  public <T> void sendAndWait(BaseKafkaModel<T> data) throws BusinessException {
    if (KanbanCommonUtil.isEmpty(topic)) {
      logger.error("Kafka topic is null. Data: {}", KanbanCommonUtil.beanToString(data));
      throw new BusinessException(ErrorCode.KAFKA_SEND_FAILED);
    }
    sendAndWait(topic, data);
  }

  @Override
  public <T> void sendAndWait(String topic, BaseKafkaModel<T> data) throws BusinessException {
    sendAndWait(topic, null, data);
  }

  @Override
  public <T> void sendAndWait(String topic, String key, BaseKafkaModel<T> data) throws BusinessException {
    try {
      var message = KanbanCommonUtil.beanToString(data);
      if (key == null) {
        kafkaTemplate.send(topic, message).get(timeout, TimeUnit.MILLISECONDS);
      } else {
        kafkaTemplate.send(topic, key, message).get(timeout, TimeUnit.MILLISECONDS);
      }
      logger.info("Kafka message sent successfully to topic [{}]: {}", topic, message);
    } catch (InterruptedException | ExecutionException | TimeoutException e) {
      logger.error("Failed to send Kafka message synchronously: {}", e.getMessage(), e);
      throw new BusinessException(ErrorCode.KAFKA_SEND_FAILED);
    }
  }

  @Override
  public <T> void send(BaseKafkaModel<T> data) {
    if (KanbanCommonUtil.isEmpty(topic)) {
      logger.error("Kafka topic is null. Data: {}", KanbanCommonUtil.beanToString(data));
      return;
    }
    var result = kafkaTemplate.send(topic, KanbanCommonUtil.beanToString(data));
    logger.info("Kafka message send status = {}, Data: {}", result.isDone(), KanbanCommonUtil.beanToString(data));
  }

  @Override
  public <T> CompletableFuture<SendResult<String, String>> sendAsync(String topic, String key, BaseKafkaModel<T> data) {
    return kafkaTemplate.send(topic, key, KanbanCommonUtil.beanToString(data));
  }

}
