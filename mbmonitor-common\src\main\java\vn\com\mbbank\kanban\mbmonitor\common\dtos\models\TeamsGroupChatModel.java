package vn.com.mbbank.kanban.mbmonitor.common.dtos.models;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/23/2025
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class TeamsGroupChatModel {
  private String teamsConfigId;
  private String groupChatId;
  @JsonIgnore
  private String groupChatName;
  private List<String> emailInvalid = new ArrayList<>();
  private List<String> emailValid = new ArrayList<>();
  @JsonIgnore
  private String nextUrl;
}
