package vn.com.mbbank.kanban.mbmonitor.common.mapper.log;

import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.log.WebhookLogModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WebHookEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.WebHookConfigMapTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/3/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WebhookLogModelMapper extends
    KanbanBaseMapper<WebhookLogModel, WebHookEntity> {
  WebhookLogModelMapper INSTANCE = Mappers.getMapper(WebhookLogModelMapper.class);

  /**
   * map SysRoleLogModel.
   *
   * @param webHook     WebHookEntity.
   * @param service     service
   * @param application application
   * @param priority    priority
   * @return WebhookLogModel
   */
  default WebhookLogModel map(WebHookEntity webHook, ServiceEntity service, ApplicationEntity application,
                              AlertPriorityConfigEntity priority) {
    var res = WebhookLogModel.builder()
        .name(webHook.getName())
        .dataType(webHook.getDataType())
        .token(webHook.getToken())
        .priorityType(webHook.getPriorityType())
        .applicationType(webHook.getApplicationType())
        .serviceType(webHook.getServiceNameType())
        .contactType(webHook.getContactType())
        .contentType(webHook.getAlertContentType());

    if (WebHookConfigMapTypeEnum.CUSTOM.equals(webHook.getServiceNameType())) {
      res.customService(Objects.nonNull(service) ? service.getName() : null);
    } else {
      res.fromSourceService(webHook.getServiceMapValue());
    }
    if (WebHookConfigMapTypeEnum.CUSTOM.equals(webHook.getApplicationType())) {
      res.customApplication(Objects.nonNull(application) ? application.getName() : null);
    } else {
      res.fromSourceApplication(webHook.getApplicationMapValue());
    }
    if (WebHookConfigMapTypeEnum.CUSTOM.equals(webHook.getPriorityType())) {
      res.customPriority(Objects.nonNull(priority) ? priority.getName() : null);
    } else {
      res.fromSourcePriority(webHook.getPriorityMapValue());
    }
    if (WebHookConfigMapTypeEnum.CUSTOM.equals(webHook.getContactType())) {
      res.customContact(webHook.getContactCustomValue());
    } else {
      res.fromSourceContact(webHook.getContactCustomValue());
    }
    if (WebHookConfigMapTypeEnum.CUSTOM.equals(webHook.getAlertContentType())) {
      res.customContent(webHook.getAlertContentCustomValue());
    } else {
      res.fromSourceContent(webHook.getAlertContentMapValue());
    }
    return res.build();
  }
}
