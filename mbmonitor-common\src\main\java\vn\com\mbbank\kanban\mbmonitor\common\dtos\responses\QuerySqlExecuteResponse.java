package vn.com.mbbank.kanban.mbmonitor.common.dtos.responses;

import java.util.List;
import java.util.Map;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * Model request service to execute group query sql.
 */

@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class QuerySqlExecuteResponse {
  List<Map<String, Object>> result;
  Integer total;
  List<String> columns;
  boolean isNonQuery;

}
