package vn.com.mbbank.kanban.mbmonitor.common.services.systems;

import java.util.List;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.acl.AclPermissionModel;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 9/30/2024
 */
@Service
public interface CommonAclPermissionService {
  /**
   * Check permission.
   *
   * @param aclPermissionModels aclPermissions
   * @param isMapAll            isMapAll
   * @return true/false
   * @throws BusinessException ex
   */
  boolean isAnyPermission(List<AclPermissionModel> aclPermissionModels, boolean isMapAll)
      throws BusinessException;

  /**
   * Check permission.
   *
   * @param aclPermissionModels aclPermissions
   * @return true/false
   * @throws BusinessException ex
   */
  boolean isAnyPermission(List<AclPermissionModel> aclPermissionModels) throws BusinessException;

  /**
   * Check user is Supper admin.
   *
   * @return true/false
   */
  boolean isSupperAdmin() throws BusinessException;

  /**
   * Check user is Supper admin.
   *
   * @param userName userName
   * @return true/false
   */
  boolean isSupperAdmin(String userName) throws BusinessException;

}
