package vn.com.mbbank.kanban.mbmonitor.common.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.DatabaseConnectionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseConnectionEntity;


/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/7/2024
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DatabaseConnectionResponseToEntityMapper extends
    KanbanBaseMapper<DatabaseConnectionResponse, DatabaseConnectionEntity> {
  DatabaseConnectionResponseToEntityMapper INSTANCE =
      Mappers.getMapper(DatabaseConnectionResponseToEntityMapper.class);
}